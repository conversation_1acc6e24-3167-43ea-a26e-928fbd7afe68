[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "engineai_training"
version = "1.0.0"
description = "Isaac Gym environments for Legged Robots with Zenoh integration"
readme = "README.md"
license = {text = "BSD-3-Clause"}
authors = [
    {name = "EngineAI Team", email = "<EMAIL>"}
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: BSD License",
    "Operating System :: POSIX :: Linux",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]
requires-python = ">=3.8,<3.11"
dependencies = [
    "torch>=1.13.0,<2.0.0",
    "torchvision>=0.14.0,<1.0.0",
    "torchaudio>=0.13.0,<1.0.0",
    "numpy==1.23.*",
    "matplotlib>=3.5.0",
    "opencv-python>=4.5.0",
    "pygame>=2.1.0",
    "mujoco>=2.3.0",
    "mujoco-python-viewer>=1.0.0",
    "eclipse-zenoh>=0.10.0",
    "msgpack>=1.0.0",
    "websockets>=10.0",
    "tqdm>=4.64.0",
    "psutil>=5.9.0",
    "tensorboard>=2.10.0",
    "setuptools==59.5.0",
    "packaging>=21.0",
    "pyyaml>=6.0",
    "click>=8.0.0",
    "rich>=12.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "black>=22.0.0",
    "isort>=5.10.0",
    "flake8>=5.0.0",
    "mypy>=0.991",
    "pre-commit>=2.20.0",
]
monitoring = [
    "nvidia-ml-py3>=7.352.0",
    "prometheus-client>=0.14.0",
]
jupyter = [
    "jupyter>=1.0.0",
    "ipywidgets>=7.6.0",
]

[project.scripts]
engineai-train = "legged_gym.scripts.train:main"
engineai-play = "legged_gym.scripts.play:main"
engineai-demo = "demo_services:main"

[project.urls]
Homepage = "https://github.com/engineai-robotics/engineai_legged_gym"
Repository = "https://github.com/engineai-robotics/engineai_legged_gym"
Documentation = "https://github.com/engineai-robotics/engineai_legged_gym/docs"
"Bug Tracker" = "https://github.com/engineai-robotics/engineai_legged_gym/issues"

# Black configuration
[tool.black]
line-length = 100
target-version = ['py38', 'py39', 'py310']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | __pycache__
  | node_modules
)/
'''

# isort configuration
[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
skip_glob = ["__pycache__", "*.pyc", "venv/*", "build/*", "dist/*"]

# MyPy configuration
[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = false
disallow_incomplete_defs = false
check_untyped_defs = true
disallow_untyped_decorators = false
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = [
    "isaacgym.*",
    "rsl_rl.*",
    "zenoh.*",
    "mujoco.*",
    "pygame.*",
]
ignore_missing_imports = true

# Pytest configuration
[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers"
testpaths = [
    "tests",
    "legged_gym/tests",
    "zenoh_services/tests",
]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

# Coverage configuration
[tool.coverage.run]
source = ["legged_gym", "zenoh_services"]
omit = [
    "*/tests/*",
    "*/test_*.py",
    "*/__pycache__/*",
    "*/venv/*",
    "*/build/*",
    "*/dist/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
