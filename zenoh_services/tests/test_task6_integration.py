"""
Integration tests for the complete Task 6 services workflow
Tests the integration of simulation and deployment services
"""

import asyncio
import pytest
import tempfile
import os
import json
import time
from unittest.mock import Mock, AsyncMock, patch
from pathlib import Path

from zenoh_services.services.simulation_service import create_simulation_service, SimulationServiceConfig
from zenoh_services.services.deployment_service import create_deployment_service, DeploymentServiceConfig
from zenoh_services.core.data_models import (
    TerrainConfig, PhysicsConfig, RobotConfig, SimulationConfig,
    DeploymentConfig, ConfigurationRequest
)
from zenoh_services.core.enhanced_session_manager import EnhancedZenohSessionManager


class TestTask6Integration:
    """Integration tests for Task 6 - Simulation and Deployment Services"""
    
    @pytest.fixture
    async def mock_session_manager(self):
        """Create a mock session manager for integration tests"""
        manager = Mock(spec=EnhancedZenohSessionManager)
        manager.initialize = AsyncMock(return_value=True)
        manager.shutdown = AsyncMock()
        return manager
    
    @pytest.fixture
    def temp_workspace(self):
        """Create a temporary workspace for testing"""
        with tempfile.TemporaryDirectory() as temp_dir:
            workspace = {
                "base_dir": temp_dir,
                "sim_configs": os.path.join(temp_dir, "simulation_configs"),
                "sim_backups": os.path.join(temp_dir, "simulation_backups"),
                "deploy_exports": os.path.join(temp_dir, "deployed_models"),
                "deploy_backups": os.path.join(temp_dir, "model_backups"),
                "test_models": os.path.join(temp_dir, "test_models")
            }
            
            # Create directories
            for directory in workspace.values():
                if directory != workspace["base_dir"]:
                    os.makedirs(directory, exist_ok=True)
            
            yield workspace
    
    @pytest.fixture
    def mock_model_file(self, temp_workspace):
        """Create a mock PyTorch model file"""
        model_path = os.path.join(temp_workspace["test_models"], "test_model.pt")
        
        # Create a simple mock model file
        model_data = {
            "model_state_dict": {
                "policy.actor.0.weight": [[0.1, 0.2, 0.3]] * 48,
                "policy.actor.0.bias": [0.0] * 128,
                "policy.critic.0.weight": [[0.05, 0.15, 0.25]] * 48,
                "policy.critic.0.bias": [0.0] * 128
            },
            "optimizer_state_dict": {},
            "epoch": 1500,
            "training_config": {
                "task_name": "anymal_c_flat",
                "num_envs": 4096,
                "max_iterations": 1500
            }
        }
        
        # Write mock model data
        import pickle
        with open(model_path, 'wb') as f:
            pickle.dump(model_data, f)
        
        return model_path
    
    @pytest.mark.asyncio
    async def test_complete_service_workflow(self, mock_session_manager, temp_workspace, mock_model_file):
        """Test complete workflow: simulation configuration → model deployment"""
        
        # Create service configurations
        sim_config = SimulationServiceConfig(
            config_storage_path=temp_workspace["sim_configs"],
            backup_path=temp_workspace["sim_backups"],
            validation_enabled=True,
            backup_enabled=True
        )
        
        deploy_config = DeploymentServiceConfig(
            export_base_path=temp_workspace["deploy_exports"],
            backup_path=temp_workspace["deploy_backups"],
            validation_enabled=True,
            backup_original=True,
            auto_versioning=True
        )
        
        # Mock topic managers for both services
        with patch('zenoh_services.services.simulation_service.TopicManager'), \
             patch('zenoh_services.services.deployment_service.TopicManager'):
            
            # Create services using factory functions
            sim_service = await create_simulation_service(mock_session_manager, sim_config)
            deploy_service = await create_deployment_service(mock_session_manager, deploy_config)
            
            # Mock topic managers after creation
            for service in [sim_service, deploy_service]:
                service.topic_manager = Mock()
                service.topic_manager.register_topic = AsyncMock()
                service.topic_manager.create_publisher = AsyncMock()
                service.topic_manager.create_subscriber = AsyncMock()
                service.topic_manager.publish_message = AsyncMock()
            
            try:
                # Step 1: Configure simulation environment
                print("Step 1: Configuring simulation environment...")
                
                # Create optimized terrain configuration for training
                training_terrain = TerrainConfig(
                    mesh_type="plane",
                    terrain_length=8.0,
                    terrain_width=8.0,
                    horizontal_scale=0.1,
                    vertical_scale=0.005,
                    border_size=0.25
                )
                
                # Create physics configuration for stable training
                training_physics = PhysicsConfig(
                    dt=0.005,
                    gravity=[0.0, 0.0, -9.81],
                    solver_type="PGS",
                    num_position_iterations=4,
                    num_velocity_iterations=1,
                    contact_offset=0.02
                )
                
                # Create robot configuration for ANYmal C
                training_robot = RobotConfig(
                    name="anymal_c",
                    base_mass=35.0,
                    joint_stiffness=80.0,
                    joint_damping=2.0,
                    contact_friction=1.0,
                    control_frequency=50.0
                )
                
                # Update simulation configurations
                terrain_result = await sim_service.update_terrain_config(training_terrain)
                physics_result = await sim_service.update_physics_config(training_physics)
                robot_result = await sim_service.update_robot_config(training_robot)
                
                assert terrain_result is True
                assert physics_result is True
                assert robot_result is True
                
                # Verify combined simulation config was updated
                sim_config_dict = sim_service.current_simulation_config.to_dict()
                assert sim_config_dict["terrain"]["mesh_type"] == "plane"
                assert sim_config_dict["physics"]["solver_type"] == "PGS"
                assert sim_config_dict["robot"]["name"] == "anymal_c"
                
                print("✅ Simulation environment configured successfully")
                
                # Step 2: Get simulation status and validate configuration
                print("Step 2: Validating simulation configuration...")
                
                sim_status = await sim_service.get_status()
                assert sim_status["is_initialized"] is True
                assert len(sim_status["current_configs"]) == 4  # simulation, terrain, physics, robot
                
                # Check configuration history
                history = await sim_service.get_configuration_history()
                assert len(history) >= 3  # At least 3 configuration updates
                
                print("✅ Simulation configuration validated")
                
                # Step 3: Prepare model for deployment
                print("Step 3: Preparing model for deployment...")
                
                # Create deployment configuration for JIT export
                jit_deployment = DeploymentConfig(
                    model_path=mock_model_file,
                    export_format="jit",
                    target_platform="cpu",
                    optimization_level="default"
                )
                
                # Validate deployment configuration
                assert jit_deployment.validate() is True
                
                # Get initial deployment service status
                deploy_status = await deploy_service.get_status()
                assert deploy_status["is_initialized"] is True
                initial_deployed_count = deploy_status["deployed_models_count"]
                
                print("✅ Deployment configuration prepared")
                
                # Step 4: Execute model export
                print("Step 4: Executing model export...")
                
                # Mock the export execution to avoid PyTorch dependencies
                with patch.object(deploy_service, '_execute_export_job') as mock_export:
                    # Configure mock to simulate successful export
                    async def mock_export_execution(job_id, config):
                        job_status = deploy_service.active_jobs[job_id]
                        job_status.state = deploy_service.DeploymentServiceState.COMPLETED
                        job_status.progress = 1.0
                        job_status.target_path = "/mock/path/exported_model.pt"
                        deploy_service._move_job_to_history(job_id)
                        
                        # Mock model registration
                        from zenoh_services.core.data_models import ModelInfo
                        model_info = ModelInfo(
                            model_path=job_status.target_path,
                            model_name="exported_test_model",
                            training_config=None,
                            creation_time=time.time(),
                            file_size=1024 * 1024,  # 1MB
                            model_format="jit"
                        )
                        deploy_service.deployed_models["exported_test_model"] = model_info
                    
                    mock_export.side_effect = mock_export_execution
                    
                    # Start export job
                    job_id = await deploy_service.export_model(jit_deployment)
                    
                    # Wait for mock execution
                    await asyncio.sleep(0.1)
                    
                    # Verify job was created and executed
                    assert job_id is not None
                    assert job_id not in deploy_service.active_jobs  # Should be in history
                    assert len(deploy_service.job_history) > 0
                    
                    # Find completed job in history
                    completed_job = next(
                        (job for job in deploy_service.job_history if job.job_id == job_id), 
                        None
                    )
                    assert completed_job is not None
                    assert completed_job.state.value == "completed"
                    assert completed_job.progress == 1.0
                
                print("✅ Model export completed successfully")
                
                # Step 5: Validate deployed model
                print("Step 5: Validating deployed model...")
                
                # Check deployment service status after export
                final_deploy_status = await deploy_service.get_status()
                assert final_deploy_status["deployed_models_count"] > initial_deployed_count
                
                # Get model list
                model_list = await deploy_service.get_model_list()
                assert len(model_list["deployed"]) > 0
                
                deployed_model = model_list["deployed"][0]
                assert deployed_model.model_format == "jit"
                assert deployed_model.model_name == "exported_test_model"
                
                print("✅ Deployed model validated")
                
                # Step 6: Test service coordination and communication
                print("Step 6: Testing service coordination...")
                
                # Test that both services can operate concurrently
                terrain_update_task = asyncio.create_task(
                    sim_service.update_terrain_config(TerrainConfig(mesh_type="trimesh"))
                )
                
                # Create another deployment config for ONNX export
                onnx_deployment = DeploymentConfig(
                    model_path=mock_model_file,
                    export_format="onnx",
                    target_platform="cpu"
                )
                
                with patch.object(deploy_service, '_execute_export_job') as mock_onnx_export:
                    mock_onnx_export.side_effect = mock_export_execution
                    
                    deployment_task = asyncio.create_task(
                        deploy_service.export_model(onnx_deployment)
                    )
                    
                    # Wait for both operations
                    terrain_result, onnx_job_id = await asyncio.gather(
                        terrain_update_task, 
                        deployment_task
                    )
                    
                    assert terrain_result is True
                    assert onnx_job_id is not None
                
                print("✅ Service coordination validated")
                
                # Step 7: Final status verification
                print("Step 7: Final status verification...")
                
                # Get final status from both services
                final_sim_status = await sim_service.get_status()
                final_deploy_status = await deploy_service.get_status()
                
                # Simulation service should have updated terrain
                assert final_sim_status["current_configs"]["terrain"]["mesh_type"] == "trimesh"
                
                # Deployment service should have multiple jobs in history
                assert len(final_deploy_status["active_jobs"]) == 0  # No active jobs
                assert len(deploy_service.job_history) >= 2  # At least 2 completed jobs
                
                print("✅ Final status verification complete")
                
                print("\n🎉 Complete Task 6 workflow test PASSED!")
                print(f"   - Simulation configurations: {len(final_sim_status['current_configs'])}")
                print(f"   - Configuration updates: {len(await sim_service.get_configuration_history())}")
                print(f"   - Deployed models: {final_deploy_status['deployed_models_count']}")
                print(f"   - Export jobs completed: {len(deploy_service.job_history)}")
                
            finally:
                # Clean up services
                await asyncio.gather(
                    sim_service.shutdown(),
                    deploy_service.shutdown()
                )
    
    @pytest.mark.asyncio
    async def test_error_handling_and_recovery(self, mock_session_manager, temp_workspace):
        """Test error handling and recovery in service operations"""
        
        # Create service configurations
        sim_config = SimulationServiceConfig(
            config_storage_path=temp_workspace["sim_configs"],
            backup_path=temp_workspace["sim_backups"],
            validation_enabled=True
        )
        
        deploy_config = DeploymentServiceConfig(
            export_base_path=temp_workspace["deploy_exports"],
            backup_path=temp_workspace["deploy_backups"],
            validation_enabled=True
        )
        
        with patch('zenoh_services.services.simulation_service.TopicManager'), \
             patch('zenoh_services.services.deployment_service.TopicManager'):
            
            sim_service = await create_simulation_service(mock_session_manager, sim_config)
            deploy_service = await create_deployment_service(mock_session_manager, deploy_config)
            
            # Mock topic managers
            for service in [sim_service, deploy_service]:
                service.topic_manager = Mock()
                service.topic_manager.register_topic = AsyncMock()
                service.topic_manager.create_publisher = AsyncMock()
                service.topic_manager.create_subscriber = AsyncMock()
                service.topic_manager.publish_message = AsyncMock()
            
            try:
                # Test 1: Invalid simulation configuration
                invalid_terrain = TerrainConfig(
                    mesh_type="invalid_type",
                    terrain_length=-5.0,  # Invalid negative length
                    terrain_width=0.0     # Invalid zero width
                )
                
                result = await sim_service.update_terrain_config(invalid_terrain)
                assert result is False  # Should fail validation
                
                # Service should still be operational
                sim_status = await sim_service.get_status()
                assert sim_status["is_initialized"] is True
                
                # Test 2: Invalid deployment configuration
                invalid_deployment = DeploymentConfig(
                    model_path="",  # Empty path
                    export_format="unsupported_format"
                )
                
                assert invalid_deployment.validate() is False
                
                # Test 3: Deployment with non-existent model file
                nonexistent_deployment = DeploymentConfig(
                    model_path="/nonexistent/path/model.pt",
                    export_format="jit"
                )
                
                with patch.object(deploy_service, '_execute_export_job') as mock_export:
                    async def failing_export(job_id, config):
                        job_status = deploy_service.active_jobs[job_id]
                        job_status.state = deploy_service.DeploymentServiceState.ERROR
                        job_status.error_message = "Model file not found"
                        deploy_service._move_job_to_history(job_id)
                    
                    mock_export.side_effect = failing_export
                    
                    job_id = await deploy_service.export_model(nonexistent_deployment)
                    await asyncio.sleep(0.1)  # Wait for mock execution
                    
                    # Job should be in history with error state
                    failed_job = next(
                        (job for job in deploy_service.job_history if job.job_id == job_id), 
                        None
                    )
                    assert failed_job is not None
                    assert failed_job.state.value == "error"
                    assert "not found" in failed_job.error_message.lower()
                
                # Service should still be operational after errors
                deploy_status = await deploy_service.get_status()
                assert deploy_status["is_initialized"] is True
                
                print("✅ Error handling and recovery tests passed")
                
            finally:
                await asyncio.gather(
                    sim_service.shutdown(),
                    deploy_service.shutdown()
                )
    
    @pytest.mark.asyncio
    async def test_service_performance_and_concurrency(self, mock_session_manager, temp_workspace):
        """Test service performance under concurrent load"""
        
        sim_config = SimulationServiceConfig(
            config_storage_path=temp_workspace["sim_configs"],
            backup_path=temp_workspace["sim_backups"]
        )
        
        deploy_config = DeploymentServiceConfig(
            export_base_path=temp_workspace["deploy_exports"],
            backup_path=temp_workspace["deploy_backups"],
            max_concurrent_exports=3
        )
        
        with patch('zenoh_services.services.simulation_service.TopicManager'), \
             patch('zenoh_services.services.deployment_service.TopicManager'):
            
            sim_service = await create_simulation_service(mock_session_manager, sim_config)
            deploy_service = await create_deployment_service(mock_session_manager, deploy_config)
            
            # Mock topic managers
            for service in [sim_service, deploy_service]:
                service.topic_manager = Mock()
                service.topic_manager.register_topic = AsyncMock()
                service.topic_manager.create_publisher = AsyncMock()
                service.topic_manager.create_subscriber = AsyncMock()
                service.topic_manager.publish_message = AsyncMock()
            
            try:
                # Test concurrent simulation config updates
                config_update_tasks = []
                
                for i in range(5):
                    terrain_config = TerrainConfig(
                        mesh_type="plane" if i % 2 == 0 else "trimesh",
                        terrain_length=8.0 + i,
                        terrain_width=8.0 + i,
                        horizontal_scale=0.1 + i * 0.01
                    )
                    
                    task = asyncio.create_task(
                        sim_service.update_terrain_config(terrain_config)
                    )
                    config_update_tasks.append(task)
                
                # Execute all config updates concurrently
                start_time = time.time()
                results = await asyncio.gather(*config_update_tasks)
                update_duration = time.time() - start_time
                
                # All updates should succeed
                assert all(results)
                print(f"✅ 5 concurrent config updates completed in {update_duration:.2f}s")
                
                # Test concurrent deployment jobs
                mock_model_files = []
                for i in range(3):
                    model_path = os.path.join(temp_workspace["test_models"], f"model_{i}.pt")
                    os.makedirs(os.path.dirname(model_path), exist_ok=True)
                    with open(model_path, 'wb') as f:
                        f.write(b"mock model data")
                    mock_model_files.append(model_path)
                
                with patch.object(deploy_service, '_execute_export_job') as mock_export:
                    async def quick_export(job_id, config):
                        job_status = deploy_service.active_jobs[job_id]
                        job_status.state = deploy_service.DeploymentServiceState.COMPLETED
                        job_status.progress = 1.0
                        deploy_service._move_job_to_history(job_id)
                    
                    mock_export.side_effect = quick_export
                    
                    export_tasks = []
                    for i, model_path in enumerate(mock_model_files):
                        deployment_config = DeploymentConfig(
                            model_path=model_path,
                            export_format="jit" if i % 2 == 0 else "onnx"
                        )
                        
                        task = asyncio.create_task(
                            deploy_service.export_model(deployment_config)
                        )
                        export_tasks.append(task)
                    
                    start_time = time.time()
                    job_ids = await asyncio.gather(*export_tasks)
                    await asyncio.sleep(0.1)  # Wait for mock execution
                    export_duration = time.time() - start_time
                    
                    # All exports should succeed
                    assert all(job_id is not None for job_id in job_ids)
                    assert len(deploy_service.job_history) >= len(job_ids)
                    
                    print(f"✅ {len(job_ids)} concurrent exports completed in {export_duration:.2f}s")
                
                # Verify service states remain healthy
                final_sim_status = await sim_service.get_status()
                final_deploy_status = await deploy_service.get_status()
                
                assert final_sim_status["is_initialized"] is True
                assert final_deploy_status["is_initialized"] is True
                
                print("✅ Performance and concurrency tests passed")
                
            finally:
                await asyncio.gather(
                    sim_service.shutdown(),
                    deploy_service.shutdown()
                )


if __name__ == "__main__":
    # Run integration tests
    pytest.main([__file__, "-v", "--tb=short", "-s"])