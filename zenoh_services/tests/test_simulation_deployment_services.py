"""
Unit tests for simulation and deployment services
"""

import asyncio
import pytest
import tempfile
import os
import json
import time
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from pathlib import Path

from zenoh_services.services.simulation_service import (
    SimulationService, SimulationServiceConfig, SimulationServiceState
)
from zenoh_services.services.deployment_service import (
    DeploymentService, DeploymentServiceConfig, DeploymentServiceState,
    ExportJobStatus, ModelValidationResult
)
from zenoh_services.core.data_models import (
    TerrainConfig, PhysicsConfig, RobotConfig, SimulationConfig,
    DeploymentConfig, ModelInfo, ConfigurationRequest
)
from zenoh_services.core.enhanced_session_manager import EnhancedZenohSessionManager
from zenoh_services.core.topic_manager import TopicManager


class TestSimulationService:
    """Test cases for SimulationService"""
    
    @pytest.fixture
    async def mock_session_manager(self):
        """Create a mock session manager"""
        manager = Mock(spec=EnhancedZenohSessionManager)
        manager.initialize = AsyncMock(return_value=True)
        manager.shutdown = AsyncMock()
        return manager
    
    @pytest.fixture
    def simulation_config(self):
        """Create a test simulation service config"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config = SimulationServiceConfig(
                config_storage_path=os.path.join(temp_dir, "configs"),
                backup_path=os.path.join(temp_dir, "backups"),
                validation_enabled=True,
                backup_enabled=True
            )
            yield config
    
    @pytest.fixture
    async def simulation_service(self, mock_session_manager, simulation_config):
        """Create a simulation service instance"""
        with patch('zenoh_services.services.simulation_service.TopicManager'):
            service = SimulationService(mock_session_manager, simulation_config)
            # Mock the topic manager
            service.topic_manager = Mock(spec=TopicManager)
            service.topic_manager.register_topic = AsyncMock()
            service.topic_manager.create_publisher = AsyncMock()
            service.topic_manager.create_subscriber = AsyncMock()
            service.topic_manager.publish_message = AsyncMock()
            
            yield service
    
    @pytest.mark.asyncio
    async def test_simulation_service_initialization(self, simulation_service):
        """Test simulation service initialization"""
        # Test successful initialization
        assert await simulation_service.initialize()
        assert simulation_service.is_initialized
        assert simulation_service.state == SimulationServiceState.IDLE
        
        # Check that default configurations are loaded
        assert simulation_service.current_terrain_config is not None
        assert simulation_service.current_physics_config is not None
        assert simulation_service.current_robot_config is not None
        assert simulation_service.current_simulation_config is not None
        
        # Check that storage directories are created
        assert os.path.exists(simulation_service.config.config_storage_path)
        assert os.path.exists(simulation_service.config.backup_path)
    
    @pytest.mark.asyncio
    async def test_terrain_config_update(self, simulation_service):
        """Test terrain configuration updates"""
        await simulation_service.initialize()
        
        # Create a new terrain config
        new_terrain_config = TerrainConfig(
            mesh_type="trimesh",
            terrain_length=12.0,
            terrain_width=12.0,
            horizontal_scale=0.2,
            vertical_scale=0.01
        )
        
        # Test successful update
        result = await simulation_service.update_terrain_config(new_terrain_config)
        assert result is True
        
        # Verify the configuration was updated
        assert simulation_service.current_terrain_config.mesh_type == "trimesh"
        assert simulation_service.current_terrain_config.terrain_length == 12.0
        assert simulation_service.current_terrain_config.horizontal_scale == 0.2
        
        # Verify the combined simulation config was updated
        assert simulation_service.current_simulation_config.terrain.mesh_type == "trimesh"
        
        # Verify topic publication was called
        simulation_service.topic_manager.publish_message.assert_called()
    
    @pytest.mark.asyncio
    async def test_physics_config_update(self, simulation_service):
        """Test physics configuration updates"""
        await simulation_service.initialize()
        
        # Create a new physics config
        new_physics_config = PhysicsConfig(
            dt=0.002,
            gravity=[0.0, 0.0, -10.0],
            solver_type="TGS",
            num_position_iterations=8,
            contact_offset=0.03
        )
        
        # Test successful update
        result = await simulation_service.update_physics_config(new_physics_config)
        assert result is True
        
        # Verify the configuration was updated
        assert simulation_service.current_physics_config.dt == 0.002
        assert simulation_service.current_physics_config.gravity == [0.0, 0.0, -10.0]
        assert simulation_service.current_physics_config.solver_type == "TGS"
        
        # Verify topic publication was called
        simulation_service.topic_manager.publish_message.assert_called()
    
    @pytest.mark.asyncio
    async def test_robot_config_update(self, simulation_service):
        """Test robot configuration updates"""
        await simulation_service.initialize()
        
        # Create a new robot config
        new_robot_config = RobotConfig(
            name="spot",
            base_mass=40.0,
            joint_stiffness=100.0,
            joint_damping=3.0,
            control_frequency=100.0
        )
        
        # Test successful update
        result = await simulation_service.update_robot_config(new_robot_config)
        assert result is True
        
        # Verify the configuration was updated
        assert simulation_service.current_robot_config.name == "spot"
        assert simulation_service.current_robot_config.base_mass == 40.0
        assert simulation_service.current_robot_config.joint_stiffness == 100.0
        
        # Verify topic publication was called
        simulation_service.topic_manager.publish_message.assert_called()
    
    @pytest.mark.asyncio
    async def test_invalid_config_validation(self, simulation_service):
        """Test validation of invalid configurations"""
        await simulation_service.initialize()
        
        # Test invalid terrain config
        invalid_terrain = TerrainConfig(
            mesh_type="invalid_type",  # Invalid type
            terrain_length=-1.0,       # Negative length
            terrain_width=0.0          # Zero width
        )
        
        result = await simulation_service.update_terrain_config(invalid_terrain)
        assert result is False
        
        # Test invalid physics config
        invalid_physics = PhysicsConfig(
            dt=-0.001,                 # Negative timestep
            solver_type="INVALID",     # Invalid solver
            gravity=[1, 2]            # Wrong gravity vector size
        )
        
        result = await simulation_service.update_physics_config(invalid_physics)
        assert result is False
        
        # Test invalid robot config
        invalid_robot = RobotConfig(
            name="",                   # Empty name
            base_mass=-10.0,          # Negative mass
            joint_stiffness=-5.0      # Negative stiffness
        )
        
        result = await simulation_service.update_robot_config(invalid_robot)
        assert result is False
    
    @pytest.mark.asyncio
    async def test_configuration_backup_and_history(self, simulation_service):
        """Test configuration backup and history functionality"""
        await simulation_service.initialize()
        
        # Get initial history count
        initial_history_count = len(simulation_service.config_history)
        
        # Update terrain config
        terrain_config = TerrainConfig(mesh_type="trimesh")
        await simulation_service.update_terrain_config(terrain_config)
        
        # Check that history was updated
        assert len(simulation_service.config_history) == initial_history_count + 1
        
        # Check history entry
        latest_entry = simulation_service.config_history[-1]
        assert latest_entry["operation"] == "terrain_update"
        assert "timestamp" in latest_entry
        assert "data" in latest_entry
    
    @pytest.mark.asyncio
    async def test_service_status(self, simulation_service):
        """Test service status reporting"""
        await simulation_service.initialize()
        
        status = await simulation_service.get_status()
        
        # Check status structure
        assert "state" in status
        assert "is_initialized" in status
        assert "current_configs" in status
        assert "history_count" in status
        assert "pending_updates" in status
        
        # Check status values
        assert status["state"] == SimulationServiceState.IDLE.value
        assert status["is_initialized"] is True
        assert status["history_count"] >= 0
        assert status["pending_updates"] == 0
        
        # Check that all config types are present
        configs = status["current_configs"]
        assert "simulation" in configs
        assert "terrain" in configs
        assert "physics" in configs
        assert "robot" in configs
    
    @pytest.mark.asyncio
    async def test_service_shutdown(self, simulation_service):
        """Test service shutdown"""
        await simulation_service.initialize()
        
        # Shutdown the service
        await simulation_service.shutdown()
        
        # Check that service is no longer initialized
        assert simulation_service.is_initialized is False
        assert simulation_service.state == SimulationServiceState.IDLE


class TestDeploymentService:
    """Test cases for DeploymentService"""
    
    @pytest.fixture
    async def mock_session_manager(self):
        """Create a mock session manager"""
        manager = Mock(spec=EnhancedZenohSessionManager)
        manager.initialize = AsyncMock(return_value=True)
        manager.shutdown = AsyncMock()
        return manager
    
    @pytest.fixture
    def deployment_config(self):
        """Create a test deployment service config"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config = DeploymentServiceConfig(
                export_base_path=os.path.join(temp_dir, "exports"),
                backup_path=os.path.join(temp_dir, "backups"),
                validation_enabled=True,
                backup_original=True
            )
            yield config
    
    @pytest.fixture
    async def deployment_service(self, mock_session_manager, deployment_config):
        """Create a deployment service instance"""
        with patch('zenoh_services.services.deployment_service.TopicManager'):
            service = DeploymentService(mock_session_manager, deployment_config)
            # Mock the topic manager
            service.topic_manager = Mock(spec=TopicManager)
            service.topic_manager.register_topic = AsyncMock()
            service.topic_manager.create_publisher = AsyncMock()
            service.topic_manager.create_subscriber = AsyncMock()
            service.topic_manager.publish_message = AsyncMock()
            
            yield service
    
    @pytest.fixture
    def mock_pytorch_model(self):
        """Create a mock PyTorch model file"""
        with tempfile.NamedTemporaryFile(suffix='.pt', delete=False) as temp_file:
            # Create a simple mock model file
            model_data = {"model_state_dict": {"layer1.weight": [1, 2, 3]}}
            
            # Use pickle to simulate torch.save format
            import pickle
            pickle.dump(model_data, temp_file)
            temp_file.flush()
            
            yield temp_file.name
            
            # Cleanup
            os.unlink(temp_file.name)
    
    @pytest.mark.asyncio
    async def test_deployment_service_initialization(self, deployment_service):
        """Test deployment service initialization"""
        # Test successful initialization
        assert await deployment_service.initialize()
        assert deployment_service.is_initialized
        assert deployment_service.state == DeploymentServiceState.IDLE
        
        # Check that storage directories are created
        assert os.path.exists(deployment_service.config.export_base_path)
        assert os.path.exists(deployment_service.config.backup_path)
        
        # Check that format subdirectories are created
        jit_dir = os.path.join(deployment_service.config.export_base_path, "jit")
        onnx_dir = os.path.join(deployment_service.config.export_base_path, "onnx")
        pt_dir = os.path.join(deployment_service.config.export_base_path, "pt")
        
        assert os.path.exists(jit_dir)
        assert os.path.exists(onnx_dir)
        assert os.path.exists(pt_dir)
    
    @pytest.mark.asyncio
    async def test_export_path_generation(self, deployment_service):
        """Test export path generation"""
        await deployment_service.initialize()
        
        # Test JIT export path
        config = DeploymentConfig(
            model_path="/path/to/model.pt",
            export_format="jit"
        )
        
        export_path = await deployment_service._generate_export_path(config)
        
        # Check path structure
        assert "jit" in export_path
        assert export_path.endswith(".pt")
        assert "model" in export_path
        
        # Test ONNX export path
        config.export_format = "onnx"
        export_path = await deployment_service._generate_export_path(config)
        
        assert "onnx" in export_path
        assert export_path.endswith(".onnx")
        
        # Test custom export path
        config.export_path = "custom_model.onnx"
        export_path = await deployment_service._generate_export_path(config)
        
        assert "custom_model.onnx" in export_path
    
    @pytest.mark.asyncio
    async def test_model_validation_nonexistent_file(self, deployment_service):
        """Test model validation with non-existent file"""
        await deployment_service.initialize()
        
        # Test with non-existent file
        result = await deployment_service.validate_model("/nonexistent/model.pt")
        
        assert isinstance(result, ModelValidationResult)
        assert result.is_valid is False
        assert "Model file not found" in result.validation_errors
        assert result.model_format == "unknown"
        assert result.model_size_mb == 0.0
    
    @pytest.mark.asyncio
    async def test_model_validation_existing_file(self, deployment_service, mock_pytorch_model):
        """Test model validation with existing file"""
        await deployment_service.initialize()
        
        # Test with existing file
        result = await deployment_service.validate_model(mock_pytorch_model)
        
        assert isinstance(result, ModelValidationResult)
        assert result.model_format == ".pt"
        assert result.model_size_mb > 0
        
        # Note: Validation may fail due to mocked PyTorch, but structure should be correct
    
    @pytest.mark.asyncio
    async def test_deployment_config_validation(self, deployment_service):
        """Test deployment configuration validation"""
        await deployment_service.initialize()
        
        # Test valid config
        valid_config = DeploymentConfig(
            model_path="/path/to/model.pt",
            export_format="jit",
            target_platform="cpu"
        )
        assert valid_config.validate()
        
        # Test invalid config - empty model path
        invalid_config = DeploymentConfig(
            model_path="",
            export_format="jit"
        )
        assert not invalid_config.validate()
        
        # Test invalid config - unsupported format
        invalid_config = DeploymentConfig(
            model_path="/path/to/model.pt",
            export_format="unsupported"
        )
        assert not invalid_config.validate()
        
        # Test invalid config - unsupported platform
        invalid_config = DeploymentConfig(
            model_path="/path/to/model.pt",
            export_format="jit",
            target_platform="unsupported"
        )
        assert not invalid_config.validate()
    
    @pytest.mark.asyncio
    async def test_export_job_management(self, deployment_service, mock_pytorch_model):
        """Test export job creation and management"""
        await deployment_service.initialize()
        
        # Create export config
        config = DeploymentConfig(
            model_path=mock_pytorch_model,
            export_format="jit"
        )
        
        # Start export job
        with patch.object(deployment_service, '_execute_export_job', new_callable=AsyncMock):
            job_id = await deployment_service.export_model(config)
            
            # Check job was created
            assert job_id in deployment_service.active_jobs
            
            job_status = deployment_service.active_jobs[job_id]
            assert isinstance(job_status, ExportJobStatus)
            assert job_status.job_id == job_id
            assert job_status.source_model == mock_pytorch_model
            assert job_status.target_format == "jit"
    
    @pytest.mark.asyncio
    async def test_job_cancellation(self, deployment_service, mock_pytorch_model):
        """Test export job cancellation"""
        await deployment_service.initialize()
        
        # Create and start a job
        config = DeploymentConfig(model_path=mock_pytorch_model, export_format="jit")
        
        with patch.object(deployment_service, '_execute_export_job', new_callable=AsyncMock):
            job_id = await deployment_service.export_model(config)
            
            # Cancel the job
            result = await deployment_service.cancel_export_job(job_id)
            assert result is True
            
            # Check job was moved to history
            assert job_id not in deployment_service.active_jobs
            assert any(job.job_id == job_id for job in deployment_service.job_history)
            
            # Check job status is error with cancellation message
            cancelled_job = next(job for job in deployment_service.job_history if job.job_id == job_id)
            assert cancelled_job.state == DeploymentServiceState.ERROR
            assert "cancelled" in cancelled_job.error_message.lower()
    
    @pytest.mark.asyncio
    async def test_model_registry(self, deployment_service):
        """Test model registry functionality"""
        await deployment_service.initialize()
        
        # Test registering a model
        with tempfile.NamedTemporaryFile(suffix='.pt', delete=False) as temp_file:
            temp_file.write(b"fake model data")
            temp_file.flush()
            
            try:
                config = DeploymentConfig(
                    model_path=temp_file.name,
                    export_format="jit"
                )
                
                await deployment_service._register_exported_model(temp_file.name, config)
                
                # Check model was added to registry
                assert len(deployment_service.deployed_models) > 0
                
                # Check topic publication was called
                deployment_service.topic_manager.publish_message.assert_called()
                
            finally:
                os.unlink(temp_file.name)
    
    @pytest.mark.asyncio
    async def test_service_status(self, deployment_service):
        """Test deployment service status reporting"""
        await deployment_service.initialize()
        
        status = await deployment_service.get_status()
        
        # Check status structure
        assert "state" in status
        assert "is_initialized" in status
        assert "active_jobs" in status
        assert "available_models_count" in status
        assert "deployed_models_count" in status
        assert "config" in status
        
        # Check status values
        assert status["state"] == DeploymentServiceState.IDLE.value
        assert status["is_initialized"] is True
        assert isinstance(status["active_jobs"], dict)
        assert isinstance(status["available_models_count"], int)
        
        # Check config info
        config_info = status["config"]
        assert "export_formats_available" in config_info
        assert "torch_available" in config_info
        assert "onnx_available" in config_info
    
    @pytest.mark.asyncio
    async def test_model_hash_calculation(self, deployment_service):
        """Test model file hash calculation"""
        await deployment_service.initialize()
        
        # Create a temporary file with known content
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            test_content = b"test model content"
            temp_file.write(test_content)
            temp_file.flush()
            
            try:
                # Calculate hash
                file_hash = await deployment_service._calculate_model_hash(temp_file.name)
                
                # Hash should be non-empty string
                assert isinstance(file_hash, str)
                assert len(file_hash) > 0
                
                # Hash should be consistent
                second_hash = await deployment_service._calculate_model_hash(temp_file.name)
                assert file_hash == second_hash
                
            finally:
                os.unlink(temp_file.name)
    
    @pytest.mark.asyncio
    async def test_service_shutdown(self, deployment_service):
        """Test deployment service shutdown"""
        await deployment_service.initialize()
        
        # Create a mock job
        job_status = ExportJobStatus(
            job_id="test_job",
            state=DeploymentServiceState.EXPORTING
        )
        deployment_service.active_jobs["test_job"] = job_status
        
        # Shutdown the service
        await deployment_service.shutdown()
        
        # Check that service is no longer initialized
        assert deployment_service.is_initialized is False
        assert deployment_service.state == DeploymentServiceState.IDLE
        
        # Check that active jobs were cancelled
        assert len(deployment_service.active_jobs) == 0


class TestServiceIntegration:
    """Integration tests for simulation and deployment services"""
    
    @pytest.fixture
    async def mock_session_manager(self):
        """Create a mock session manager for integration tests"""
        manager = Mock(spec=EnhancedZenohSessionManager)
        manager.initialize = AsyncMock(return_value=True)
        manager.shutdown = AsyncMock()
        return manager
    
    @pytest.mark.asyncio
    async def test_concurrent_service_operation(self, mock_session_manager):
        """Test concurrent operation of simulation and deployment services"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create service configs
            sim_config = SimulationServiceConfig(
                config_storage_path=os.path.join(temp_dir, "sim_configs"),
                backup_path=os.path.join(temp_dir, "sim_backups")
            )
            
            deploy_config = DeploymentServiceConfig(
                export_base_path=os.path.join(temp_dir, "exports"),
                backup_path=os.path.join(temp_dir, "deploy_backups")
            )
            
            # Create services with mocked topic managers
            with patch('zenoh_services.services.simulation_service.TopicManager'), \
                 patch('zenoh_services.services.deployment_service.TopicManager'):
                
                sim_service = SimulationService(mock_session_manager, sim_config)
                deploy_service = DeploymentService(mock_session_manager, deploy_config)
                
                # Mock topic managers
                for service in [sim_service, deploy_service]:
                    service.topic_manager = Mock(spec=TopicManager)
                    service.topic_manager.register_topic = AsyncMock()
                    service.topic_manager.create_publisher = AsyncMock()
                    service.topic_manager.create_subscriber = AsyncMock()
                    service.topic_manager.publish_message = AsyncMock()
                
                # Initialize services concurrently
                init_results = await asyncio.gather(
                    sim_service.initialize(),
                    deploy_service.initialize()
                )
                
                # Both should initialize successfully
                assert all(init_results)
                assert sim_service.is_initialized
                assert deploy_service.is_initialized
                
                # Test concurrent operations
                terrain_config = TerrainConfig(mesh_type="trimesh")
                
                # Create a mock model file for deployment
                with tempfile.NamedTemporaryFile(suffix='.pt', delete=False) as temp_file:
                    temp_file.write(b"fake model")
                    temp_file.flush()
                    
                    try:
                        deployment_config = DeploymentConfig(
                            model_path=temp_file.name,
                            export_format="jit"
                        )
                        
                        # Execute operations concurrently
                        with patch.object(deploy_service, '_execute_export_job', new_callable=AsyncMock):
                            results = await asyncio.gather(
                                sim_service.update_terrain_config(terrain_config),
                                deploy_service.export_model(deployment_config)
                            )
                            
                            # Both operations should complete successfully
                            config_result, job_id = results
                            assert config_result is True
                            assert job_id is not None
                        
                    finally:
                        os.unlink(temp_file.name)
                
                # Shutdown services
                await asyncio.gather(
                    sim_service.shutdown(),
                    deploy_service.shutdown()
                )
    
    @pytest.mark.asyncio
    async def test_configuration_serialization(self):
        """Test configuration serialization/deserialization for service communication"""
        
        # Test terrain config serialization
        terrain_config = TerrainConfig(
            mesh_type="trimesh",
            terrain_length=10.0,
            horizontal_scale=0.15
        )
        
        # Serialize and deserialize
        terrain_dict = terrain_config.to_dict()
        restored_terrain = TerrainConfig.from_dict(terrain_dict)
        
        # Check that values are preserved
        assert restored_terrain.mesh_type == terrain_config.mesh_type
        assert restored_terrain.terrain_length == terrain_config.terrain_length
        assert restored_terrain.horizontal_scale == terrain_config.horizontal_scale
        
        # Test deployment config serialization
        deploy_config = DeploymentConfig(
            model_path="/path/to/model.pt",
            export_format="onnx",
            target_platform="gpu"
        )
        
        deploy_dict = deploy_config.to_dict()
        restored_deploy = DeploymentConfig.from_dict(deploy_dict)
        
        assert restored_deploy.model_path == deploy_config.model_path
        assert restored_deploy.export_format == deploy_config.export_format
        assert restored_deploy.target_platform == deploy_config.target_platform
        
        # Test configuration request serialization
        config_request = ConfigurationRequest(
            config_type="simulation",
            action="set",
            config_data=terrain_dict
        )
        
        request_dict = config_request.to_dict()
        restored_request = ConfigurationRequest.from_dict(request_dict)
        
        assert restored_request.config_type == config_request.config_type
        assert restored_request.action == config_request.action
        assert restored_request.config_data == config_request.config_data


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v", "--tb=short"])