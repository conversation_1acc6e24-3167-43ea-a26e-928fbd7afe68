"""
Task 15.1.3: Multi-Service Coordination Test Cases
Tests for coordinated operations between multiple services in the system
"""

import asyncio
import pytest
import logging
import time
import json
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

# Import all necessary services and components
from zenoh_services.core.enhanced_session_manager import EnhancedZenohSessionManager
from zenoh_services.core.topic_manager import TopicManager
from zenoh_services.core.data_models import (
    TrainingConfig, TrainingState, TrainingMetrics, PlayState, 
    DeploymentState, SimulationConfig, SystemConfig
)
from zenoh_services.core.error_handler import get_error_handler, ErrorContext, ErrorCategory, ErrorSeverity
from zenoh_services.core.fault_detection import get_fault_detection_system

from zenoh_services.services.training_service import TrainingService, TrainingServiceConfig
from zenoh_services.services.play_service import PlayService
from zenoh_services.services.simulation_service import SimulationService
from zenoh_services.services.deployment_service import DeploymentService
from zenoh_services.services.config_service import ConfigService

# Set up logging for tests
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class WorkflowState(Enum):
    """Workflow execution states"""
    INITIALIZING = "initializing"
    CONFIGURING = "configuring"
    TRAINING = "training"
    TESTING = "testing"
    DEPLOYING = "deploying"
    COMPLETED = "completed"
    FAILED = "failed"
    PAUSED = "paused"


@dataclass
class ServiceEvent:
    """Service event for coordination"""
    service_name: str
    event_type: str
    data: Any
    timestamp: float
    workflow_id: str


class WorkflowCoordinator:
    """Coordinates multi-service workflows"""
    
    def __init__(self, services: Dict[str, Any]):
        self.services = services
        self.workflows = {}
        self.event_history = []
        self.state = WorkflowState.INITIALIZING
        
    async def start_training_workflow(self, workflow_id: str, config: TrainingConfig) -> bool:
        """Start coordinated training workflow"""
        logger.info(f"Starting training workflow {workflow_id}")
        
        workflow = {
            "id": workflow_id,
            "state": WorkflowState.CONFIGURING,
            "config": config,
            "start_time": time.time(),
            "steps_completed": [],
            "current_step": None
        }
        
        self.workflows[workflow_id] = workflow
        
        try:
            # Step 1: Configure all services
            await self._configure_services(workflow_id, config)
            workflow["steps_completed"].append("configure")
            
            # Step 2: Start training
            workflow["state"] = WorkflowState.TRAINING
            workflow["current_step"] = "training"
            await self._start_training(workflow_id, config)
            workflow["steps_completed"].append("training")
            
            # Step 3: Monitor training
            await self._monitor_training(workflow_id)
            
            workflow["state"] = WorkflowState.COMPLETED
            workflow["end_time"] = time.time()
            
            return True
            
        except Exception as e:
            workflow["state"] = WorkflowState.FAILED
            workflow["error"] = str(e)
            logger.error(f"Workflow {workflow_id} failed: {e}")
            return False
    
    async def _configure_services(self, workflow_id: str, config: TrainingConfig):
        """Configure all services for training"""
        config_service = self.services["config"]
        simulation_service = self.services["simulation"]
        
        # Set training configuration
        await config_service.set_training_config(config)
        
        # Configure simulation
        sim_config = SimulationConfig(
            physics_engine="isaac_gym",
            dt=0.005,
            substeps=2,
            gravity=[0.0, 0.0, -9.81],
            terrain_type="plane"
        )
        await simulation_service.set_simulation_config(sim_config)
        
        # Emit configuration event
        event = ServiceEvent(
            service_name="coordinator",
            event_type="services_configured",
            data={"workflow_id": workflow_id},
            timestamp=time.time(),
            workflow_id=workflow_id
        )
        self.event_history.append(event)
    
    async def _start_training(self, workflow_id: str, config: TrainingConfig):
        """Start training service"""
        training_service = self.services["training"]
        
        # Start training
        await training_service.start_training(config)
        
        # Emit training started event
        event = ServiceEvent(
            service_name="training",
            event_type="training_started",
            data={"workflow_id": workflow_id, "config": config},
            timestamp=time.time(),
            workflow_id=workflow_id
        )
        self.event_history.append(event)
    
    async def _monitor_training(self, workflow_id: str):
        """Monitor training progress"""
        training_service = self.services["training"]
        
        # Simulate monitoring for a few iterations
        for i in range(3):
            await asyncio.sleep(0.1)
            
            status = await training_service.get_training_status()
            metrics = await training_service.get_current_metrics()
            
            event = ServiceEvent(
                service_name="training",
                event_type="training_progress",
                data={"status": status, "metrics": metrics},
                timestamp=time.time(),
                workflow_id=workflow_id
            )
            self.event_history.append(event)
    
    async def start_deployment_workflow(self, workflow_id: str, model_path: str) -> bool:
        """Start coordinated deployment workflow"""
        logger.info(f"Starting deployment workflow {workflow_id}")
        
        workflow = {
            "id": workflow_id,
            "state": WorkflowState.TESTING,
            "model_path": model_path,
            "start_time": time.time(),
            "steps_completed": []
        }
        
        self.workflows[workflow_id] = workflow
        
        try:
            # Step 1: Test model
            await self._test_model(workflow_id, model_path)
            workflow["steps_completed"].append("testing")
            
            # Step 2: Deploy model
            workflow["state"] = WorkflowState.DEPLOYING
            await self._deploy_model(workflow_id, model_path)
            workflow["steps_completed"].append("deployment")
            
            workflow["state"] = WorkflowState.COMPLETED
            workflow["end_time"] = time.time()
            
            return True
            
        except Exception as e:
            workflow["state"] = WorkflowState.FAILED
            workflow["error"] = str(e)
            logger.error(f"Deployment workflow {workflow_id} failed: {e}")
            return False
    
    async def _test_model(self, workflow_id: str, model_path: str):
        """Test model using play service"""
        play_service = self.services["play"]
        
        # Load and test model
        await play_service.load_policy(model_path)
        await play_service.start_testing()
        
        # Get test results
        test_results = await play_service.get_test_results()
        
        event = ServiceEvent(
            service_name="play",
            event_type="model_tested",
            data={"model_path": model_path, "results": test_results},
            timestamp=time.time(),
            workflow_id=workflow_id
        )
        self.event_history.append(event)
    
    async def _deploy_model(self, workflow_id: str, model_path: str):
        """Deploy model using deployment service"""
        deployment_service = self.services["deployment"]
        
        # Export and deploy model
        exported_path = await deployment_service.export_model(model_path, "onnx")
        await deployment_service.validate_model(exported_path)
        await deployment_service.deploy_model(exported_path)
        
        event = ServiceEvent(
            service_name="deployment",
            event_type="model_deployed",
            data={"original_path": model_path, "deployed_path": exported_path},
            timestamp=time.time(),
            workflow_id=workflow_id
        )
        self.event_history.append(event)
    
    def get_workflow_status(self, workflow_id: str) -> Dict[str, Any]:
        """Get workflow status"""
        return self.workflows.get(workflow_id, {})
    
    def get_workflow_events(self, workflow_id: str) -> List[ServiceEvent]:
        """Get events for specific workflow"""
        return [event for event in self.event_history if event.workflow_id == workflow_id]


@pytest.fixture
async def mock_services():
    """Create mock services for testing"""
    
    # Mock session manager
    session_manager = Mock(spec=EnhancedZenohSessionManager)
    session_manager.session = Mock()
    session_manager.is_connected = Mock(return_value=True)
    
    # Create mock services
    services = {
        "training": TrainingService(session_manager),
        "play": PlayService(session_manager),
        "simulation": SimulationService(session_manager),
        "deployment": DeploymentService(session_manager),
        "config": ConfigService(session_manager)
    }
    
    # Mock all service methods
    for service_name, service in services.items():
        service.initialize = AsyncMock(return_value=True)
        service.start = AsyncMock(return_value=True)
        service.stop = AsyncMock(return_value=True)
        service.cleanup = AsyncMock(return_value=True)
    
    # Mock specific training service methods
    training_service = services["training"]
    training_service.start_training = AsyncMock(return_value=True)
    training_service.get_training_status = AsyncMock(return_value=TrainingState.TRAINING)
    training_service.get_current_metrics = AsyncMock(return_value=TrainingMetrics(
        iteration=10,
        mean_reward=150.0,
        std_reward=25.0,
        mean_episode_length=800.0,
        learning_rate=0.001,
        total_timesteps=640000,
        fps=1200.0
    ))
    
    # Mock play service methods
    play_service = services["play"]
    play_service.load_policy = AsyncMock(return_value=True)
    play_service.start_testing = AsyncMock(return_value=True)
    play_service.get_test_results = AsyncMock(return_value={
        "episodes": 10,
        "mean_reward": 180.0,
        "success_rate": 0.85,
        "avg_episode_length": 750.0
    })
    
    # Mock simulation service methods
    simulation_service = services["simulation"]
    simulation_service.set_simulation_config = AsyncMock(return_value=True)
    simulation_service.reset_simulation = AsyncMock(return_value=True)
    simulation_service.get_simulation_state = AsyncMock(return_value="running")
    
    # Mock deployment service methods
    deployment_service = services["deployment"]
    deployment_service.export_model = AsyncMock(return_value="model.onnx")
    deployment_service.validate_model = AsyncMock(return_value=True)
    deployment_service.deploy_model = AsyncMock(return_value=True)
    
    # Mock config service methods
    config_service = services["config"]
    config_service.set_training_config = AsyncMock(return_value=True)
    config_service.get_training_config = AsyncMock()
    
    return services


@pytest.fixture
def workflow_coordinator(mock_services):
    """Create workflow coordinator with mock services"""
    return WorkflowCoordinator(mock_services)


class TestMultiServiceCoordination:
    """Test multi-service coordination scenarios"""
    
    @pytest.mark.asyncio
    async def test_training_workflow_coordination(self, workflow_coordinator, mock_services):
        """Test coordinated training workflow"""
        
        logger.info("Testing training workflow coordination...")
        
        # Create training configuration
        config = TrainingConfig(
            task_name="coordinated_training",
            num_envs=64,
            max_iterations=100,
            learning_rate=0.001,
            batch_size=2048
        )
        
        # Start training workflow
        workflow_id = "test_training_workflow"
        result = await workflow_coordinator.start_training_workflow(workflow_id, config)
        
        # Verify workflow completed successfully
        assert result is True
        
        # Check workflow status
        workflow_status = workflow_coordinator.get_workflow_status(workflow_id)
        assert workflow_status["state"] == WorkflowState.COMPLETED
        assert "configure" in workflow_status["steps_completed"]
        assert "training" in workflow_status["steps_completed"]
        
        # Verify service interactions
        config_service = mock_services["config"]
        config_service.set_training_config.assert_called_once()
        
        simulation_service = mock_services["simulation"]
        simulation_service.set_simulation_config.assert_called_once()
        
        training_service = mock_services["training"]
        training_service.start_training.assert_called_once()
        
        # Check workflow events
        events = workflow_coordinator.get_workflow_events(workflow_id)
        assert len(events) >= 4  # At least configure, start, and progress events
        
        event_types = [event.event_type for event in events]
        assert "services_configured" in event_types
        assert "training_started" in event_types
        assert "training_progress" in event_types
        
        logger.info("Training workflow coordination test passed!")
    
    @pytest.mark.asyncio
    async def test_deployment_workflow_coordination(self, workflow_coordinator, mock_services):
        """Test coordinated deployment workflow"""
        
        logger.info("Testing deployment workflow coordination...")
        
        # Start deployment workflow
        workflow_id = "test_deployment_workflow"
        model_path = "trained_model.pt"
        result = await workflow_coordinator.start_deployment_workflow(workflow_id, model_path)
        
        # Verify workflow completed successfully
        assert result is True
        
        # Check workflow status
        workflow_status = workflow_coordinator.get_workflow_status(workflow_id)
        assert workflow_status["state"] == WorkflowState.COMPLETED
        assert "testing" in workflow_status["steps_completed"]
        assert "deployment" in workflow_status["steps_completed"]
        
        # Verify service interactions
        play_service = mock_services["play"]
        play_service.load_policy.assert_called_once_with(model_path)
        play_service.start_testing.assert_called_once()
        play_service.get_test_results.assert_called_once()
        
        deployment_service = mock_services["deployment"]
        deployment_service.export_model.assert_called_once()
        deployment_service.validate_model.assert_called_once()
        deployment_service.deploy_model.assert_called_once()
        
        # Check workflow events
        events = workflow_coordinator.get_workflow_events(workflow_id)
        assert len(events) == 2  # Test and deployment events
        
        event_types = [event.event_type for event in events]
        assert "model_tested" in event_types
        assert "model_deployed" in event_types
        
        logger.info("Deployment workflow coordination test passed!")
    
    @pytest.mark.asyncio
    async def test_concurrent_workflow_coordination(self, mock_services):
        """Test multiple concurrent workflows"""
        
        logger.info("Testing concurrent workflow coordination...")
        
        coordinator = WorkflowCoordinator(mock_services)
        
        # Create multiple training configurations
        configs = [
            TrainingConfig(task_name=f"task_{i}", num_envs=64, max_iterations=10)
            for i in range(3)
        ]
        
        # Start multiple workflows concurrently
        workflow_tasks = []
        for i, config in enumerate(configs):
            workflow_id = f"concurrent_workflow_{i}"
            task = asyncio.create_task(
                coordinator.start_training_workflow(workflow_id, config)
            )
            workflow_tasks.append((workflow_id, task))
        
        # Wait for all workflows to complete
        results = []
        for workflow_id, task in workflow_tasks:
            result = await task
            results.append((workflow_id, result))
        
        # Verify all workflows completed successfully
        for workflow_id, result in results:
            assert result is True
            workflow_status = coordinator.get_workflow_status(workflow_id)
            assert workflow_status["state"] == WorkflowState.COMPLETED
        
        # Verify all services were called for each workflow
        training_service = mock_services["training"]
        assert training_service.start_training.call_count == 3
        
        logger.info("Concurrent workflow coordination test passed!")
    
    @pytest.mark.asyncio
    async def test_service_dependency_chain(self, mock_services):
        """Test service dependency chain execution"""
        
        logger.info("Testing service dependency chain...")
        
        coordinator = WorkflowCoordinator(mock_services)
        
        # Track service execution order
        execution_order = []
        
        # Mock services to track execution order
        async def track_config_call(*args, **kwargs):
            execution_order.append("config")
            return True
        
        async def track_simulation_call(*args, **kwargs):
            execution_order.append("simulation")
            return True
        
        async def track_training_call(*args, **kwargs):
            execution_order.append("training")
            return True
        
        mock_services["config"].set_training_config.side_effect = track_config_call
        mock_services["simulation"].set_simulation_config.side_effect = track_simulation_call
        mock_services["training"].start_training.side_effect = track_training_call
        
        # Start workflow
        config = TrainingConfig(task_name="dependency_test", num_envs=64, max_iterations=10)
        workflow_id = "dependency_test_workflow"
        result = await coordinator.start_training_workflow(workflow_id, config)
        
        # Verify workflow completed
        assert result is True
        
        # Verify execution order (config -> simulation -> training)
        assert execution_order == ["config", "simulation", "training"]
        
        logger.info("Service dependency chain test passed!")
    
    @pytest.mark.asyncio
    async def test_cross_service_data_flow(self, mock_services):
        """Test data flow between services"""
        
        logger.info("Testing cross-service data flow...")
        
        coordinator = WorkflowCoordinator(mock_services)
        
        # Track data passing between services
        data_flow = {}
        
        # Mock config service to capture training config
        async def capture_training_config(config):
            data_flow["training_config"] = config
            return True
        
        # Mock training service to use captured config
        async def use_training_config(config):
            data_flow["used_config"] = config
            assert config == data_flow["training_config"]
            return True
        
        mock_services["config"].set_training_config.side_effect = capture_training_config
        mock_services["training"].start_training.side_effect = use_training_config
        
        # Start workflow
        config = TrainingConfig(task_name="data_flow_test", num_envs=128, max_iterations=20)
        workflow_id = "data_flow_workflow"
        result = await coordinator.start_training_workflow(workflow_id, config)
        
        # Verify workflow completed and data flowed correctly
        assert result is True
        assert "training_config" in data_flow
        assert "used_config" in data_flow
        assert data_flow["training_config"] == data_flow["used_config"]
        
        logger.info("Cross-service data flow test passed!")
    
    @pytest.mark.asyncio
    async def test_service_failure_handling(self, mock_services):
        """Test workflow behavior when service fails"""
        
        logger.info("Testing service failure handling...")
        
        coordinator = WorkflowCoordinator(mock_services)
        
        # Make training service fail
        mock_services["training"].start_training.side_effect = Exception("Training service failed")
        
        # Start workflow
        config = TrainingConfig(task_name="failure_test", num_envs=64, max_iterations=10)
        workflow_id = "failure_test_workflow"
        result = await coordinator.start_training_workflow(workflow_id, config)
        
        # Verify workflow failed
        assert result is False
        
        # Check workflow status
        workflow_status = coordinator.get_workflow_status(workflow_id)
        assert workflow_status["state"] == WorkflowState.FAILED
        assert "error" in workflow_status
        assert "Training service failed" in workflow_status["error"]
        
        logger.info("Service failure handling test passed!")
    
    @pytest.mark.asyncio
    async def test_workflow_state_transitions(self, workflow_coordinator, mock_services):
        """Test workflow state transitions"""
        
        logger.info("Testing workflow state transitions...")
        
        # Track state changes
        state_history = []
        
        # Override workflow methods to track states
        original_configure = workflow_coordinator._configure_services
        original_start_training = workflow_coordinator._start_training
        
        async def track_configure(*args, **kwargs):
            state_history.append(WorkflowState.CONFIGURING)
            return await original_configure(*args, **kwargs)
        
        async def track_training(*args, **kwargs):
            state_history.append(WorkflowState.TRAINING)
            return await original_start_training(*args, **kwargs)
        
        workflow_coordinator._configure_services = track_configure
        workflow_coordinator._start_training = track_training
        
        # Start workflow
        config = TrainingConfig(task_name="state_test", num_envs=64, max_iterations=10)
        workflow_id = "state_test_workflow"
        result = await workflow_coordinator.start_training_workflow(workflow_id, config)
        
        # Verify workflow completed
        assert result is True
        
        # Verify state transitions
        assert WorkflowState.CONFIGURING in state_history
        assert WorkflowState.TRAINING in state_history
        
        # Verify final state
        workflow_status = workflow_coordinator.get_workflow_status(workflow_id)
        assert workflow_status["state"] == WorkflowState.COMPLETED
        
        logger.info("Workflow state transitions test passed!")
    
    @pytest.mark.asyncio
    async def test_event_ordering_and_timing(self, workflow_coordinator, mock_services):
        """Test event ordering and timing in workflows"""
        
        logger.info("Testing event ordering and timing...")
        
        # Start workflow
        config = TrainingConfig(task_name="timing_test", num_envs=64, max_iterations=10)
        workflow_id = "timing_test_workflow"
        result = await workflow_coordinator.start_training_workflow(workflow_id, config)
        
        # Verify workflow completed
        assert result is True
        
        # Get events for the workflow
        events = workflow_coordinator.get_workflow_events(workflow_id)
        
        # Verify events are ordered by timestamp
        timestamps = [event.timestamp for event in events]
        assert timestamps == sorted(timestamps), "Events are not in chronological order"
        
        # Verify expected event sequence
        event_types = [event.event_type for event in events]
        
        # Should have configuration before training
        config_index = next(i for i, event_type in enumerate(event_types) 
                          if event_type == "services_configured")
        training_index = next(i for i, event_type in enumerate(event_types) 
                            if event_type == "training_started")
        
        assert config_index < training_index, "Configuration should happen before training"
        
        # Verify timing intervals
        for i in range(1, len(events)):
            time_diff = events[i].timestamp - events[i-1].timestamp
            assert time_diff >= 0, "Events cannot have negative time differences"
            assert time_diff < 10.0, "Events should happen within reasonable time"
        
        logger.info("Event ordering and timing test passed!")
    
    @pytest.mark.asyncio
    async def test_resource_sharing_coordination(self, mock_services):
        """Test coordination when services share resources"""
        
        logger.info("Testing resource sharing coordination...")
        
        coordinator = WorkflowCoordinator(mock_services)
        
        # Mock resource usage tracking
        gpu_usage = {"current": 0, "max": 100}
        resource_locks = {}
        
        async def acquire_gpu_resource(service_name, amount):
            if gpu_usage["current"] + amount > gpu_usage["max"]:
                raise Exception(f"Insufficient GPU resources for {service_name}")
            
            gpu_usage["current"] += amount
            resource_locks[service_name] = amount
            return True
        
        async def release_gpu_resource(service_name):
            if service_name in resource_locks:
                gpu_usage["current"] -= resource_locks[service_name]
                del resource_locks[service_name]
            return True
        
        # Mock training service to use GPU resources
        original_start_training = mock_services["training"].start_training
        async def training_with_resources(*args, **kwargs):
            await acquire_gpu_resource("training", 80)
            result = await original_start_training(*args, **kwargs)
            await release_gpu_resource("training")
            return result
        
        mock_services["training"].start_training.side_effect = training_with_resources
        
        # Start workflow
        config = TrainingConfig(task_name="resource_test", num_envs=64, max_iterations=10)
        workflow_id = "resource_test_workflow"
        result = await coordinator.start_training_workflow(workflow_id, config)
        
        # Verify workflow completed successfully
        assert result is True
        
        # Verify resources were properly managed
        assert gpu_usage["current"] == 0, "GPU resources should be released after workflow"
        assert len(resource_locks) == 0, "No resource locks should remain"
        
        logger.info("Resource sharing coordination test passed!")
    
    @pytest.mark.asyncio
    async def test_workflow_recovery_mechanisms(self, mock_services):
        """Test workflow recovery from partial failures"""
        
        logger.info("Testing workflow recovery mechanisms...")
        
        coordinator = WorkflowCoordinator(mock_services)
        
        # Track recovery attempts
        recovery_attempts = []
        
        # Make simulation service fail initially, then succeed
        call_count = 0
        async def failing_simulation_config(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                recovery_attempts.append("simulation_retry")
                raise Exception("Simulation initialization failed")
            return True
        
        mock_services["simulation"].set_simulation_config.side_effect = failing_simulation_config
        
        # Enhance coordinator with retry logic
        original_configure = coordinator._configure_services
        async def configure_with_retry(workflow_id, config):
            try:
                await original_configure(workflow_id, config)
            except Exception as e:
                recovery_attempts.append("configuration_retry")
                # Retry configuration
                await original_configure(workflow_id, config)
        
        coordinator._configure_services = configure_with_retry
        
        # Start workflow
        config = TrainingConfig(task_name="recovery_test", num_envs=64, max_iterations=10)
        workflow_id = "recovery_test_workflow"
        result = await coordinator.start_training_workflow(workflow_id, config)
        
        # Verify workflow completed despite initial failure
        assert result is True
        
        # Verify recovery was attempted
        assert len(recovery_attempts) >= 1
        assert "configuration_retry" in recovery_attempts
        
        # Verify simulation service was called multiple times
        assert mock_services["simulation"].set_simulation_config.call_count == 2
        
        logger.info("Workflow recovery mechanisms test passed!")


class TestServiceInteractionPatterns:
    """Test different service interaction patterns"""
    
    @pytest.mark.asyncio
    async def test_publish_subscribe_coordination(self, mock_services):
        """Test publish-subscribe coordination pattern"""
        
        logger.info("Testing publish-subscribe coordination...")
        
        # Mock Zenoh client for message passing
        class MockZenohCoordination:
            def __init__(self):
                self.subscribers = {}
                self.published_messages = []
            
            async def subscribe(self, topic, callback):
                self.subscribers[topic] = callback
            
            async def publish(self, topic, data):
                message = {"topic": topic, "data": data, "timestamp": time.time()}
                self.published_messages.append(message)
                
                if topic in self.subscribers:
                    await self.subscribers[topic](data)
        
        zenoh_client = MockZenohCoordination()
        
        # Set up service coordination via pub/sub
        service_states = {}
        
        async def training_status_callback(data):
            service_states["training"] = data
        
        async def deployment_ready_callback(data):
            service_states["deployment"] = data
        
        await zenoh_client.subscribe("legged_gym/training/status", training_status_callback)
        await zenoh_client.subscribe("legged_gym/deployment/ready", deployment_ready_callback)
        
        # Simulate service interactions
        await zenoh_client.publish("legged_gym/training/status", {"state": "training", "progress": 0.5})
        await zenoh_client.publish("legged_gym/deployment/ready", {"ready": True, "model_path": "model.pt"})
        
        # Verify message coordination
        assert "training" in service_states
        assert "deployment" in service_states
        assert service_states["training"]["state"] == "training"
        assert service_states["deployment"]["ready"] is True
        
        # Verify message publishing
        assert len(zenoh_client.published_messages) == 2
        topics = [msg["topic"] for msg in zenoh_client.published_messages]
        assert "legged_gym/training/status" in topics
        assert "legged_gym/deployment/ready" in topics
        
        logger.info("Publish-subscribe coordination test passed!")
    
    @pytest.mark.asyncio
    async def test_request_response_coordination(self, mock_services):
        """Test request-response coordination pattern"""
        
        logger.info("Testing request-response coordination...")
        
        # Mock request-response coordination
        class ServiceRequestManager:
            def __init__(self, services):
                self.services = services
                self.pending_requests = {}
                self.request_id_counter = 0
            
            async def send_request(self, service_name: str, operation: str, data: Any) -> Any:
                request_id = f"req_{self.request_id_counter}"
                self.request_id_counter += 1
                
                # Store request
                self.pending_requests[request_id] = {
                    "service": service_name,
                    "operation": operation,
                    "data": data,
                    "timestamp": time.time()
                }
                
                # Process request
                service = self.services[service_name]
                
                if operation == "get_status":
                    if service_name == "training":
                        response = await service.get_training_status()
                    elif service_name == "play":
                        response = await service.get_play_status()
                    else:
                        response = "unknown"
                elif operation == "start":
                    if service_name == "training":
                        response = await service.start_training(data)
                    elif service_name == "play":
                        response = await service.start_testing()
                    else:
                        response = True
                else:
                    response = None
                
                # Clean up request
                del self.pending_requests[request_id]
                
                return {"request_id": request_id, "response": response}
        
        request_manager = ServiceRequestManager(mock_services)
        
        # Send coordinated requests
        config = TrainingConfig(task_name="request_test", num_envs=64, max_iterations=10)
        
        # Request training start
        training_response = await request_manager.send_request(
            "training", "start", config
        )
        assert training_response["response"] is True
        
        # Request training status
        status_response = await request_manager.send_request(
            "training", "get_status", None
        )
        assert status_response["response"] == TrainingState.TRAINING
        
        # Request testing start
        testing_response = await request_manager.send_request(
            "play", "start", None
        )
        assert testing_response["response"] is True
        
        # Verify all requests were processed
        assert len(request_manager.pending_requests) == 0
        
        logger.info("Request-response coordination test passed!")
    
    @pytest.mark.asyncio
    async def test_event_driven_coordination(self, mock_services):
        """Test event-driven coordination pattern"""
        
        logger.info("Testing event-driven coordination...")
        
        # Event-driven coordination system
        class EventDrivenCoordinator:
            def __init__(self, services):
                self.services = services
                self.event_handlers = {}
                self.event_history = []
            
            def register_handler(self, event_type: str, handler_func):
                if event_type not in self.event_handlers:
                    self.event_handlers[event_type] = []
                self.event_handlers[event_type].append(handler_func)
            
            async def emit_event(self, event_type: str, data: Any):
                event = {
                    "type": event_type,
                    "data": data,
                    "timestamp": time.time()
                }
                self.event_history.append(event)
                
                # Trigger handlers
                if event_type in self.event_handlers:
                    for handler in self.event_handlers[event_type]:
                        await handler(data)
        
        coordinator = EventDrivenCoordinator(mock_services)
        
        # Register event handlers
        workflow_state = {"training_completed": False, "testing_started": False}
        
        async def on_training_completed(data):
            workflow_state["training_completed"] = True
            # Automatically start testing when training completes
            await coordinator.emit_event("start_testing", {"model_path": data["model_path"]})
        
        async def on_start_testing(data):
            workflow_state["testing_started"] = True
            play_service = coordinator.services["play"]
            await play_service.load_policy(data["model_path"])
            await play_service.start_testing()
        
        coordinator.register_handler("training_completed", on_training_completed)
        coordinator.register_handler("start_testing", on_start_testing)
        
        # Trigger event chain
        await coordinator.emit_event("training_completed", {"model_path": "trained_model.pt"})
        
        # Verify event chain execution
        assert workflow_state["training_completed"] is True
        assert workflow_state["testing_started"] is True
        
        # Verify services were called
        play_service = mock_services["play"]
        play_service.load_policy.assert_called_once_with("trained_model.pt")
        play_service.start_testing.assert_called_once()
        
        # Verify event history
        assert len(coordinator.event_history) == 2
        event_types = [event["type"] for event in coordinator.event_history]
        assert "training_completed" in event_types
        assert "start_testing" in event_types
        
        logger.info("Event-driven coordination test passed!")


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v", "-s", "--tb=short"])