"""
Simplified service tests without <PERSON> Gym dependencies
Tests the service logic and Zenoh communication patterns
"""

import pytest
import asyncio
import time
from unittest.mock import MagicMock, patch, AsyncMock

from zenoh_services.core.enhanced_session_manager import EnhancedZenohSessionManager, EnhancedZenohConfig
from zenoh_services.core.topic_manager import TopicManager
from zenoh_services.core.data_models import TrainingConfig, TrainingCommand, PlayConfig
from zenoh_services.core.message_format import MessageFactory
from zenoh_services.core import topics


class MockZenohSession:
    """Mock Zenoh session for testing"""
    
    def __init__(self):
        self.publishers = {}
        self.subscribers = {}
        self.is_closed = False
        self.published_messages = []
    
    def declare_publisher(self, topic):
        mock_pub = MagicMock()
        mock_pub.put = MagicMock(side_effect=lambda data: self.published_messages.append((topic, data)))
        self.publishers[topic] = mock_pub
        return mock_pub
    
    def declare_subscriber(self, topic, callback):
        mock_sub = MagicMock()
        mock_sub.undeclare = MagicMock()
        self.subscribers[topic] = mock_sub
        return mock_sub
    
    def close(self):
        self.is_closed = True


@pytest.fixture
async def session_manager():
    """Create session manager with mocked Zenoh"""
    mock_session = MockZenohSession()
    config = EnhancedZenohConfig(
        router_endpoints=["tcp/127.0.0.1:7447"],
        enable_heartbeat=False,
        enable_metrics=False
    )
    
    manager = EnhancedZenohSessionManager(config, "test_service")
    
    with patch('zenoh.open', return_value=mock_session):
        await manager.initialize()
    
    yield manager
    
    await manager.shutdown()


class TestServiceCore:
    """Test core service functionality without Isaac Gym dependencies"""
    
    @pytest.mark.asyncio
    async def test_training_service_import_and_init(self):
        """Test that training service can be imported and configured"""
        # Import with mocked dependencies
        with patch.dict('sys.modules', {
            'legged_gym.envs': MagicMock(),
            'legged_gym.utils': MagicMock(),
            'rsl_rl.runners.on_policy_runner': MagicMock(),
            'isaacgym': MagicMock()
        }):
            from zenoh_services.services.training_service import TrainingServiceConfig, TrainingServiceState
            
            # Test config creation
            config = TrainingServiceConfig(
                task_name="test_task",
                num_envs=4,
                max_iterations=10
            )
            
            assert config.task_name == "test_task"
            assert config.num_envs == 4
            assert config.max_iterations == 10
            
            # Test state enum
            assert TrainingServiceState.IDLE.value == "idle"
            assert TrainingServiceState.TRAINING.value == "training"
    
    @pytest.mark.asyncio
    async def test_play_service_import_and_init(self):
        """Test that play service can be imported and configured"""
        # Import with mocked dependencies  
        with patch.dict('sys.modules', {
            'legged_gym.envs': MagicMock(),
            'legged_gym.utils': MagicMock(),
            'legged_gym': MagicMock(),
            'isaacgym': MagicMock(),
            'isaacgym.gymapi': MagicMock(),
            'isaacgym.torch_utils': MagicMock(),
            'cv2': MagicMock()
        }):
            from zenoh_services.services.play_service import PlayServiceConfig, PlayServiceState
            
            # Test config creation
            config = PlayServiceConfig(
                task_name="test_task",
                num_envs=4,
                render=False
            )
            
            assert config.task_name == "test_task"
            assert config.num_envs == 4
            assert config.render is False
            
            # Test state enum
            assert PlayServiceState.IDLE.value == "idle"
            assert PlayServiceState.RUNNING.value == "running"
    
    @pytest.mark.asyncio
    async def test_training_command_data_model(self):
        """Test TrainingCommand data model"""
        from zenoh_services.core.data_models import TrainingCommand, TrainingConfig
        
        # Test valid command
        config = TrainingConfig(task_name="test_task", max_iterations=10)
        command = TrainingCommand(command="start", config=config)
        
        assert command.validate() is True
        assert command.command == "start"
        assert command.config.task_name == "test_task"
        
        # Test invalid command
        invalid_command = TrainingCommand(command="invalid")
        assert invalid_command.validate() is False
        
        # Test serialization
        serialized = command.to_dict()
        assert "command" in serialized
        assert "config" in serialized
        
        # Test deserialization
        restored = TrainingCommand.from_dict(serialized)
        assert restored.command == "start"
    
    @pytest.mark.asyncio 
    async def test_topic_registration(self, session_manager):
        """Test topic registration for services"""
        topic_manager = TopicManager(session_manager)
        
        # Test training topics
        training_topics = [
            topics.TRAINING_COMMAND,
            topics.TRAINING_STATUS,
            topics.TRAINING_METRICS
        ]
        
        for topic in training_topics:
            result = await topic_manager.register_topic(topic)
            assert result is True
            assert topic in topic_manager.registered_topics
            
            # Test publisher creation
            result = await topic_manager.create_publisher(topic)
            assert result is True
    
    @pytest.mark.asyncio
    async def test_message_publishing(self, session_manager):
        """Test message publishing through topic manager"""
        topic_manager = TopicManager(session_manager)
        
        # Register and create publisher
        await topic_manager.register_topic(topics.TRAINING_STATUS)
        await topic_manager.create_publisher(topics.TRAINING_STATUS)
        
        # Create and publish message
        status_data = {
            "state": "training",
            "iteration": 10,
            "message": "Test status"
        }
        
        message = MessageFactory.create_status(status_data, "test_service")
        await topic_manager.publish_message(topics.TRAINING_STATUS, message)
        
        # Verify message was published
        mock_session = session_manager.session
        assert len(mock_session.published_messages) > 0
        
        # Check message content
        topic, data = mock_session.published_messages[-1]
        assert topic == topics.TRAINING_STATUS
    
    @pytest.mark.asyncio
    async def test_service_coordination_patterns(self, session_manager):
        """Test coordination patterns between services"""
        topic_manager = TopicManager(session_manager)
        
        # Test command-response pattern
        await topic_manager.register_topic(topics.TRAINING_COMMAND)
        await topic_manager.create_publisher(topics.TRAINING_COMMAND)
        
        # Simulate training command
        command = TrainingCommand(command="start")
        command_message = MessageFactory.create_data(command.to_dict(), "coordinator")
        
        await topic_manager.publish_message(topics.TRAINING_COMMAND, command_message)
        
        # Test status publishing pattern  
        await topic_manager.register_topic(topics.TRAINING_STATUS)
        await topic_manager.create_publisher(topics.TRAINING_STATUS)
        
        status_data = {
            "state": "training",
            "message": "Training started in response to command"
        }
        status_message = MessageFactory.create_status(status_data, "training_service")
        await topic_manager.publish_message(topics.TRAINING_STATUS, status_message)
        
        # Verify coordination messages were published
        mock_session = session_manager.session
        published_topics = [topic for topic, _ in mock_session.published_messages]
        
        assert topics.TRAINING_COMMAND in published_topics
        assert topics.TRAINING_STATUS in published_topics
    
    @pytest.mark.asyncio
    async def test_error_handling_patterns(self):
        """Test error handling in service components"""
        from zenoh_services.core.data_models import ErrorInfo
        
        # Test error info creation
        error = ErrorInfo(
            error_type="ServiceError",
            error_message="Test error message",
            severity="error"
        )
        
        assert error.validate() is True
        assert error.resolved is False
        
        # Test serialization
        error_dict = error.to_dict()
        restored_error = ErrorInfo.from_dict(error_dict)
        assert restored_error.error_message == "Test error message"
    
    @pytest.mark.asyncio
    async def test_configuration_validation(self):
        """Test configuration validation"""
        from zenoh_services.core.data_models import TrainingConfig, PlayConfig
        
        # Test valid training config
        valid_training_config = TrainingConfig(
            task_name="test_task",
            num_envs=4,
            max_iterations=100
        )
        assert valid_training_config.validate() is True
        
        # Test invalid training config
        invalid_training_config = TrainingConfig(
            task_name="",  # Empty task name
            num_envs=0,    # Zero environments
            max_iterations=-1  # Negative iterations
        )
        assert invalid_training_config.validate() is False
        
        # Test valid play config
        valid_play_config = PlayConfig(
            task_name="test_task",
            num_envs=1
        )
        assert valid_play_config.validate() is True


class TestServiceMetrics:
    """Test service metrics collection"""
    
    @pytest.mark.asyncio
    async def test_training_metrics_structure(self):
        """Test training metrics data structure"""
        from zenoh_services.core.data_models import TrainingMetrics
        
        metrics = TrainingMetrics(
            iteration=10,
            mean_reward=100.5,
            std_reward=15.2,
            mean_episode_length=500.0,
            learning_rate=3e-4,
            entropy=0.5,
            kl_divergence=0.01,
            policy_loss=-0.05,
            value_loss=0.1,
            timestamp=time.time()
        )
        
        assert metrics.validate() is True
        
        # Test serialization
        metrics_dict = metrics.to_dict()
        assert "iteration" in metrics_dict
        assert "mean_reward" in metrics_dict
        assert "timestamp" in metrics_dict
        
        # Test deserialization
        restored_metrics = TrainingMetrics.from_dict(metrics_dict)
        assert restored_metrics.iteration == 10
        assert restored_metrics.mean_reward == 100.5
    
    @pytest.mark.asyncio
    async def test_robot_state_structure(self):
        """Test robot state data structure"""
        from zenoh_services.services.play_service import RobotStateData
        
        robot_state = RobotStateData(
            joint_positions=[0.1, 0.2, 0.3],
            joint_velocities=[0.01, 0.02, 0.03],
            joint_torques=[1.0, 2.0, 3.0],
            base_position=[0.0, 0.0, 0.5],
            base_linear_velocity=[0.5, 0.0, 0.0],
            velocity_command=[0.5, 0.0, 0.0],
            contact_forces=[[0, 0, 10], [0, 0, 12]],
            env_index=0,
            timestamp=time.time()
        )
        
        # Test serialization
        state_dict = robot_state.to_dict()
        assert "joint_positions" in state_dict
        assert "base_position" in state_dict
        assert "timestamp" in state_dict
        
        # Test deserialization  
        restored_state = RobotStateData.from_dict(state_dict)
        assert restored_state.joint_positions == [0.1, 0.2, 0.3]
        assert restored_state.env_index == 0


if __name__ == "__main__":
    pytest.main([__file__, "-v"])