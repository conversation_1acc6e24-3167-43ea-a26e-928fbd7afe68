"""
Fault Simulation Framework
Provides tools for simulating various types of failures and testing recovery mechanisms.
"""

import asyncio
import logging
import time
import random
import threading
from typing import Dict, Any, Optional, Callable, List, Union
from dataclasses import dataclass, field
from enum import Enum
import psutil
import gc
import signal
import os

from zenoh_services.core.error_handler import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ErrorContext, Error<PERSON>ate<PERSON><PERSON>, <PERSON><PERSON>r<PERSON><PERSON><PERSON>, get_error_handler
)
from zenoh_services.core.fault_detection import (
    FaultDetectionSystem, ServiceMonitor, get_fault_detection_system
)


class SimulationMode(Enum):
    """Fault simulation modes"""
    IMMEDIATE = "immediate"  # Fault occurs immediately
    GRADUAL = "gradual"      # Fault develops over time
    INTERMITTENT = "intermittent"  # Fault occurs sporadically
    CASCADING = "cascading"  # Fault causes other faults


class FaultType(Enum):
    """Types of faults to simulate"""
    NETWORK_FAILURE = "network_failure"
    MEMORY_LEAK = "memory_leak"
    CPU_SPIKE = "cpu_spike"
    DISK_FULL = "disk_full"
    SERVICE_CRASH = "service_crash"
    DATABASE_CONNECTION = "database_connection"
    ZENOH_SESSION_LOSS = "zenoh_session_loss"
    TIMEOUT = "timeout"
    DEADLOCK = "deadlock"
    RESOURCE_CONTENTION = "resource_contention"


@dataclass
class FaultScenario:
    """Definition of a fault scenario"""
    name: str
    fault_type: FaultType
    simulation_mode: SimulationMode
    target_service: str
    duration: float = 60.0  # seconds
    intensity: float = 1.0  # 0.0 to 1.0
    parameters: Dict[str, Any] = field(default_factory=dict)
    recovery_expected: bool = True
    cascading_services: List[str] = field(default_factory=list)


class FaultSimulator:
    """Simulates various types of faults for testing"""
    
    def __init__(self, error_handler: Optional[ErrorHandler] = None,
                 fault_detection: Optional[FaultDetectionSystem] = None):
        self.error_handler = error_handler or get_error_handler()
        self.fault_detection = fault_detection or get_fault_detection_system()
        self.logger = logging.getLogger("fault_simulator")
        
        # Active simulations
        self.active_simulations: Dict[str, asyncio.Task] = {}
        self.simulation_results: Dict[str, Dict[str, Any]] = {}
        
        # Resource tracking for cleanup
        self.memory_hogs: List[Any] = []
        self.cpu_burners: List[threading.Thread] = []
        
        # Callbacks
        self.fault_callbacks: List[Callable[[str, FaultScenario], None]] = []
        self.recovery_callbacks: List[Callable[[str, bool], None]] = []
    
    def add_fault_callback(self, callback: Callable[[str, FaultScenario], None]):
        """Add callback for fault events"""
        self.fault_callbacks.append(callback)
    
    def add_recovery_callback(self, callback: Callable[[str, bool], None]):
        """Add callback for recovery events"""
        self.recovery_callbacks.append(callback)
    
    async def run_scenario(self, scenario: FaultScenario) -> Dict[str, Any]:
        """Run a fault simulation scenario"""
        scenario_id = f"{scenario.name}_{int(time.time())}"
        
        self.logger.info(f"Starting fault simulation: {scenario.name}")
        
        # Record start time
        start_time = time.time()
        
        # Initialize result tracking
        result = {
            "scenario_id": scenario_id,
            "scenario_name": scenario.name,
            "fault_type": scenario.fault_type.value,
            "target_service": scenario.target_service,
            "start_time": start_time,
            "end_time": None,
            "duration": scenario.duration,
            "success": False,
            "errors_generated": [],
            "recovery_attempts": [],
            "final_status": "unknown"
        }
        
        try:
            # Notify callbacks
            for callback in self.fault_callbacks:
                callback(scenario_id, scenario)
            
            # Start fault simulation
            simulation_task = asyncio.create_task(
                self._execute_fault_simulation(scenario, result)
            )
            self.active_simulations[scenario_id] = simulation_task
            
            # Wait for completion
            await simulation_task
            
            result["success"] = True
            result["final_status"] = "completed"
            
        except Exception as e:
            self.logger.error(f"Fault simulation failed: {e}")
            result["final_status"] = f"failed: {e}"
            
        finally:
            # Record end time
            result["end_time"] = time.time()
            result["actual_duration"] = result["end_time"] - result["start_time"]
            
            # Clean up
            if scenario_id in self.active_simulations:
                del self.active_simulations[scenario_id]
            
            self.simulation_results[scenario_id] = result
            
            self.logger.info(f"Fault simulation completed: {scenario.name}")
        
        return result
    
    async def _execute_fault_simulation(self, scenario: FaultScenario, result: Dict[str, Any]):
        """Execute the actual fault simulation"""
        
        if scenario.simulation_mode == SimulationMode.IMMEDIATE:
            await self._simulate_immediate_fault(scenario, result)
        elif scenario.simulation_mode == SimulationMode.GRADUAL:
            await self._simulate_gradual_fault(scenario, result)
        elif scenario.simulation_mode == SimulationMode.INTERMITTENT:
            await self._simulate_intermittent_fault(scenario, result)
        elif scenario.simulation_mode == SimulationMode.CASCADING:
            await self._simulate_cascading_fault(scenario, result)
    
    async def _simulate_immediate_fault(self, scenario: FaultScenario, result: Dict[str, Any]):
        """Simulate immediate fault"""
        self.logger.info(f"Simulating immediate {scenario.fault_type.value} fault")
        
        # Generate fault immediately
        await self._generate_fault(scenario, result)
        
        # Monitor for recovery
        recovery_start = time.time()
        recovery_timeout = scenario.duration
        
        while time.time() - recovery_start < recovery_timeout:
            if await self._check_recovery(scenario):
                recovery_time = time.time() - recovery_start
                result["recovery_attempts"].append({
                    "attempt": len(result["recovery_attempts"]) + 1,
                    "success": True,
                    "recovery_time": recovery_time
                })
                self._notify_recovery(scenario.name, True)
                break
            await asyncio.sleep(1.0)
        else:
            result["recovery_attempts"].append({
                "attempt": len(result["recovery_attempts"]) + 1,
                "success": False,
                "timeout": True
            })
            self._notify_recovery(scenario.name, False)
        
        # Clean up fault effects
        await self._cleanup_fault(scenario)
    
    async def _simulate_gradual_fault(self, scenario: FaultScenario, result: Dict[str, Any]):
        """Simulate gradual fault development"""
        self.logger.info(f"Simulating gradual {scenario.fault_type.value} fault")
        
        steps = 10
        step_duration = scenario.duration / steps
        
        for step in range(steps):
            intensity = (step + 1) / steps * scenario.intensity
            
            # Create scenario with current intensity
            step_scenario = FaultScenario(
                name=f"{scenario.name}_step_{step}",
                fault_type=scenario.fault_type,
                simulation_mode=SimulationMode.IMMEDIATE,
                target_service=scenario.target_service,
                intensity=intensity,
                parameters=scenario.parameters.copy()
            )
            
            await self._generate_fault(step_scenario, result)
            await asyncio.sleep(step_duration)
            
            # Check if recovery occurred
            if await self._check_recovery(scenario):
                recovery_time = (step + 1) * step_duration
                result["recovery_attempts"].append({
                    "attempt": len(result["recovery_attempts"]) + 1,
                    "success": True,
                    "recovery_time": recovery_time,
                    "recovery_step": step + 1
                })
                self._notify_recovery(scenario.name, True)
                break
        
        await self._cleanup_fault(scenario)
    
    async def _simulate_intermittent_fault(self, scenario: FaultScenario, result: Dict[str, Any]):
        """Simulate intermittent fault"""
        self.logger.info(f"Simulating intermittent {scenario.fault_type.value} fault")
        
        end_time = time.time() + scenario.duration
        fault_active = False
        
        while time.time() < end_time:
            if not fault_active:
                # Start fault
                if random.random() < 0.3:  # 30% chance to start fault
                    await self._generate_fault(scenario, result)
                    fault_active = True
                    self.logger.debug("Intermittent fault activated")
            else:
                # Check if fault should end
                if random.random() < 0.4:  # 40% chance to end fault
                    await self._cleanup_fault(scenario)
                    fault_active = False
                    self.logger.debug("Intermittent fault deactivated")
            
            await asyncio.sleep(2.0)
        
        # Ensure cleanup
        if fault_active:
            await self._cleanup_fault(scenario)
    
    async def _simulate_cascading_fault(self, scenario: FaultScenario, result: Dict[str, Any]):
        """Simulate cascading fault"""
        self.logger.info(f"Simulating cascading {scenario.fault_type.value} fault")
        
        # Start with primary service
        await self._generate_fault(scenario, result)
        
        # Cascade to dependent services
        cascade_delay = 5.0  # seconds between cascades
        for dependent_service in scenario.cascading_services:
            await asyncio.sleep(cascade_delay)
            
            # Create cascading fault scenario
            cascade_scenario = FaultScenario(
                name=f"{scenario.name}_cascade_{dependent_service}",
                fault_type=scenario.fault_type,
                simulation_mode=SimulationMode.IMMEDIATE,
                target_service=dependent_service,
                intensity=scenario.intensity * 0.8,  # Slightly reduced intensity
                parameters=scenario.parameters.copy()
            )
            
            await self._generate_fault(cascade_scenario, result)
        
        # Monitor for system-wide recovery
        recovery_timeout = scenario.duration
        await asyncio.sleep(recovery_timeout)
        
        await self._cleanup_fault(scenario)
    
    async def _generate_fault(self, scenario: FaultScenario, result: Dict[str, Any]):
        """Generate specific type of fault"""
        
        try:
            if scenario.fault_type == FaultType.NETWORK_FAILURE:
                await self._simulate_network_failure(scenario)
            elif scenario.fault_type == FaultType.MEMORY_LEAK:
                await self._simulate_memory_leak(scenario)
            elif scenario.fault_type == FaultType.CPU_SPIKE:
                await self._simulate_cpu_spike(scenario)
            elif scenario.fault_type == FaultType.SERVICE_CRASH:
                await self._simulate_service_crash(scenario)
            elif scenario.fault_type == FaultType.ZENOH_SESSION_LOSS:
                await self._simulate_zenoh_session_loss(scenario)
            elif scenario.fault_type == FaultType.TIMEOUT:
                await self._simulate_timeout(scenario)
            elif scenario.fault_type == FaultType.DEADLOCK:
                await self._simulate_deadlock(scenario)
            else:
                self.logger.warning(f"Unknown fault type: {scenario.fault_type}")
            
            result["errors_generated"].append({
                "timestamp": time.time(),
                "fault_type": scenario.fault_type.value,
                "target_service": scenario.target_service,
                "intensity": scenario.intensity
            })
            
        except Exception as e:
            self.logger.error(f"Error generating fault: {e}")
            raise
    
    async def _simulate_network_failure(self, scenario: FaultScenario):
        """Simulate network failure"""
        # Create network-related exception
        if scenario.intensity > 0.7:
            exception = ConnectionError("Network unreachable")
            category = ErrorCategory.NETWORK
            severity = ErrorSeverity.CRITICAL
        else:
            exception = TimeoutError("Network timeout")
            category = ErrorCategory.NETWORK
            severity = ErrorSeverity.HIGH
        
        context = ErrorContext(
            service_name=scenario.target_service,
            service_state="connecting",
            timestamp=time.time(),
            thread_id=threading.get_ident(),
            function_name="network_operation"
        )
        
        await self.error_handler.handle_error(exception, context, category, severity)
    
    async def _simulate_memory_leak(self, scenario: FaultScenario):
        """Simulate memory leak"""
        # Allocate memory that won't be cleaned up immediately
        leak_size = int(scenario.intensity * 100 * 1024 * 1024)  # MB
        
        try:
            # Create memory leak
            memory_hog = bytearray(leak_size)
            self.memory_hogs.append(memory_hog)
            
            # Generate error about memory issues
            exception = MemoryError("Memory allocation failed")
            context = ErrorContext(
                service_name=scenario.target_service,
                service_state="processing",
                timestamp=time.time(),
                thread_id=threading.get_ident(),
                function_name="allocate_memory"
            )
            
            await self.error_handler.handle_error(
                exception, context, ErrorCategory.RESOURCE, ErrorSeverity.HIGH
            )
            
        except MemoryError:
            # If we can't allocate, just simulate the error
            exception = MemoryError("Simulated memory exhaustion")
            context = ErrorContext(
                service_name=scenario.target_service,
                service_state="processing",
                timestamp=time.time(),
                thread_id=threading.get_ident(),
                function_name="simulate_memory_leak"
            )
            
            await self.error_handler.handle_error(
                exception, context, ErrorCategory.RESOURCE, ErrorSeverity.HIGH
            )
    
    async def _simulate_cpu_spike(self, scenario: FaultScenario):
        """Simulate CPU spike"""
        def cpu_burner():
            """CPU-intensive function"""
            end_time = time.time() + (scenario.intensity * 10)
            while time.time() < end_time:
                # Busy loop
                for _ in range(100000):
                    pass
        
        # Start CPU burning threads
        num_threads = max(1, int(scenario.intensity * psutil.cpu_count()))
        for _ in range(num_threads):
            thread = threading.Thread(target=cpu_burner, daemon=True)
            self.cpu_burners.append(thread)
            thread.start()
        
        # Generate performance degradation error
        exception = RuntimeError("Performance degradation detected")
        context = ErrorContext(
            service_name=scenario.target_service,
            service_state="processing",
            timestamp=time.time(),
            thread_id=threading.get_ident(),
            function_name="cpu_intensive_operation"
        )
        
        await self.error_handler.handle_error(
            exception, context, ErrorCategory.RESOURCE, ErrorSeverity.MEDIUM
        )
    
    async def _simulate_service_crash(self, scenario: FaultScenario):
        """Simulate service crash"""
        exception = RuntimeError("Service has crashed")
        context = ErrorContext(
            service_name=scenario.target_service,
            service_state="crashed",
            timestamp=time.time(),
            thread_id=threading.get_ident(),
            function_name="service_main_loop"
        )
        
        await self.error_handler.handle_error(
            exception, context, ErrorCategory.SERVICE, ErrorSeverity.CRITICAL
        )
    
    async def _simulate_zenoh_session_loss(self, scenario: FaultScenario):
        """Simulate Zenoh session loss"""
        exception = ConnectionError("Zenoh session lost")
        context = ErrorContext(
            service_name=scenario.target_service,
            service_state="disconnected",
            timestamp=time.time(),
            thread_id=threading.get_ident(),
            function_name="zenoh_session_check"
        )
        
        await self.error_handler.handle_error(
            exception, context, ErrorCategory.ZENOH, ErrorSeverity.HIGH
        )
    
    async def _simulate_timeout(self, scenario: FaultScenario):
        """Simulate timeout"""
        exception = TimeoutError("Operation timed out")
        context = ErrorContext(
            service_name=scenario.target_service,
            service_state="timeout",
            timestamp=time.time(),
            thread_id=threading.get_ident(),
            function_name="long_running_operation"
        )
        
        await self.error_handler.handle_error(
            exception, context, ErrorCategory.SERVICE, ErrorSeverity.MEDIUM
        )
    
    async def _simulate_deadlock(self, scenario: FaultScenario):
        """Simulate deadlock"""
        exception = RuntimeError("Deadlock detected")
        context = ErrorContext(
            service_name=scenario.target_service,
            service_state="deadlocked",
            timestamp=time.time(),
            thread_id=threading.get_ident(),
            function_name="acquire_multiple_locks"
        )
        
        await self.error_handler.handle_error(
            exception, context, ErrorCategory.SERVICE, ErrorSeverity.HIGH
        )
    
    async def _check_recovery(self, scenario: FaultScenario) -> bool:
        """Check if recovery has occurred"""
        # Check service health
        service_monitor = self.fault_detection.get_service_monitor(scenario.target_service)
        if service_monitor:
            return service_monitor.last_health_status.name in ["HEALTHY", "DEGRADED"]
        
        # If no monitor, assume recovery based on time (simplified)
        return random.random() < 0.3  # 30% chance of recovery
    
    async def _cleanup_fault(self, scenario: FaultScenario):
        """Clean up fault simulation effects"""
        # Clean up memory leaks
        if self.memory_hogs:
            del self.memory_hogs[:]
            gc.collect()
        
        # CPU threads will naturally end
        self.cpu_burners.clear()
        
        self.logger.debug(f"Cleaned up fault simulation for {scenario.target_service}")
    
    def _notify_recovery(self, scenario_name: str, success: bool):
        """Notify recovery callbacks"""
        for callback in self.recovery_callbacks:
            try:
                callback(scenario_name, success)
            except Exception as e:
                self.logger.error(f"Error in recovery callback: {e}")
    
    async def run_stress_test(self, scenarios: List[FaultScenario], 
                             concurrent: bool = False) -> List[Dict[str, Any]]:
        """Run multiple fault scenarios as a stress test"""
        results = []
        
        if concurrent:
            # Run scenarios concurrently
            tasks = [self.run_scenario(scenario) for scenario in scenarios]
            results = await asyncio.gather(*tasks, return_exceptions=True)
        else:
            # Run scenarios sequentially
            for scenario in scenarios:
                result = await self.run_scenario(scenario)
                results.append(result)
                
                # Brief pause between scenarios
                await asyncio.sleep(2.0)
        
        return results
    
    def get_simulation_results(self) -> Dict[str, Dict[str, Any]]:
        """Get all simulation results"""
        return self.simulation_results.copy()
    
    def stop_all_simulations(self):
        """Stop all active simulations"""
        for scenario_id, task in self.active_simulations.items():
            if not task.done():
                task.cancel()
                self.logger.info(f"Stopped simulation: {scenario_id}")
        
        self.active_simulations.clear()
    
    async def cleanup(self):
        """Clean up all simulation resources"""
        self.stop_all_simulations()
        
        # Clean up memory leaks
        if self.memory_hogs:
            del self.memory_hogs[:]
            gc.collect()
        
        # CPU threads will naturally end
        self.cpu_burners.clear()
        
        self.logger.info("Fault simulator cleanup complete")


# Predefined fault scenarios for common testing
def get_predefined_scenarios() -> List[FaultScenario]:
    """Get predefined fault scenarios for testing"""
    return [
        FaultScenario(
            name="network_outage",
            fault_type=FaultType.NETWORK_FAILURE,
            simulation_mode=SimulationMode.IMMEDIATE,
            target_service="network_service",
            duration=30.0,
            intensity=1.0
        ),
        FaultScenario(
            name="gradual_memory_leak",
            fault_type=FaultType.MEMORY_LEAK,
            simulation_mode=SimulationMode.GRADUAL,
            target_service="training_service",
            duration=60.0,
            intensity=0.8
        ),
        FaultScenario(
            name="intermittent_zenoh_loss",
            fault_type=FaultType.ZENOH_SESSION_LOSS,
            simulation_mode=SimulationMode.INTERMITTENT,
            target_service="zenoh_service",
            duration=90.0,
            intensity=0.7
        ),
        FaultScenario(
            name="cascading_service_failure",
            fault_type=FaultType.SERVICE_CRASH,
            simulation_mode=SimulationMode.CASCADING,
            target_service="training_service",
            duration=120.0,
            intensity=1.0,
            cascading_services=["play_service", "simulation_service", "deployment_service"]
        ),
        FaultScenario(
            name="cpu_spike_stress",
            fault_type=FaultType.CPU_SPIKE,
            simulation_mode=SimulationMode.IMMEDIATE,
            target_service="computation_service",
            duration=45.0,
            intensity=0.9
        )
    ]


# Example usage function
async def run_fault_simulation_demo():
    """Demonstration of fault simulation system"""
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger("fault_simulation_demo")
    
    # Initialize systems
    error_handler = get_error_handler()
    fault_detection = get_fault_detection_system()
    simulator = FaultSimulator(error_handler, fault_detection)
    
    # Add callbacks
    def on_fault(scenario_id: str, scenario: FaultScenario):
        logger.info(f"Fault simulation started: {scenario.name}")
    
    def on_recovery(scenario_name: str, success: bool):
        logger.info(f"Recovery {'succeeded' if success else 'failed'} for {scenario_name}")
    
    simulator.add_fault_callback(on_fault)
    simulator.add_recovery_callback(on_recovery)
    
    # Get predefined scenarios
    scenarios = get_predefined_scenarios()
    
    # Run stress test
    logger.info("Starting fault simulation stress test...")
    results = await simulator.run_stress_test(scenarios[:3], concurrent=False)
    
    # Print results
    for result in results:
        logger.info(f"Scenario '{result['scenario_name']}': {result['final_status']}")
        logger.info(f"  Duration: {result.get('actual_duration', 0):.1f}s")
        logger.info(f"  Errors: {len(result.get('errors_generated', []))}")
        logger.info(f"  Recovery attempts: {len(result.get('recovery_attempts', []))}")
    
    # Cleanup
    await simulator.cleanup()
    logger.info("Fault simulation demo complete")


if __name__ == "__main__":
    asyncio.run(run_fault_simulation_demo())