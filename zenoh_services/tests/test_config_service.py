"""
Unit tests for configuration management service
"""

import asyncio
import pytest
import tempfile
import os
import json
import time
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from pathlib import Path

from zenoh_services.services.config_service import (
    ConfigurationService, ConfigServiceConfig, ConfigServiceState,
    ConfigurationVersion, ConfigurationConflict, ConfigurationSnapshot,
    ConfigurationStatus, ConflictResolutionStrategy
)
from zenoh_services.core.data_models import (
    TrainingConfig, SimulationConfig, DeploymentConfig, ConfigurationRequest
)
from zenoh_services.core.enhanced_session_manager import EnhancedZenohSessionManager


class TestConfigurationService:
    """Test cases for ConfigurationService"""
    
    @pytest.fixture
    async def mock_session_manager(self):
        """Create a mock session manager"""
        manager = Mock(spec=EnhancedZenohSessionManager)
        manager.initialize = AsyncMock(return_value=True)
        manager.shutdown = AsyncMock()
        return manager
    
    @pytest.fixture
    def config_service_config(self):
        """Create a test configuration service config"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config = ConfigServiceConfig(
                config_storage_path=os.path.join(temp_dir, "configs"),
                version_storage_path=os.path.join(temp_dir, "versions"),
                backup_path=os.path.join(temp_dir, "backups"),
                template_path=os.path.join(temp_dir, "templates"),
                auto_backup_enabled=False,  # Disable for tests
                max_versions_per_config=10,
                auto_cleanup_days=1
            )
            yield config
    
    @pytest.fixture
    async def config_service(self, mock_session_manager, config_service_config):
        """Create a configuration service instance"""
        with patch('zenoh_services.services.config_service.TopicManager'):
            service = ConfigurationService(mock_session_manager, config_service_config)
            
            # Mock the topic manager
            service.topic_manager = Mock()
            service.topic_manager.register_topic = AsyncMock()
            service.topic_manager.create_publisher = AsyncMock()
            service.topic_manager.create_subscriber = AsyncMock()
            service.topic_manager.publish_message = AsyncMock()
            
            yield service
    
    @pytest.mark.asyncio
    async def test_config_service_initialization(self, config_service):
        """Test configuration service initialization"""
        # Test successful initialization
        assert await config_service.initialize()
        assert config_service.is_initialized
        assert config_service.state == ConfigServiceState.IDLE
        
        # Check that storage directories are created
        assert os.path.exists(config_service.config.config_storage_path)
        assert os.path.exists(config_service.config.version_storage_path)
        assert os.path.exists(config_service.config.backup_path)
        assert os.path.exists(config_service.config.template_path)
        
        # Check that default templates are created
        assert len(config_service.templates) > 0
        assert "training_default" in config_service.templates
        assert "simulation_default" in config_service.templates
        assert "deployment_default" in config_service.templates
    
    @pytest.mark.asyncio
    async def test_set_and_get_configuration(self, config_service):
        """Test setting and getting configurations"""
        await config_service.initialize()
        
        # Create test configuration
        training_config = {
            "task_name": "test_task",
            "num_envs": 2048,
            "max_iterations": 1000,
            "learning_rate": 3e-4,
            "experiment_name": "test_experiment"
        }
        
        # Set configuration
        version_id = await config_service.set_configuration(
            "training", "test_config", training_config,
            author="test", comment="Test configuration"
        )
        
        assert version_id is not None
        
        # Get configuration
        retrieved_config = await config_service.get_configuration("training", "test_config")
        
        assert retrieved_config == training_config
        assert config_service.configurations["training"]["test_config"] == training_config
        
        # Check version was created
        config_key = "training/test_config"
        assert config_key in config_service.versions
        assert len(config_service.versions[config_key]) == 1
        assert config_service.active_versions[config_key] == version_id
    
    @pytest.mark.asyncio
    async def test_configuration_validation(self, config_service):
        """Test configuration validation"""
        await config_service.initialize()
        
        # Test valid training configuration
        valid_training_config = {
            "task_name": "anymal_c_flat",
            "num_envs": 4096,
            "max_iterations": 1500,
            "learning_rate": 3e-4,
            "batch_size": 24576,
            "experiment_name": "test_experiment",
            "run_name": "test_run",
            "resume": False,
            "checkpoint": -1
        }
        
        validation_result = await config_service._validate_configuration("training", valid_training_config)
        assert validation_result["is_valid"] is True
        assert len(validation_result["errors"]) == 0
        
        # Test invalid training configuration
        invalid_training_config = {
            "task_name": "",  # Invalid empty task name
            "num_envs": -1,   # Invalid negative environments
            "max_iterations": 0,  # Invalid zero iterations
            "learning_rate": -0.1,  # Invalid negative learning rate
        }
        
        validation_result = await config_service._validate_configuration("training", invalid_training_config)
        assert validation_result["is_valid"] is False
        assert len(validation_result["errors"]) > 0
    
    @pytest.mark.asyncio
    async def test_version_control(self, config_service):
        """Test configuration version control"""
        await config_service.initialize()
        
        config_type = "training"
        config_name = "version_test"
        
        # Create initial configuration
        config_v1 = {"task_name": "task_v1", "num_envs": 1024}
        version_1 = await config_service.set_configuration(
            config_type, config_name, config_v1, 
            author="test", comment="Version 1"
        )
        
        # Create second version
        config_v2 = {"task_name": "task_v2", "num_envs": 2048}
        version_2 = await config_service.set_configuration(
            config_type, config_name, config_v2,
            author="test", comment="Version 2"
        )
        
        # Check version history
        config_key = f"{config_type}/{config_name}"
        versions = config_service.versions[config_key]
        
        assert len(versions) == 2
        assert versions[0].version_number == 1
        assert versions[1].version_number == 2
        assert versions[1].is_active is True
        assert versions[0].is_active is False
        
        # Test getting specific version
        v1_data = await config_service.get_configuration(config_type, config_name, version_1)
        assert v1_data["task_name"] == "task_v1"
        assert v1_data["num_envs"] == 1024
        
        v2_data = await config_service.get_configuration(config_type, config_name, version_2)
        assert v2_data["task_name"] == "task_v2"
        assert v2_data["num_envs"] == 2048
        
        # Test getting active version (should be v2)
        active_data = await config_service.get_configuration(config_type, config_name)
        assert active_data == v2_data
    
    @pytest.mark.asyncio
    async def test_conflict_detection(self, config_service):
        """Test configuration conflict detection"""
        await config_service.initialize()
        
        # Set initial configuration
        initial_config = {
            "task_name": "test_task",
            "num_envs": 4096,
            "max_iterations": 1000,
            "learning_rate": 3e-4
        }
        
        await config_service.set_configuration(
            "training", "conflict_test", initial_config,
            author="user1", comment="Initial config"
        )
        
        # Create conflicting configuration
        conflicting_config = {
            "task_name": "different_task",  # Conflict
            "num_envs": 2048,              # Conflict
            "max_iterations": 1000,        # Same
            "learning_rate": 1e-3          # Conflict
        }
        
        # Test conflict detection
        conflict = await config_service._detect_configuration_conflict(
            "training/conflict_test", initial_config, conflicting_config
        )
        
        assert conflict is not None
        assert len(conflict.conflicted_fields) > 0
        assert "task_name" in conflict.conflicted_fields
        assert "num_envs" in conflict.conflicted_fields
        assert "learning_rate" in conflict.conflicted_fields
        assert conflict.conflict_severity in ["medium", "high", "critical"]
    
    @pytest.mark.asyncio
    async def test_conflict_resolution_merge(self, config_service):
        """Test automatic conflict resolution through merging"""
        await config_service.initialize()
        config_service.config.default_conflict_strategy = ConflictResolutionStrategy.MERGE
        
        # Set initial configuration
        initial_config = {
            "task_name": "original_task",
            "num_envs": 4096,
            "max_iterations": 1000,
            "learning_rate": 3e-4,
            "batch_size": 24576
        }
        
        await config_service.set_configuration(
            "training", "merge_test", initial_config,
            author="user1", comment="Initial config"
        )
        
        # Create configuration with non-critical conflicts
        new_config = {
            "task_name": "original_task",  # Same (no conflict)
            "num_envs": 2048,             # Different but non-critical
            "max_iterations": 1500,       # Different but non-critical
            "learning_rate": 1e-3,        # Different but non-critical
            "batch_size": 32768,          # New value
            "new_parameter": "test"       # New parameter
        }
        
        # Set configuration with merge strategy
        version_id = await config_service.set_configuration(
            "training", "merge_test", new_config,
            author="user2", comment="Updated config"
        )
        
        # Should succeed due to merge strategy
        assert version_id is not None
        
        # Check merged configuration
        merged_config = await config_service.get_configuration("training", "merge_test")
        
        # Critical fields should remain unchanged
        assert merged_config["task_name"] == "original_task"
        
        # Non-critical fields should be updated
        assert merged_config["num_envs"] == 2048
        assert merged_config["max_iterations"] == 1500
        assert merged_config["new_parameter"] == "test"
    
    @pytest.mark.asyncio
    async def test_configuration_templates(self, config_service):
        """Test configuration template functionality"""
        await config_service.initialize()
        
        # Check default templates exist
        assert "training_default" in config_service.templates
        
        # Apply template with variables
        request_data = {
            "template_name": "training_default",
            "config_type": "training",
            "config_name": "from_template",
            "variables": {
                "TASK_NAME": "custom_task",
                "EXPERIMENT_NAME": "custom_experiment",
                "RUN_NAME": "custom_run"
            }
        }
        
        request = ConfigurationRequest(
            config_type="training",
            action="apply_template",
            config_data=request_data
        )
        
        await config_service._handle_apply_template(request)
        
        # Check that configuration was created from template
        template_config = await config_service.get_configuration("training", "from_template")
        
        assert template_config["task_name"] == "custom_task"
        assert template_config["experiment_name"] == "custom_experiment"
        assert template_config["run_name"] == "custom_run"
        
        # Other template values should be preserved
        assert template_config["num_envs"] == 4096
        assert template_config["max_iterations"] == 1500
    
    @pytest.mark.asyncio
    async def test_configuration_snapshots(self, config_service):
        """Test configuration snapshot functionality"""
        await config_service.initialize()
        
        # Create some configurations
        await config_service.set_configuration("training", "default", {
            "task_name": "test_task", 
            "num_envs": 4096
        })
        
        await config_service.set_configuration("simulation", "default", {
            "terrain": {"mesh_type": "plane"},
            "physics": {"dt": 0.005},
            "robot": {"name": "anymal_c"}
        })
        
        # Create snapshot
        request_data = {
            "snapshot_name": "test_snapshot",
            "description": "Test snapshot for unit tests"
        }
        
        request = ConfigurationRequest(
            config_type="system",
            action="create_snapshot",
            config_data=request_data
        )
        
        await config_service._handle_create_snapshot(request)
        
        # Check snapshot was created
        assert len(config_service.snapshots) == 1
        
        snapshot = list(config_service.snapshots.values())[0]
        assert snapshot.snapshot_name == "test_snapshot"
        assert snapshot.description == "Test snapshot for unit tests"
        assert snapshot.training_config_version is not None
        assert snapshot.simulation_config_version is not None
    
    @pytest.mark.asyncio
    async def test_configuration_persistence(self, config_service):
        """Test configuration persistence and loading"""
        await config_service.initialize()
        
        # Create test configuration
        test_config = {
            "task_name": "persistence_test",
            "num_envs": 1024,
            "max_iterations": 500
        }
        
        # Set configuration (should persist automatically)
        version_id = await config_service.set_configuration(
            "training", "persist_test", test_config,
            author="test", comment="Persistence test"
        )
        
        # Check files were created
        config_file = Path(config_service.config.config_storage_path) / "training" / "persist_test.json"
        assert config_file.exists()
        
        # Check version files were created
        config_key = "training/persist_test"
        versions_dir = Path(config_service.config.version_storage_path) / "training_persist_test"
        assert versions_dir.exists()
        
        version_files = list(versions_dir.glob("version_*.json"))
        data_files = list(versions_dir.glob("data_*.json"))
        
        assert len(version_files) > 0
        assert len(data_files) > 0 or len(list(versions_dir.glob("data_*.json.gz"))) > 0
        
        # Create new service instance and load persisted data
        new_service = ConfigurationService(config_service.session_manager, config_service.config)
        new_service.topic_manager = config_service.topic_manager
        
        await new_service.initialize()
        
        # Check that configuration was loaded
        loaded_config = await new_service.get_configuration("training", "persist_test")
        assert loaded_config == test_config
        
        # Check that version history was loaded
        assert config_key in new_service.versions
        assert len(new_service.versions[config_key]) == 1
        assert new_service.active_versions[config_key] == version_id
    
    @pytest.mark.asyncio
    async def test_cache_functionality(self, config_service):
        """Test configuration caching"""
        await config_service.initialize()
        
        # Set configuration
        test_config = {"task_name": "cache_test", "num_envs": 512}
        
        await config_service.set_configuration(
            "training", "cache_test", test_config
        )
        
        # First get - should cache the result
        config1 = await config_service.get_configuration("training", "cache_test")
        
        # Check cache was populated
        cache_key = "training/cache_test:active"
        assert cache_key in config_service.config_cache
        
        # Second get - should use cache
        config2 = await config_service.get_configuration("training", "cache_test")
        
        assert config1 == config2
        
        # Update configuration - should clear cache
        updated_config = {"task_name": "cache_test", "num_envs": 1024}
        
        await config_service.set_configuration(
            "training", "cache_test", updated_config
        )
        
        # Cache should be cleared for this config
        cache_keys = [key for key in config_service.config_cache.keys() if key.startswith("training/cache_test")]
        assert len(cache_keys) == 0 or all(
            time.time() - config_service.config_cache[key][1] > config_service.cache_ttl 
            for key in cache_keys
        )
    
    @pytest.mark.asyncio
    async def test_service_status(self, config_service):
        """Test configuration service status reporting"""
        await config_service.initialize()
        
        # Add some test data
        await config_service.set_configuration("training", "test1", {"task_name": "test1"})
        await config_service.set_configuration("training", "test2", {"task_name": "test2"})
        await config_service.set_configuration("simulation", "test1", {"terrain": {"mesh_type": "plane"}})
        
        status = await config_service.get_status()
        
        # Check status structure
        assert "state" in status
        assert "is_initialized" in status
        assert "configurations_count" in status
        assert "versions_count" in status
        assert "pending_conflicts" in status
        assert "resolved_conflicts" in status
        assert "snapshots_count" in status
        assert "templates_count" in status
        assert "cache_size" in status
        
        # Check status values
        assert status["state"] == ConfigServiceState.IDLE.value
        assert status["is_initialized"] is True
        assert status["configurations_count"] == 3  # 2 training + 1 simulation
        assert status["versions_count"] >= 3  # At least 3 versions
        assert status["templates_count"] >= 3  # Default templates
    
    @pytest.mark.asyncio
    async def test_cleanup_operations(self, config_service):
        """Test cleanup of old versions and backups"""
        await config_service.initialize()
        config_service.config.max_versions_per_config = 2  # Limit for testing
        
        # Create multiple versions
        base_config = {"task_name": "cleanup_test", "num_envs": 1024}
        
        for i in range(5):
            updated_config = base_config.copy()
            updated_config["version"] = i
            
            await config_service.set_configuration(
                "training", "cleanup_test", updated_config,
                author="test", comment=f"Version {i}"
            )
        
        config_key = "training/cleanup_test"
        
        # Should have limited versions due to archiving
        assert len(config_service.versions[config_key]) <= config_service.config.max_versions_per_config
        
        # Test cleanup of old versions
        await config_service._cleanup_old_versions()
        
        # Should not affect recent versions (cleanup time-based)
        assert len(config_service.versions[config_key]) > 0
    
    @pytest.mark.asyncio
    async def test_service_shutdown(self, config_service):
        """Test configuration service shutdown"""
        await config_service.initialize()
        
        # Add some test data
        await config_service.set_configuration("training", "shutdown_test", {"task_name": "test"})
        
        # Shutdown the service
        await config_service.shutdown()
        
        # Check that service is no longer initialized
        assert config_service.is_initialized is False
        assert config_service.state == ConfigServiceState.IDLE


class TestConfigurationIntegration:
    """Integration tests for configuration service with other services"""
    
    @pytest.fixture
    async def mock_session_manager(self):
        """Create a mock session manager for integration tests"""
        manager = Mock(spec=EnhancedZenohSessionManager)
        manager.initialize = AsyncMock(return_value=True)
        manager.shutdown = AsyncMock()
        return manager
    
    @pytest.mark.asyncio
    async def test_multi_service_configuration_coordination(self, mock_session_manager):
        """Test coordination between configuration service and other services"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create configuration service
            config_service_config = ConfigServiceConfig(
                config_storage_path=os.path.join(temp_dir, "configs"),
                version_storage_path=os.path.join(temp_dir, "versions"),
                backup_path=os.path.join(temp_dir, "backups"),
                template_path=os.path.join(temp_dir, "templates"),
                auto_backup_enabled=False
            )
            
            with patch('zenoh_services.services.config_service.TopicManager'):
                config_service = ConfigurationService(mock_session_manager, config_service_config)
                config_service.topic_manager = Mock()
                config_service.topic_manager.register_topic = AsyncMock()
                config_service.topic_manager.create_publisher = AsyncMock()
                config_service.topic_manager.create_subscriber = AsyncMock()
                config_service.topic_manager.publish_message = AsyncMock()
                
                await config_service.initialize()
                
                try:
                    # Test coordinated configuration updates
                    training_config = {
                        "task_name": "anymal_c_flat",
                        "num_envs": 4096,
                        "max_iterations": 1500,
                        "learning_rate": 3e-4
                    }
                    
                    simulation_config = {
                        "terrain": {
                            "mesh_type": "plane",
                            "terrain_length": 8.0,
                            "terrain_width": 8.0
                        },
                        "physics": {
                            "dt": 0.005,
                            "gravity": [0.0, 0.0, -9.81],
                            "solver_type": "PGS"
                        },
                        "robot": {
                            "name": "anymal_c",
                            "base_mass": 35.0,
                            "joint_stiffness": 80.0
                        },
                        "num_envs": 4096
                    }
                    
                    deployment_config = {
                        "export_format": "jit",
                        "target_platform": "cpu",
                        "optimization_level": "default",
                        "model_path": "/path/to/model.pt"
                    }
                    
                    # Set all configurations
                    training_version = await config_service.set_configuration(
                        "training", "default", training_config,
                        author="integration_test", comment="Training config for integration test"
                    )
                    
                    simulation_version = await config_service.set_configuration(
                        "simulation", "default", simulation_config,
                        author="integration_test", comment="Simulation config for integration test"
                    )
                    
                    deployment_version = await config_service.set_configuration(
                        "deployment", "default", deployment_config,
                        author="integration_test", comment="Deployment config for integration test"
                    )
                    
                    # Create coordinated snapshot
                    request_data = {
                        "snapshot_name": "integration_test_snapshot",
                        "description": "Coordinated configuration snapshot for integration testing"
                    }
                    
                    request = ConfigurationRequest(
                        config_type="system",
                        action="create_snapshot",
                        config_data=request_data
                    )
                    
                    await config_service._handle_create_snapshot(request)
                    
                    # Verify snapshot includes all configurations
                    snapshot = list(config_service.snapshots.values())[0]
                    assert snapshot.training_config_version == training_version
                    assert snapshot.simulation_config_version == simulation_version
                    assert snapshot.deployment_config_version == deployment_version
                    
                    # Test configuration validation across services
                    # Training config should reference valid simulation parameters
                    training_data = await config_service.get_configuration("training", "default")
                    simulation_data = await config_service.get_configuration("simulation", "default")
                    
                    assert training_data["num_envs"] == simulation_data["num_envs"]
                    
                    # Verify Zenoh message publishing was called for updates
                    assert config_service.topic_manager.publish_message.call_count >= 3
                    
                finally:
                    await config_service.shutdown()
    
    @pytest.mark.asyncio
    async def test_configuration_conflict_across_services(self, mock_session_manager):
        """Test conflict detection across different service configurations"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_service_config = ConfigServiceConfig(
                config_storage_path=os.path.join(temp_dir, "configs"),
                version_storage_path=os.path.join(temp_dir, "versions"),
                backup_path=os.path.join(temp_dir, "backups"),
                template_path=os.path.join(temp_dir, "templates"),
                default_conflict_strategy=ConflictResolutionStrategy.MERGE,
                validate_cross_dependencies=True
            )
            
            with patch('zenoh_services.services.config_service.TopicManager'):
                config_service = ConfigurationService(mock_session_manager, config_service_config)
                config_service.topic_manager = Mock()
                config_service.topic_manager.register_topic = AsyncMock()
                config_service.topic_manager.create_publisher = AsyncMock()
                config_service.topic_manager.create_subscriber = AsyncMock()
                config_service.topic_manager.publish_message = AsyncMock()
                
                await config_service.initialize()
                
                try:
                    # Set initial training configuration
                    initial_training = {
                        "task_name": "anymal_c_flat",
                        "num_envs": 4096,
                        "max_iterations": 1000
                    }
                    
                    await config_service.set_configuration(
                        "training", "default", initial_training
                    )
                    
                    # Set matching simulation configuration
                    initial_simulation = {
                        "num_envs": 4096,  # Matches training
                        "terrain": {"mesh_type": "plane"},
                        "physics": {"dt": 0.005},
                        "robot": {"name": "anymal_c"}
                    }
                    
                    await config_service.set_configuration(
                        "simulation", "default", initial_simulation
                    )
                    
                    # Try to set conflicting training configuration
                    conflicting_training = {
                        "task_name": "anymal_c_flat",
                        "num_envs": 2048,  # Different from simulation
                        "max_iterations": 1500
                    }
                    
                    # Should detect cross-dependency conflict
                    validation_result = await config_service._validate_cross_dependencies(
                        "training", conflicting_training
                    )
                    
                    # For this simple test, we don't enforce num_envs consistency
                    # but in a real implementation, this could be a cross-dependency check
                    
                    # Test successful update with consistent parameters
                    consistent_training = {
                        "task_name": "anymal_c_flat", 
                        "num_envs": 4096,  # Matches simulation
                        "max_iterations": 1500
                    }
                    
                    version_id = await config_service.set_configuration(
                        "training", "default", consistent_training,
                        comment="Consistent update"
                    )
                    
                    assert version_id is not None
                    
                finally:
                    await config_service.shutdown()


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v", "--tb=short"])