"""
Task 15.2.5 - 性能基准和监控系统 (Performance Baseline and Monitoring System)

本模块实现性能基准建立和持续监控系统，包括：
- 性能基准建立
- 实时性能监控
- 性能回归检测
- 性能报告生成
"""

import asyncio
import time
import threading
import statistics
import json
import sqlite3
from typing import List, Dict, Any, Optional, Callable
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from collections import deque, defaultdict
from unittest.mock import Mock, patch
import pytest
import sys
import os

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from core.session_manager import SessionManager
from core.topics import TopicManager
from services.training_service import TrainingService


@dataclass
class PerformanceMetric:
    """性能指标数据类"""
    metric_name: str
    value: float
    unit: str
    timestamp: datetime
    category: str
    tags: Dict[str, str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = {}


@dataclass
class PerformanceBaseline:
    """性能基准数据类"""
    metric_name: str
    baseline_value: float
    tolerance_percent: float
    warning_threshold: float
    critical_threshold: float
    established_date: datetime
    sample_count: int
    
    def is_within_tolerance(self, value: float) -> bool:
        """检查值是否在容许范围内"""
        tolerance = self.baseline_value * self.tolerance_percent / 100
        return abs(value - self.baseline_value) <= tolerance
    
    def get_deviation_level(self, value: float) -> str:
        """获取偏差级别"""
        deviation = abs(value - self.baseline_value) / self.baseline_value * 100
        
        if deviation <= self.tolerance_percent:
            return 'normal'
        elif deviation <= self.warning_threshold:
            return 'warning'
        elif deviation <= self.critical_threshold:
            return 'critical'
        else:
            return 'severe'


class PerformanceDatabase:
    """性能数据库"""
    
    def __init__(self, db_path: str = ':memory:'):
        self.db_path = db_path
        self.connection = None
        self._init_database()
    
    def _init_database(self):
        """初始化数据库"""
        self.connection = sqlite3.connect(self.db_path, check_same_thread=False)
        self.connection.execute('''
            CREATE TABLE IF NOT EXISTS performance_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                metric_name TEXT NOT NULL,
                value REAL NOT NULL,
                unit TEXT NOT NULL,
                timestamp TEXT NOT NULL,
                category TEXT NOT NULL,
                tags TEXT
            )
        ''')
        
        self.connection.execute('''
            CREATE TABLE IF NOT EXISTS performance_baselines (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                metric_name TEXT UNIQUE NOT NULL,
                baseline_value REAL NOT NULL,
                tolerance_percent REAL NOT NULL,
                warning_threshold REAL NOT NULL,
                critical_threshold REAL NOT NULL,
                established_date TEXT NOT NULL,
                sample_count INTEGER NOT NULL
            )
        ''')
        
        self.connection.commit()
    
    def store_metric(self, metric: PerformanceMetric):
        """存储性能指标"""
        self.connection.execute('''
            INSERT INTO performance_metrics 
            (metric_name, value, unit, timestamp, category, tags)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            metric.metric_name,
            metric.value,
            metric.unit,
            metric.timestamp.isoformat(),
            metric.category,
            json.dumps(metric.tags)
        ))
        self.connection.commit()
    
    def store_baseline(self, baseline: PerformanceBaseline):
        """存储性能基准"""
        self.connection.execute('''
            INSERT OR REPLACE INTO performance_baselines
            (metric_name, baseline_value, tolerance_percent, warning_threshold,
             critical_threshold, established_date, sample_count)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            baseline.metric_name,
            baseline.baseline_value,
            baseline.tolerance_percent,
            baseline.warning_threshold,
            baseline.critical_threshold,
            baseline.established_date.isoformat(),
            baseline.sample_count
        ))
        self.connection.commit()
    
    def get_metrics(self, metric_name: str, hours: int = 24) -> List[PerformanceMetric]:
        """获取指定时间范围内的指标"""
        since = datetime.now() - timedelta(hours=hours)
        
        cursor = self.connection.execute('''
            SELECT metric_name, value, unit, timestamp, category, tags
            FROM performance_metrics
            WHERE metric_name = ? AND timestamp > ?
            ORDER BY timestamp DESC
        ''', (metric_name, since.isoformat()))
        
        metrics = []
        for row in cursor:
            metrics.append(PerformanceMetric(
                metric_name=row[0],
                value=row[1],
                unit=row[2],
                timestamp=datetime.fromisoformat(row[3]),
                category=row[4],
                tags=json.loads(row[5]) if row[5] else {}
            ))
        
        return metrics
    
    def get_baseline(self, metric_name: str) -> Optional[PerformanceBaseline]:
        """获取性能基准"""
        cursor = self.connection.execute('''
            SELECT metric_name, baseline_value, tolerance_percent, warning_threshold,
                   critical_threshold, established_date, sample_count
            FROM performance_baselines
            WHERE metric_name = ?
        ''', (metric_name,))
        
        row = cursor.fetchone()
        if row:
            return PerformanceBaseline(
                metric_name=row[0],
                baseline_value=row[1],
                tolerance_percent=row[2],
                warning_threshold=row[3],
                critical_threshold=row[4],
                established_date=datetime.fromisoformat(row[5]),
                sample_count=row[6]
            )
        return None
    
    def get_all_metrics_summary(self, hours: int = 24) -> Dict[str, Dict[str, float]]:
        """获取所有指标的摘要"""
        since = datetime.now() - timedelta(hours=hours)
        
        cursor = self.connection.execute('''
            SELECT metric_name, AVG(value) as avg_value, MIN(value) as min_value,
                   MAX(value) as max_value, COUNT(*) as sample_count
            FROM performance_metrics
            WHERE timestamp > ?
            GROUP BY metric_name
        ''', (since.isoformat(),))
        
        summary = {}
        for row in cursor:
            summary[row[0]] = {
                'avg': row[1],
                'min': row[2],
                'max': row[3],
                'count': row[4]
            }
        
        return summary
    
    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()


class PerformanceCollector:
    """性能数据收集器"""
    
    def __init__(self, database: PerformanceDatabase):
        self.database = database
        self.collectors = {}  # metric_name -> collector_function
        self.collection_intervals = {}  # metric_name -> interval_seconds
        self.collection_threads = {}
        self.collecting = False
    
    def register_collector(self, metric_name: str, collector_func: Callable[[], float],
                         interval_seconds: float = 1.0, category: str = 'system',
                         unit: str = 'count', tags: Dict[str, str] = None):
        """注册性能收集器"""
        self.collectors[metric_name] = {
            'function': collector_func,
            'category': category,
            'unit': unit,
            'tags': tags or {}
        }
        self.collection_intervals[metric_name] = interval_seconds
    
    def start_collection(self):
        """开始性能收集"""
        self.collecting = True
        for metric_name in self.collectors:
            thread = threading.Thread(
                target=self._collect_metric_continuously,
                args=(metric_name,)
            )
            thread.daemon = True
            thread.start()
            self.collection_threads[metric_name] = thread
    
    def stop_collection(self):
        """停止性能收集"""
        self.collecting = False
        for thread in self.collection_threads.values():
            thread.join(timeout=1)
        self.collection_threads.clear()
    
    def _collect_metric_continuously(self, metric_name: str):
        """持续收集指定指标"""
        collector_info = self.collectors[metric_name]
        interval = self.collection_intervals[metric_name]
        
        while self.collecting:
            try:
                value = collector_info['function']()
                
                metric = PerformanceMetric(
                    metric_name=metric_name,
                    value=value,
                    unit=collector_info['unit'],
                    timestamp=datetime.now(),
                    category=collector_info['category'],
                    tags=collector_info['tags']
                )
                
                self.database.store_metric(metric)
                
            except Exception as e:
                print(f"Error collecting metric {metric_name}: {e}")
            
            time.sleep(interval)


class BaselineEstablisher:
    """基准建立器"""
    
    def __init__(self, database: PerformanceDatabase):
        self.database = database
        self.establishment_config = {
            'min_samples': 100,  # 最少样本数
            'collection_hours': 24,  # 收集时间（小时）
            'outlier_threshold': 2.5,  # 异常值标准差倍数
            'default_tolerance': 10.0,  # 默认容忍度（%）
            'default_warning': 20.0,  # 默认警告阈值（%）
            'default_critical': 50.0  # 默认严重阈值（%）
        }
    
    def establish_baseline(self, metric_name: str, 
                          custom_config: Dict[str, float] = None) -> PerformanceBaseline:
        """建立性能基准"""
        config = self.establishment_config.copy()
        if custom_config:
            config.update(custom_config)
        
        # 获取历史数据
        metrics = self.database.get_metrics(metric_name, hours=config['collection_hours'])
        
        if len(metrics) < config['min_samples']:
            raise ValueError(f"样本数量不足：需要至少{config['min_samples']}个，实际{len(metrics)}个")
        
        # 提取数值并排序
        values = [m.value for m in metrics]
        values.sort()
        
        # 移除异常值
        mean_val = statistics.mean(values)
        std_val = statistics.stdev(values) if len(values) > 1 else 0
        
        filtered_values = []
        outlier_threshold = config['outlier_threshold'] * std_val
        
        for value in values:
            if abs(value - mean_val) <= outlier_threshold:
                filtered_values.append(value)
        
        if len(filtered_values) < config['min_samples'] * 0.7:
            # 如果过滤后样本太少，使用原始数据
            filtered_values = values
        
        # 计算基准值（中位数更稳定）
        baseline_value = statistics.median(filtered_values)
        
        # 创建基准对象
        baseline = PerformanceBaseline(
            metric_name=metric_name,
            baseline_value=baseline_value,
            tolerance_percent=config['default_tolerance'],
            warning_threshold=config['default_warning'],
            critical_threshold=config['default_critical'],
            established_date=datetime.now(),
            sample_count=len(filtered_values)
        )
        
        # 存储基准
        self.database.store_baseline(baseline)
        
        return baseline
    
    def update_baseline(self, metric_name: str, 
                       new_tolerance: float = None,
                       new_warning: float = None,
                       new_critical: float = None):
        """更新基准参数"""
        baseline = self.database.get_baseline(metric_name)
        if not baseline:
            raise ValueError(f"基准不存在：{metric_name}")
        
        if new_tolerance is not None:
            baseline.tolerance_percent = new_tolerance
        if new_warning is not None:
            baseline.warning_threshold = new_warning
        if new_critical is not None:
            baseline.critical_threshold = new_critical
        
        self.database.store_baseline(baseline)


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, database: PerformanceDatabase):
        self.database = database
        self.alert_handlers = []
        self.monitoring = False
        self.monitor_thread = None
        self.check_interval = 30  # 30秒检查一次
        self.recent_alerts = deque(maxlen=1000)  # 保留最近1000个警报
    
    def add_alert_handler(self, handler: Callable[[str, str, PerformanceMetric, PerformanceBaseline], None]):
        """添加警报处理器"""
        self.alert_handlers.append(handler)
    
    def start_monitoring(self):
        """开始监控"""
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_continuously)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
    
    def _monitor_continuously(self):
        """持续监控"""
        while self.monitoring:
            try:
                self._check_all_metrics()
            except Exception as e:
                print(f"监控错误: {e}")
            
            time.sleep(self.check_interval)
    
    def _check_all_metrics(self):
        """检查所有指标"""
        # 获取最近的指标摘要
        summary = self.database.get_all_metrics_summary(hours=1)
        
        for metric_name, stats in summary.items():
            baseline = self.database.get_baseline(metric_name)
            if not baseline:
                continue
            
            # 检查平均值
            current_value = stats['avg']
            deviation_level = baseline.get_deviation_level(current_value)
            
            if deviation_level != 'normal':
                # 创建虚拟指标用于警报
                metric = PerformanceMetric(
                    metric_name=metric_name,
                    value=current_value,
                    unit='avg',
                    timestamp=datetime.now(),
                    category='monitoring'
                )
                
                # 发送警报
                self._send_alert(deviation_level, metric, baseline)
    
    def _send_alert(self, level: str, metric: PerformanceMetric, baseline: PerformanceBaseline):
        """发送警报"""
        alert_info = {
            'level': level,
            'metric_name': metric.metric_name,
            'current_value': metric.value,
            'baseline_value': baseline.baseline_value,
            'deviation_percent': abs(metric.value - baseline.baseline_value) / baseline.baseline_value * 100,
            'timestamp': metric.timestamp.isoformat()
        }
        
        # 避免重复警报
        alert_key = f"{metric.metric_name}_{level}"
        recent_alerts = [a for a in self.recent_alerts if time.time() - a['time'] < 300]  # 5分钟内
        recent_alert_keys = [a['key'] for a in recent_alerts]
        
        if alert_key not in recent_alert_keys:
            self.recent_alerts.append({
                'key': alert_key,
                'time': time.time(),
                'info': alert_info
            })
            
            # 调用所有警报处理器
            for handler in self.alert_handlers:
                try:
                    handler(level, f"性能指标 {metric.metric_name} 超出正常范围", metric, baseline)
                except Exception as e:
                    print(f"警报处理器错误: {e}")
    
    def get_recent_alerts(self, hours: int = 24) -> List[Dict[str, Any]]:
        """获取最近的警报"""
        since_time = time.time() - hours * 3600
        return [a['info'] for a in self.recent_alerts if a['time'] > since_time]


class PerformanceReporter:
    """性能报告生成器"""
    
    def __init__(self, database: PerformanceDatabase):
        self.database = database
    
    def generate_summary_report(self, hours: int = 24) -> Dict[str, Any]:
        """生成摘要报告"""
        summary = self.database.get_all_metrics_summary(hours)
        baselines_info = {}
        deviations = {}
        
        for metric_name in summary:
            baseline = self.database.get_baseline(metric_name)
            if baseline:
                baselines_info[metric_name] = asdict(baseline)
                current_avg = summary[metric_name]['avg']
                deviation = baseline.get_deviation_level(current_avg)
                deviations[metric_name] = {
                    'level': deviation,
                    'deviation_percent': abs(current_avg - baseline.baseline_value) / baseline.baseline_value * 100
                }
        
        return {
            'report_time': datetime.now().isoformat(),
            'time_range_hours': hours,
            'metrics_summary': summary,
            'baselines': baselines_info,
            'deviations': deviations,
            'total_metrics': len(summary)
        }
    
    def generate_trend_report(self, metric_name: str, hours: int = 168) -> Dict[str, Any]:
        """生成趋势报告（默认7天）"""
        metrics = self.database.get_metrics(metric_name, hours)
        if not metrics:
            return {'error': f'No data found for metric: {metric_name}'}
        
        # 按小时分组
        hourly_data = defaultdict(list)
        for metric in metrics:
            hour_key = metric.timestamp.replace(minute=0, second=0, microsecond=0)
            hourly_data[hour_key].append(metric.value)
        
        # 计算每小时统计
        trend_data = []
        for hour, values in sorted(hourly_data.items()):
            trend_data.append({
                'timestamp': hour.isoformat(),
                'avg': statistics.mean(values),
                'min': min(values),
                'max': max(values),
                'count': len(values)
            })
        
        # 计算总体趋势
        if len(trend_data) > 1:
            first_avg = trend_data[0]['avg']
            last_avg = trend_data[-1]['avg']
            trend_direction = 'increasing' if last_avg > first_avg else 'decreasing' if last_avg < first_avg else 'stable'
            trend_change_percent = abs(last_avg - first_avg) / first_avg * 100 if first_avg != 0 else 0
        else:
            trend_direction = 'unknown'
            trend_change_percent = 0
        
        baseline = self.database.get_baseline(metric_name)
        
        return {
            'metric_name': metric_name,
            'report_time': datetime.now().isoformat(),
            'time_range_hours': hours,
            'trend_data': trend_data,
            'trend_direction': trend_direction,
            'trend_change_percent': trend_change_percent,
            'baseline_info': asdict(baseline) if baseline else None,
            'total_samples': len(metrics)
        }


class PerformanceBenchmarkSystem:
    """性能基准和监控系统"""
    
    def __init__(self, db_path: str = ':memory:'):
        self.database = PerformanceDatabase(db_path)
        self.collector = PerformanceCollector(self.database)
        self.baseline_establisher = BaselineEstablisher(self.database)
        self.monitor = PerformanceMonitor(self.database)
        self.reporter = PerformanceReporter(self.database)
        
        # 默认警报处理器
        self.monitor.add_alert_handler(self._default_alert_handler)
    
    def _default_alert_handler(self, level: str, message: str, 
                             metric: PerformanceMetric, baseline: PerformanceBaseline):
        """默认警报处理器"""
        print(f"[{level.upper()}] {message}")
        print(f"  指标: {metric.metric_name}")
        print(f"  当前值: {metric.value} {metric.unit}")
        print(f"  基准值: {baseline.baseline_value} {metric.unit}")
        print(f"  偏差: {abs(metric.value - baseline.baseline_value) / baseline.baseline_value * 100:.2f}%")
    
    def setup_system_monitoring(self):
        """设置系统监控"""
        # 注册系统性能收集器
        import psutil
        
        self.collector.register_collector(
            'cpu_usage_percent',
            lambda: psutil.cpu_percent(),
            interval_seconds=2.0,
            category='system',
            unit='percent'
        )
        
        self.collector.register_collector(
            'memory_usage_percent',
            lambda: psutil.virtual_memory().percent,
            interval_seconds=2.0,
            category='system',
            unit='percent'
        )
        
        self.collector.register_collector(
            'disk_usage_percent',
            lambda: psutil.disk_usage('/').percent,
            interval_seconds=10.0,
            category='system',
            unit='percent'
        )
    
    def start_full_monitoring(self):
        """开始完整监控"""
        self.collector.start_collection()
        self.monitor.start_monitoring()
    
    def stop_full_monitoring(self):
        """停止完整监控"""
        self.collector.stop_collection()
        self.monitor.stop_monitoring()
    
    def establish_all_baselines(self):
        """建立所有已收集指标的基准"""
        summary = self.database.get_all_metrics_summary(hours=24)
        baselines_created = []
        
        for metric_name in summary:
            try:
                baseline = self.baseline_establisher.establish_baseline(metric_name)
                baselines_created.append(baseline.metric_name)
            except ValueError as e:
                print(f"无法为 {metric_name} 建立基准: {e}")
        
        return baselines_created
    
    def get_system_health_status(self) -> Dict[str, Any]:
        """获取系统健康状态"""
        recent_alerts = self.monitor.get_recent_alerts(hours=1)
        summary = self.database.get_all_metrics_summary(hours=1)
        
        # 计算健康分数
        total_metrics = len(summary)
        critical_alerts = len([a for a in recent_alerts if a['level'] == 'critical'])
        warning_alerts = len([a for a in recent_alerts if a['level'] == 'warning'])
        
        health_score = 100
        if total_metrics > 0:
            health_score -= (critical_alerts / total_metrics) * 50
            health_score -= (warning_alerts / total_metrics) * 20
            health_score = max(0, health_score)
        
        return {
            'health_score': health_score,
            'status': 'healthy' if health_score > 80 else 'warning' if health_score > 50 else 'critical',
            'total_metrics': total_metrics,
            'recent_alerts': len(recent_alerts),
            'critical_alerts': critical_alerts,
            'warning_alerts': warning_alerts,
            'timestamp': datetime.now().isoformat()
        }
    
    def cleanup(self):
        """清理资源"""
        self.stop_full_monitoring()
        self.database.close()


@pytest.mark.asyncio
class TestPerformanceBenchmarkSystem:
    """性能基准和监控系统测试用例"""
    
    def setup_method(self):
        """测试前设置"""
        self.system = PerformanceBenchmarkSystem()
    
    def teardown_method(self):
        """测试后清理"""
        self.system.cleanup()
    
    def test_metric_collection_and_storage(self):
        """测试指标收集和存储"""
        # 注册测试收集器
        test_counter = [0]
        
        def test_collector():
            test_counter[0] += 1
            return test_counter[0] * 10
        
        self.system.collector.register_collector(
            'test_metric',
            test_collector,
            interval_seconds=0.1,
            category='test',
            unit='count'
        )
        
        # 收集一段时间
        self.system.collector.start_collection()
        time.sleep(0.5)  # 收集500ms
        self.system.collector.stop_collection()
        
        # 检查数据
        metrics = self.system.database.get_metrics('test_metric', hours=1)
        assert len(metrics) >= 3, "应收集到至少3个指标点"
        assert all(m.metric_name == 'test_metric' for m in metrics), "指标名称应正确"
        
        print(f"指标收集测试结果: 收集到 {len(metrics)} 个指标点")
    
    def test_baseline_establishment(self):
        """测试基准建立"""
        # 生成模拟数据
        import random
        for i in range(150):
            # 模拟正常分布的数据，均值100，标准差10
            value = random.normalvariate(100, 10)
            metric = PerformanceMetric(
                metric_name='test_baseline_metric',
                value=value,
                unit='ms',
                timestamp=datetime.now() - timedelta(minutes=i),
                category='test'
            )
            self.system.database.store_metric(metric)
        
        # 建立基准
        baseline = self.system.baseline_establisher.establish_baseline('test_baseline_metric')
        
        assert baseline is not None, "应成功建立基准"
        assert 90 <= baseline.baseline_value <= 110, "基准值应在合理范围内"
        assert baseline.sample_count >= 100, "样本数应足够"
        
        # 测试基准检查
        assert baseline.is_within_tolerance(105), "105应在容忍范围内"
        assert not baseline.is_within_tolerance(150), "150应超出容忍范围"
        
        print(f"基准建立测试结果: 基准值 {baseline.baseline_value:.2f}, 样本数 {baseline.sample_count}")
    
    def test_performance_monitoring_and_alerts(self):
        """测试性能监控和警报"""
        # 建立基准
        baseline = PerformanceBaseline(
            metric_name='test_monitor_metric',
            baseline_value=50.0,
            tolerance_percent=10.0,
            warning_threshold=20.0,
            critical_threshold=40.0,
            established_date=datetime.now(),
            sample_count=100
        )
        self.system.database.store_baseline(baseline)
        
        # 记录测试警报
        alerts_received = []
        
        def test_alert_handler(level, message, metric, baseline):
            alerts_received.append({
                'level': level,
                'metric_value': metric.value,
                'baseline_value': baseline.baseline_value
            })
        
        self.system.monitor.add_alert_handler(test_alert_handler)
        
        # 添加正常值
        normal_metric = PerformanceMetric(
            metric_name='test_monitor_metric',
            value=52.0,  # 4%偏差，正常
            unit='ms',
            timestamp=datetime.now(),
            category='test'
        )
        self.system.database.store_metric(normal_metric)
        
        # 添加警告值
        warning_metric = PerformanceMetric(
            metric_name='test_monitor_metric',
            value=62.0,  # 24%偏差，警告
            unit='ms',
            timestamp=datetime.now(),
            category='test'
        )
        self.system.database.store_metric(warning_metric)
        
        # 手动触发检查
        self.system.monitor._check_all_metrics()
        
        # 验证警报
        assert len(alerts_received) == 1, "应收到1个警报"
        assert alerts_received[0]['level'] == 'warning', "应为警告级别"
        
        print(f"性能监控测试结果: 收到 {len(alerts_received)} 个警报")
    
    def test_report_generation(self):
        """测试报告生成"""
        # 生成测试数据
        metric_names = ['cpu_usage', 'memory_usage', 'response_time']
        
        for metric_name in metric_names:
            # 为每个指标生成数据
            for i in range(50):
                value = 50 + (i % 20) + random.uniform(-5, 5)
                metric = PerformanceMetric(
                    metric_name=metric_name,
                    value=value,
                    unit='percent' if 'usage' in metric_name else 'ms',
                    timestamp=datetime.now() - timedelta(hours=i/2),
                    category='system'
                )
                self.system.database.store_metric(metric)
            
            # 建立基准
            baseline = PerformanceBaseline(
                metric_name=metric_name,
                baseline_value=55.0,
                tolerance_percent=10.0,
                warning_threshold=20.0,
                critical_threshold=40.0,
                established_date=datetime.now(),
                sample_count=50
            )
            self.system.database.store_baseline(baseline)
        
        # 生成摘要报告
        summary_report = self.system.reporter.generate_summary_report(hours=48)
        
        assert 'metrics_summary' in summary_report, "应包含指标摘要"
        assert 'baselines' in summary_report, "应包含基准信息"
        assert len(summary_report['metrics_summary']) == 3, "应有3个指标"
        
        # 生成趋势报告
        trend_report = self.system.reporter.generate_trend_report('cpu_usage', hours=48)
        
        assert 'trend_data' in trend_report, "应包含趋势数据"
        assert len(trend_report['trend_data']) > 0, "应有趋势数据点"
        assert 'trend_direction' in trend_report, "应包含趋势方向"
        
        print(f"报告生成测试结果:")
        print(f"  摘要报告指标数: {len(summary_report['metrics_summary'])}")
        print(f"  趋势报告数据点: {len(trend_report['trend_data'])}")
    
    def test_system_health_monitoring(self):
        """测试系统健康监控"""
        # 设置系统监控（使用模拟数据）
        test_values = [80, 85, 90]  # CPU使用率
        value_index = [0]
        
        def mock_cpu_collector():
            value = test_values[value_index[0] % len(test_values)]
            value_index[0] += 1
            return value
        
        self.system.collector.register_collector(
            'system_cpu_usage',
            mock_cpu_collector,
            interval_seconds=0.1,
            category='system',
            unit='percent'
        )
        
        # 收集数据
        self.system.collector.start_collection()
        time.sleep(0.5)
        self.system.collector.stop_collection()
        
        # 建立基准
        try:
            self.system.baseline_establisher.establish_baseline('system_cpu_usage')
        except ValueError:
            # 如果样本不足，手动创建基准
            baseline = PerformanceBaseline(
                metric_name='system_cpu_usage',
                baseline_value=85.0,
                tolerance_percent=5.0,
                warning_threshold=10.0,
                critical_threshold=20.0,
                established_date=datetime.now(),
                sample_count=10
            )
            self.system.database.store_baseline(baseline)
        
        # 获取系统健康状态
        health_status = self.system.get_system_health_status()
        
        assert 'health_score' in health_status, "应包含健康分数"
        assert 'status' in health_status, "应包含健康状态"
        assert 0 <= health_status['health_score'] <= 100, "健康分数应在0-100之间"
        
        print(f"系统健康监控测试结果:")
        print(f"  健康分数: {health_status['health_score']}")
        print(f"  健康状态: {health_status['status']}")
    
    def test_full_system_integration(self):
        """测试完整系统集成"""
        import random
        
        # 模拟完整系统运行
        system = PerformanceBenchmarkSystem()
        
        try:
            # 注册多个收集器
            collectors_data = {
                'request_latency': lambda: random.uniform(10, 100),
                'throughput': lambda: random.uniform(1000, 5000),
                'error_rate': lambda: random.uniform(0, 5)
            }
            
            for name, collector in collectors_data.items():
                system.collector.register_collector(
                    name,
                    collector,
                    interval_seconds=0.1,
                    category='application',
                    unit='ms' if 'latency' in name else 'count'
                )
            
            # 启动完整监控
            system.start_full_monitoring()
            time.sleep(1.0)  # 运行1秒
            system.stop_full_monitoring()
            
            # 尝试建立基准
            established_baselines = []
            for metric_name in collectors_data.keys():
                try:
                    baseline = system.baseline_establisher.establish_baseline(metric_name)
                    established_baselines.append(baseline.metric_name)
                except ValueError:
                    # 样本不足，跳过
                    pass
            
            # 生成完整报告
            summary_report = system.reporter.generate_summary_report()
            health_status = system.get_system_health_status()
            
            assert len(summary_report['metrics_summary']) > 0, "应有指标数据"
            assert health_status['health_score'] >= 0, "应有健康分数"
            
            print(f"完整系统集成测试结果:")
            print(f"  收集的指标: {list(summary_report['metrics_summary'].keys())}")
            print(f"  建立的基准: {established_baselines}")
            print(f"  系统健康分数: {health_status['health_score']}")
            
        finally:
            system.cleanup()


if __name__ == "__main__":
    # 运行性能基准和监控系统测试
    def main():
        test_suite = TestPerformanceBenchmarkSystem()
        
        print("开始性能基准和监控系统测试...")
        
        print("\n1. 指标收集和存储测试...")
        test_suite.setup_method()
        test_suite.test_metric_collection_and_storage()
        test_suite.teardown_method()
        
        print("\n2. 基准建立测试...")
        test_suite.setup_method()
        test_suite.test_baseline_establishment()
        test_suite.teardown_method()
        
        print("\n3. 性能监控和警报测试...")
        test_suite.setup_method()
        test_suite.test_performance_monitoring_and_alerts()
        test_suite.teardown_method()
        
        print("\n4. 报告生成测试...")
        test_suite.setup_method()
        test_suite.test_report_generation()
        test_suite.teardown_method()
        
        print("\n5. 系统健康监控测试...")
        test_suite.setup_method()
        test_suite.test_system_health_monitoring()
        test_suite.teardown_method()
        
        print("\n6. 完整系统集成测试...")
        test_suite.test_full_system_integration()
        
        print("\n所有性能基准和监控系统测试完成！")
    
    main()