"""
Task 15.2.3 - 大规模并发用户压力测试 (Large-Scale Concurrent User Stress Testing)

本模块实现大规模并发用户的压力测试，包括：
- 并发用户模拟
- 系统负载测试
- 故障恢复测试
- 性能降级测试
"""

import asyncio
import time
import threading
import random
import statistics
import psutil
import pytest
from typing import List, Dict, Any, Optional
from unittest.mock import Mock, patch
from concurrent.futures import ThreadPoolExecutor, as_completed
import json
import sys
import os
from dataclasses import dataclass
from collections import defaultdict

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from core.session_manager import SessionManager
from core.topics import TopicManager
from core.message_format import MessageFormatter
from services.training_service import TrainingService
from services.simulation_service import SimulationService


@dataclass
class UserSimulationConfig:
    """用户模拟配置"""
    user_count: int = 100
    session_duration_min: int = 5
    session_duration_max: int = 30
    action_interval_min: float = 0.1
    action_interval_max: float = 2.0
    message_size_min: int = 100
    message_size_max: int = 10000
    failure_rate: float = 0.01  # 1% failure rate


@dataclass
class StressTestMetrics:
    """压力测试指标"""
    total_users: int = 0
    concurrent_users: int = 0
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    average_response_time: float = 0.0
    p95_response_time: float = 0.0
    p99_response_time: float = 0.0
    max_response_time: float = 0.0
    requests_per_second: float = 0.0
    cpu_usage_avg: float = 0.0
    cpu_usage_max: float = 0.0
    memory_usage_avg: float = 0.0
    memory_usage_max: float = 0.0
    error_rate: float = 0.0
    throughput_mbps: float = 0.0


class VirtualUser:
    """虚拟用户模拟器"""
    
    def __init__(self, user_id: int, config: UserSimulationConfig):
        self.user_id = user_id
        self.config = config
        self.session_manager = None
        self.is_active = False
        self.requests_sent = 0
        self.requests_successful = 0
        self.response_times = []
        self.start_time = None
        self.end_time = None
    
    async def initialize(self):
        """初始化虚拟用户"""
        with patch('core.session_manager.zenoh'):
            self.session_manager = SessionManager()
            await self.session_manager.initialize()
        self.is_active = True
        self.start_time = time.time()
    
    async def simulate_user_session(self) -> Dict[str, Any]:
        """模拟用户会话"""
        session_duration = random.uniform(
            self.config.session_duration_min, 
            self.config.session_duration_max
        )
        session_end_time = time.time() + session_duration
        
        while time.time() < session_end_time and self.is_active:
            # 执行用户操作
            await self._perform_user_action()
            
            # 随机等待间隔
            wait_time = random.uniform(
                self.config.action_interval_min,
                self.config.action_interval_max
            )
            await asyncio.sleep(wait_time)
        
        self.end_time = time.time()
        return self._get_user_metrics()
    
    async def _perform_user_action(self):
        """执行用户操作"""
        action_type = random.choice([
            'send_training_data', 
            'get_simulation_state', 
            'update_config',
            'subscribe_to_updates'
        ])
        
        request_start = time.time()
        success = False
        
        try:
            # 模拟不同类型的请求
            if action_type == 'send_training_data':
                success = await self._send_training_data()
            elif action_type == 'get_simulation_state':
                success = await self._get_simulation_state()
            elif action_type == 'update_config':
                success = await self._update_config()
            elif action_type == 'subscribe_to_updates':
                success = await self._subscribe_to_updates()
            
        except Exception as e:
            success = False
        
        request_end = time.time()
        response_time = request_end - request_start
        
        self.requests_sent += 1
        if success:
            self.requests_successful += 1
        
        self.response_times.append(response_time)
    
    async def _send_training_data(self) -> bool:
        """发送训练数据"""
        # 模拟发送训练数据
        message_size = random.randint(
            self.config.message_size_min, 
            self.config.message_size_max
        )
        data = 'x' * message_size
        
        # 模拟网络延迟
        delay = random.uniform(0.001, 0.020)  # 1-20ms
        await asyncio.sleep(delay)
        
        # 模拟失败率
        return random.random() > self.config.failure_rate
    
    async def _get_simulation_state(self) -> bool:
        """获取仿真状态"""
        # 模拟获取状态请求
        delay = random.uniform(0.005, 0.050)  # 5-50ms
        await asyncio.sleep(delay)
        return random.random() > self.config.failure_rate
    
    async def _update_config(self) -> bool:
        """更新配置"""
        delay = random.uniform(0.010, 0.100)  # 10-100ms
        await asyncio.sleep(delay)
        return random.random() > self.config.failure_rate
    
    async def _subscribe_to_updates(self) -> bool:
        """订阅更新"""
        delay = random.uniform(0.002, 0.010)  # 2-10ms
        await asyncio.sleep(delay)
        return random.random() > self.config.failure_rate
    
    def _get_user_metrics(self) -> Dict[str, Any]:
        """获取用户指标"""
        duration = self.end_time - self.start_time if self.end_time else 0
        
        return {
            'user_id': self.user_id,
            'session_duration': duration,
            'requests_sent': self.requests_sent,
            'requests_successful': self.requests_successful,
            'success_rate': self.requests_successful / max(1, self.requests_sent),
            'avg_response_time': statistics.mean(self.response_times) if self.response_times else 0,
            'max_response_time': max(self.response_times) if self.response_times else 0
        }
    
    def stop(self):
        """停止用户会话"""
        self.is_active = False


class SystemMonitor:
    """系统资源监控器"""
    
    def __init__(self):
        self.monitoring = False
        self.monitor_thread = None
        self.cpu_samples = []
        self.memory_samples = []
        self.network_samples = []
        self.disk_samples = []
        self.process_stats = []
    
    def start_monitoring(self):
        """开始监控"""
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_system)
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join()
    
    def _monitor_system(self):
        """监控系统资源"""
        while self.monitoring:
            # CPU使用率
            cpu_percent = psutil.cpu_percent()
            self.cpu_samples.append(cpu_percent)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            self.memory_samples.append(memory.percent)
            
            # 网络IO
            network = psutil.net_io_counters()
            self.network_samples.append({
                'bytes_sent': network.bytes_sent,
                'bytes_recv': network.bytes_recv
            })
            
            # 磁盘IO
            disk = psutil.disk_io_counters()
            if disk:
                self.disk_samples.append({
                    'read_bytes': disk.read_bytes,
                    'write_bytes': disk.write_bytes
                })
            
            # 进程统计
            process_count = len(psutil.pids())
            self.process_stats.append(process_count)
            
            time.sleep(0.5)  # 500ms采样
    
    def get_monitoring_results(self) -> Dict[str, Any]:
        """获取监控结果"""
        return {
            'cpu_stats': {
                'avg': statistics.mean(self.cpu_samples) if self.cpu_samples else 0,
                'max': max(self.cpu_samples) if self.cpu_samples else 0,
                'min': min(self.cpu_samples) if self.cpu_samples else 0
            },
            'memory_stats': {
                'avg': statistics.mean(self.memory_samples) if self.memory_samples else 0,
                'max': max(self.memory_samples) if self.memory_samples else 0,
                'min': min(self.memory_samples) if self.memory_samples else 0
            },
            'process_count_avg': statistics.mean(self.process_stats) if self.process_stats else 0,
            'network_io_total': self._calculate_network_io(),
            'disk_io_total': self._calculate_disk_io()
        }
    
    def _calculate_network_io(self) -> Dict[str, int]:
        """计算网络IO"""
        if not self.network_samples:
            return {'bytes_sent': 0, 'bytes_recv': 0}
        
        first = self.network_samples[0]
        last = self.network_samples[-1]
        
        return {
            'bytes_sent': last['bytes_sent'] - first['bytes_sent'],
            'bytes_recv': last['bytes_recv'] - first['bytes_recv']
        }
    
    def _calculate_disk_io(self) -> Dict[str, int]:
        """计算磁盘IO"""
        if not self.disk_samples:
            return {'read_bytes': 0, 'write_bytes': 0}
        
        first = self.disk_samples[0]
        last = self.disk_samples[-1]
        
        return {
            'read_bytes': last['read_bytes'] - first['read_bytes'],
            'write_bytes': last['write_bytes'] - first['write_bytes']
        }


class StressTestRunner:
    """压力测试运行器"""
    
    def __init__(self, config: UserSimulationConfig):
        self.config = config
        self.users = []
        self.system_monitor = SystemMonitor()
        self.test_start_time = None
        self.test_end_time = None
    
    async def run_concurrent_user_test(self) -> StressTestMetrics:
        """运行并发用户测试"""
        self.test_start_time = time.time()
        self.system_monitor.start_monitoring()
        
        try:
            # 创建虚拟用户
            self.users = [
                VirtualUser(i, self.config) 
                for i in range(self.config.user_count)
            ]
            
            # 初始化所有用户
            init_tasks = [user.initialize() for user in self.users]
            await asyncio.gather(*init_tasks)
            
            # 运行用户会话
            session_tasks = [user.simulate_user_session() for user in self.users]
            user_results = await asyncio.gather(*session_tasks, return_exceptions=True)
            
            # 停止所有用户
            for user in self.users:
                user.stop()
                
        finally:
            self.test_end_time = time.time()
            self.system_monitor.stop_monitoring()
        
        return self._calculate_metrics(user_results)
    
    async def run_gradual_ramp_up_test(self, ramp_duration: int = 60) -> List[StressTestMetrics]:
        """运行渐进式压力增加测试"""
        metrics_snapshots = []
        users_per_step = max(1, self.config.user_count // 10)  # 10步骤
        current_users = []
        
        self.system_monitor.start_monitoring()
        
        try:
            for step in range(10):
                step_start_time = time.time()
                
                # 添加新用户
                start_idx = len(current_users)
                end_idx = min(start_idx + users_per_step, self.config.user_count)
                
                new_users = [
                    VirtualUser(i, self.config) 
                    for i in range(start_idx, end_idx)
                ]
                
                # 初始化新用户
                init_tasks = [user.initialize() for user in new_users]
                await asyncio.gather(*init_tasks)
                
                current_users.extend(new_users)
                
                # 运行所有当前用户一段时间
                step_duration = ramp_duration / 10
                await asyncio.sleep(step_duration)
                
                # 收集当前步骤的指标
                active_user_metrics = []
                for user in current_users:
                    if user.is_active:
                        metrics = user._get_user_metrics()
                        active_user_metrics.append(metrics)
                
                step_metrics = self._calculate_metrics(active_user_metrics)
                step_metrics.concurrent_users = len(current_users)
                metrics_snapshots.append(step_metrics)
                
                print(f"Ramp-up Step {step + 1}: {len(current_users)} concurrent users")
                
        finally:
            # 停止所有用户
            for user in current_users:
                user.stop()
            self.system_monitor.stop_monitoring()
        
        return metrics_snapshots
    
    async def run_burst_test(self, burst_count: int = 5, burst_interval: int = 30) -> List[StressTestMetrics]:
        """运行突发流量测试"""
        burst_metrics = []
        self.system_monitor.start_monitoring()
        
        try:
            for burst in range(burst_count):
                print(f"Starting burst {burst + 1} of {burst_count}...")
                
                # 创建突发用户
                burst_users = [
                    VirtualUser(f"burst_{burst}_{i}", self.config) 
                    for i in range(self.config.user_count)
                ]
                
                burst_start = time.time()
                
                # 同时启动所有突发用户
                init_tasks = [user.initialize() for user in burst_users]
                await asyncio.gather(*init_tasks)
                
                # 运行短时间高强度测试
                session_tasks = []
                for user in burst_users:
                    # 创建短时间高频会话
                    short_config = UserSimulationConfig(
                        user_count=1,
                        session_duration_min=10,
                        session_duration_max=15,
                        action_interval_min=0.01,  # 更高频率
                        action_interval_max=0.1,
                        failure_rate=self.config.failure_rate
                    )
                    user.config = short_config
                    session_tasks.append(user.simulate_user_session())
                
                user_results = await asyncio.gather(*session_tasks, return_exceptions=True)
                
                # 计算突发指标
                burst_metrics_data = self._calculate_metrics(user_results)
                burst_metrics_data.concurrent_users = len(burst_users)
                burst_metrics.append(burst_metrics_data)
                
                # 停止突发用户
                for user in burst_users:
                    user.stop()
                
                # 等待下一个突发
                if burst < burst_count - 1:
                    await asyncio.sleep(burst_interval)
                
        finally:
            self.system_monitor.stop_monitoring()
        
        return burst_metrics
    
    def _calculate_metrics(self, user_results: List[Dict[str, Any]]) -> StressTestMetrics:
        """计算测试指标"""
        valid_results = [r for r in user_results if isinstance(r, dict)]
        
        if not valid_results:
            return StressTestMetrics()
        
        # 聚合用户指标
        total_requests = sum(r.get('requests_sent', 0) for r in valid_results)
        successful_requests = sum(r.get('requests_successful', 0) for r in valid_results)
        response_times = []
        
        for r in valid_results:
            if 'avg_response_time' in r and r['avg_response_time'] > 0:
                # 使用请求数加权平均响应时间
                requests = r.get('requests_sent', 1)
                avg_time = r['avg_response_time']
                response_times.extend([avg_time] * requests)
        
        # 系统监控指标
        system_metrics = self.system_monitor.get_monitoring_results()
        
        # 计算测试持续时间
        duration = (self.test_end_time or time.time()) - (self.test_start_time or time.time())
        
        # 创建指标对象
        metrics = StressTestMetrics()
        metrics.total_users = len(valid_results)
        metrics.total_requests = total_requests
        metrics.successful_requests = successful_requests
        metrics.failed_requests = total_requests - successful_requests
        metrics.error_rate = (metrics.failed_requests / max(1, total_requests)) * 100
        
        if response_times:
            metrics.average_response_time = statistics.mean(response_times)
            metrics.max_response_time = max(response_times)
            sorted_times = sorted(response_times)
            metrics.p95_response_time = sorted_times[int(len(sorted_times) * 0.95)]
            metrics.p99_response_time = sorted_times[int(len(sorted_times) * 0.99)]
        
        if duration > 0:
            metrics.requests_per_second = total_requests / duration
        
        metrics.cpu_usage_avg = system_metrics['cpu_stats']['avg']
        metrics.cpu_usage_max = system_metrics['cpu_stats']['max']
        metrics.memory_usage_avg = system_metrics['memory_stats']['avg']
        metrics.memory_usage_max = system_metrics['memory_stats']['max']
        
        # 计算吞吐量 (估算)
        network_io = system_metrics.get('network_io_total', {})
        total_bytes = network_io.get('bytes_sent', 0) + network_io.get('bytes_recv', 0)
        if duration > 0:
            metrics.throughput_mbps = (total_bytes * 8) / (1024 * 1024 * duration)
        
        return metrics


@pytest.mark.asyncio
class TestStressAndConcurrency:
    """压力测试和并发测试用例"""
    
    async def test_concurrent_users_basic(self):
        """测试基本并发用户性能"""
        config = UserSimulationConfig(
            user_count=50,
            session_duration_min=10,
            session_duration_max=20,
            failure_rate=0.05
        )
        
        test_runner = StressTestRunner(config)
        metrics = await test_runner.run_concurrent_user_test()
        
        # 并发性能断言
        assert metrics.total_users == 50, "应有50个并发用户"
        assert metrics.error_rate < 10.0, "错误率应小于10%"
        assert metrics.average_response_time < 0.5, "平均响应时间应小于500ms"
        assert metrics.cpu_usage_max < 90, "最大CPU使用率应小于90%"
        assert metrics.memory_usage_max < 95, "最大内存使用率应小于95%"
        
        print(f"基本并发用户测试结果: {json.dumps(metrics.__dict__, indent=2)}")
    
    async def test_high_concurrency_stress(self):
        """测试高并发压力"""
        config = UserSimulationConfig(
            user_count=200,
            session_duration_min=15,
            session_duration_max=30,
            action_interval_min=0.05,
            action_interval_max=0.5,
            failure_rate=0.02
        )
        
        test_runner = StressTestRunner(config)
        metrics = await test_runner.run_concurrent_user_test()
        
        # 高并发压力断言
        assert metrics.total_users == 200, "应有200个并发用户"
        assert metrics.error_rate < 15.0, "高并发错误率应小于15%"
        assert metrics.p95_response_time < 1.0, "95%响应时间应小于1秒"
        assert metrics.requests_per_second > 100, "每秒请求数应大于100"
        
        print(f"高并发压力测试结果: {json.dumps(metrics.__dict__, indent=2)}")
    
    async def test_gradual_ramp_up(self):
        """测试渐进式负载增加"""
        config = UserSimulationConfig(
            user_count=100,
            session_duration_min=20,
            session_duration_max=30
        )
        
        test_runner = StressTestRunner(config)
        metrics_snapshots = await test_runner.run_gradual_ramp_up_test(ramp_duration=30)
        
        # 渐进式负载断言
        assert len(metrics_snapshots) == 10, "应有10个快照"
        
        # 检查负载递增
        for i in range(1, len(metrics_snapshots)):
            current = metrics_snapshots[i]
            previous = metrics_snapshots[i-1]
            assert current.concurrent_users >= previous.concurrent_users, "用户数应递增"
        
        # 检查系统稳定性
        final_metrics = metrics_snapshots[-1]
        assert final_metrics.error_rate < 20.0, "最终错误率应小于20%"
        
        print("渐进式负载测试结果:")
        for i, metrics in enumerate(metrics_snapshots):
            print(f"  Step {i+1}: {metrics.concurrent_users} users, {metrics.error_rate:.2f}% error rate")
    
    async def test_burst_traffic(self):
        """测试突发流量"""
        config = UserSimulationConfig(
            user_count=150,
            session_duration_min=8,
            session_duration_max=12,
            action_interval_min=0.01,
            action_interval_max=0.1
        )
        
        test_runner = StressTestRunner(config)
        burst_metrics = await test_runner.run_burst_test(burst_count=3, burst_interval=15)
        
        # 突发流量断言
        assert len(burst_metrics) == 3, "应有3个突发测试结果"
        
        for i, metrics in enumerate(burst_metrics):
            assert metrics.total_users == 150, f"突发{i+1}应有150个用户"
            assert metrics.error_rate < 25.0, f"突发{i+1}错误率应小于25%"
            assert metrics.requests_per_second > 50, f"突发{i+1}QPS应大于50"
        
        print("突发流量测试结果:")
        for i, metrics in enumerate(burst_metrics):
            print(f"  Burst {i+1}: {metrics.requests_per_second:.1f} RPS, {metrics.error_rate:.2f}% error rate")
    
    async def test_system_resource_limits(self):
        """测试系统资源限制"""
        config = UserSimulationConfig(
            user_count=300,  # 更高的用户数
            session_duration_min=25,
            session_duration_max=35,
            action_interval_min=0.02,
            action_interval_max=0.2,
            message_size_min=1000,
            message_size_max=50000  # 更大的消息
        )
        
        test_runner = StressTestRunner(config)
        metrics = await test_runner.run_concurrent_user_test()
        
        # 资源限制测试断言
        assert metrics.total_users == 300, "应有300个并发用户"
        
        # 在高负载下系统应该能够应对，但允许更高的错误率
        assert metrics.error_rate < 30.0, "极高负载错误率应小于30%"
        assert metrics.cpu_usage_max < 98, "CPU使用率不应达到极限"
        assert metrics.memory_usage_max < 98, "内存使用率不应达到极限"
        
        # 检查系统是否仍有响应
        assert metrics.average_response_time < 2.0, "即使高负载，平均响应时间应小于2秒"
        
        print(f"系统资源限制测试结果: {json.dumps(metrics.__dict__, indent=2)}")


if __name__ == "__main__":
    # 运行压力测试
    async def main():
        test_suite = TestStressAndConcurrency()
        
        print("开始大规模并发用户压力测试...")
        
        print("\n1. 基本并发用户测试...")
        await test_suite.test_concurrent_users_basic()
        
        print("\n2. 高并发压力测试...")
        await test_suite.test_high_concurrency_stress()
        
        print("\n3. 渐进式负载测试...")
        await test_suite.test_gradual_ramp_up()
        
        print("\n4. 突发流量测试...")
        await test_suite.test_burst_traffic()
        
        print("\n5. 系统资源限制测试...")
        await test_suite.test_system_resource_limits()
        
        print("\n所有压力测试完成！")
    
    asyncio.run(main())