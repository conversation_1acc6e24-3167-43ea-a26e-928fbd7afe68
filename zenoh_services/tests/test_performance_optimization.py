"""
Task 15.2.1 - 高频数据传输性能测试套件 (High-Frequency Data Transmission Performance Test Suite)

本模块实现高频数据传输的性能测试，包括：
- 数据传输吞吐量测试
- 延迟测试
- CPU和内存使用率监控
- 网络带宽优化测试
"""

import asyncio
import time
import threading
import statistics
import psutil
import pytest
from typing import List, Dict, Any
from unittest.mock import Mock, patch
import json
import sys
import os

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from core.session_manager import SessionManager
from core.topics import TopicManager
from core.message_format import MessageFormatter
from services.training_service import TrainingService
from services.simulation_service import SimulationService


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
        self.latencies = []
        self.throughput_data = []
        self.cpu_usage = []
        self.memory_usage = []
        self.monitoring = False
        self.monitor_thread = None
    
    def start_monitoring(self):
        """开始性能监控"""
        self.start_time = time.time()
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_resources)
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止性能监控"""
        self.end_time = time.time()
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join()
    
    def _monitor_resources(self):
        """监控系统资源"""
        while self.monitoring:
            self.cpu_usage.append(psutil.cpu_percent())
            self.memory_usage.append(psutil.virtual_memory().percent)
            time.sleep(0.1)  # 100ms采样
    
    def record_latency(self, latency: float):
        """记录延迟"""
        self.latencies.append(latency)
    
    def record_throughput(self, messages_per_second: float):
        """记录吞吐量"""
        self.throughput_data.append(messages_per_second)
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        duration = (self.end_time or time.time()) - (self.start_time or time.time())
        
        return {
            'duration': duration,
            'latency_stats': {
                'min': min(self.latencies) if self.latencies else 0,
                'max': max(self.latencies) if self.latencies else 0,
                'avg': statistics.mean(self.latencies) if self.latencies else 0,
                'median': statistics.median(self.latencies) if self.latencies else 0,
                'p95': self._percentile(self.latencies, 95) if len(self.latencies) > 20 else 0,
                'p99': self._percentile(self.latencies, 99) if len(self.latencies) > 100 else 0
            },
            'throughput_stats': {
                'avg_mps': statistics.mean(self.throughput_data) if self.throughput_data else 0,
                'max_mps': max(self.throughput_data) if self.throughput_data else 0,
                'total_messages': sum(self.throughput_data) if self.throughput_data else 0
            },
            'resource_usage': {
                'avg_cpu': statistics.mean(self.cpu_usage) if self.cpu_usage else 0,
                'max_cpu': max(self.cpu_usage) if self.cpu_usage else 0,
                'avg_memory': statistics.mean(self.memory_usage) if self.memory_usage else 0,
                'max_memory': max(self.memory_usage) if self.memory_usage else 0
            }
        }
    
    @staticmethod
    def _percentile(data: List[float], percentile: int) -> float:
        """计算百分位数"""
        if not data:
            return 0
        sorted_data = sorted(data)
        index = int(len(sorted_data) * percentile / 100)
        return sorted_data[min(index, len(sorted_data) - 1)]


class HighFrequencyDataTransmissionTest:
    """高频数据传输测试"""
    
    def __init__(self):
        self.session_manager = None
        self.topic_manager = None
        self.message_formatter = None
        self.monitor = PerformanceMonitor()
    
    async def setup(self):
        """设置测试环境"""
        # 使用Mock以避免真实的Zenoh连接
        with patch('core.session_manager.zenoh'):
            self.session_manager = SessionManager()
            await self.session_manager.initialize()
        
        self.topic_manager = TopicManager()
        self.message_formatter = MessageFormatter()
    
    async def test_high_frequency_publishing(self, message_count: int = 10000, frequency_hz: int = 1000):
        """测试高频发布性能"""
        await self.setup()
        self.monitor.start_monitoring()
        
        try:
            # 准备测试数据
            test_message = {
                'timestamp': time.time(),
                'data': list(range(100)),  # 模拟传感器数据
                'metadata': {'test': True, 'sequence': 0}
            }
            
            interval = 1.0 / frequency_hz
            messages_sent = 0
            batch_size = 100
            
            start_time = time.time()
            
            for i in range(0, message_count, batch_size):
                batch_start = time.time()
                
                # 批量发送消息
                tasks = []
                for j in range(min(batch_size, message_count - i)):
                    test_message['metadata']['sequence'] = i + j
                    test_message['timestamp'] = time.time()
                    
                    task = self._send_message_async(test_message)
                    tasks.append(task)
                
                # 等待批量完成
                await asyncio.gather(*tasks)
                messages_sent += len(tasks)
                
                # 记录延迟
                batch_duration = time.time() - batch_start
                self.monitor.record_latency(batch_duration)
                
                # 控制发送频率
                elapsed = time.time() - start_time
                expected_time = messages_sent * interval
                if elapsed < expected_time:
                    await asyncio.sleep(expected_time - elapsed)
                
                # 记录吞吐量（每秒消息数）
                current_throughput = len(tasks) / (time.time() - batch_start)
                self.monitor.record_throughput(current_throughput)
        
        finally:
            self.monitor.stop_monitoring()
        
        return self.monitor.get_performance_metrics()
    
    async def _send_message_async(self, message: Dict[str, Any]):
        """异步发送消息"""
        # 模拟消息发送延迟
        await asyncio.sleep(0.001)  # 1ms模拟网络延迟
        return True
    
    async def test_concurrent_subscribers(self, subscriber_count: int = 50, message_count: int = 1000):
        """测试并发订阅者性能"""
        await self.setup()
        self.monitor.start_monitoring()
        
        try:
            # 创建多个并发订阅者
            subscribers = []
            received_messages = [[] for _ in range(subscriber_count)]
            
            async def subscriber_worker(subscriber_id: int):
                """订阅者工作线程"""
                for i in range(message_count):
                    message_start = time.time()
                    # 模拟消息接收和处理
                    await asyncio.sleep(0.0001)  # 0.1ms处理时间
                    message_end = time.time()
                    
                    received_messages[subscriber_id].append({
                        'id': i,
                        'latency': message_end - message_start,
                        'timestamp': message_end
                    })
                    
                    self.monitor.record_latency(message_end - message_start)
            
            # 启动所有订阅者
            tasks = [subscriber_worker(i) for i in range(subscriber_count)]
            await asyncio.gather(*tasks)
            
            # 计算总体统计
            total_messages = sum(len(msgs) for msgs in received_messages)
            self.monitor.record_throughput(total_messages)
        
        finally:
            self.monitor.stop_monitoring()
        
        return self.monitor.get_performance_metrics()
    
    async def test_large_message_throughput(self, message_size_kb: int = 100, message_count: int = 1000):
        """测试大消息吞吐量"""
        await self.setup()
        self.monitor.start_monitoring()
        
        try:
            # 创建大消息
            large_data = 'x' * (message_size_kb * 1024)  # KB转换为字节
            test_message = {
                'timestamp': time.time(),
                'large_data': large_data,
                'size_kb': message_size_kb,
                'sequence': 0
            }
            
            start_time = time.time()
            
            for i in range(message_count):
                message_start = time.time()
                test_message['sequence'] = i
                test_message['timestamp'] = time.time()
                
                # 发送大消息
                await self._send_large_message_async(test_message)
                
                message_end = time.time()
                message_latency = message_end - message_start
                
                self.monitor.record_latency(message_latency)
                
                # 每100个消息计算一次吞吐量
                if (i + 1) % 100 == 0:
                    elapsed = time.time() - start_time
                    throughput = (i + 1) / elapsed
                    self.monitor.record_throughput(throughput)
        
        finally:
            self.monitor.stop_monitoring()
        
        return self.monitor.get_performance_metrics()
    
    async def _send_large_message_async(self, message: Dict[str, Any]):
        """异步发送大消息"""
        # 模拟大消息传输延迟（基于消息大小）
        size_kb = message.get('size_kb', 1)
        delay = 0.001 + (size_kb * 0.0001)  # 基础1ms + 每KB 0.1ms
        await asyncio.sleep(delay)
        return True


@pytest.mark.asyncio
class TestHighFrequencyPerformance:
    """高频数据传输性能测试用例"""
    
    async def test_standard_frequency_performance(self):
        """测试标准频率性能 (1000 Hz)"""
        test_runner = HighFrequencyDataTransmissionTest()
        metrics = await test_runner.test_high_frequency_publishing(
            message_count=5000,
            frequency_hz=1000
        )
        
        # 性能断言
        assert metrics['latency_stats']['avg'] < 0.010, "平均延迟应小于10ms"
        assert metrics['throughput_stats']['avg_mps'] > 500, "平均吞吐量应大于500 msg/s"
        assert metrics['resource_usage']['max_cpu'] < 80, "最大CPU使用率应小于80%"
        assert metrics['resource_usage']['max_memory'] < 90, "最大内存使用率应小于90%"
        
        print(f"标准频率性能测试结果: {json.dumps(metrics, indent=2)}")
    
    async def test_high_frequency_performance(self):
        """测试高频性能 (5000 Hz)"""
        test_runner = HighFrequencyDataTransmissionTest()
        metrics = await test_runner.test_high_frequency_publishing(
            message_count=10000,
            frequency_hz=5000
        )
        
        # 高频性能断言
        assert metrics['latency_stats']['p95'] < 0.020, "95%延迟应小于20ms"
        assert metrics['throughput_stats']['avg_mps'] > 2000, "平均吞吐量应大于2000 msg/s"
        
        print(f"高频性能测试结果: {json.dumps(metrics, indent=2)}")
    
    async def test_concurrent_subscribers_performance(self):
        """测试并发订阅者性能"""
        test_runner = HighFrequencyDataTransmissionTest()
        metrics = await test_runner.test_concurrent_subscribers(
            subscriber_count=20,
            message_count=500
        )
        
        # 并发性能断言
        assert metrics['latency_stats']['avg'] < 0.005, "并发平均延迟应小于5ms"
        assert metrics['resource_usage']['max_cpu'] < 85, "并发时最大CPU使用率应小于85%"
        
        print(f"并发订阅者性能测试结果: {json.dumps(metrics, indent=2)}")
    
    async def test_large_message_performance(self):
        """测试大消息性能"""
        test_runner = HighFrequencyDataTransmissionTest()
        metrics = await test_runner.test_large_message_throughput(
            message_size_kb=500,  # 500KB消息
            message_count=100
        )
        
        # 大消息性能断言
        assert metrics['latency_stats']['avg'] < 0.100, "大消息平均延迟应小于100ms"
        assert metrics['throughput_stats']['avg_mps'] > 10, "大消息吞吐量应大于10 msg/s"
        
        print(f"大消息性能测试结果: {json.dumps(metrics, indent=2)}")
    
    async def test_stress_endurance_performance(self):
        """测试压力耐久性能"""
        test_runner = HighFrequencyDataTransmissionTest()
        
        # 长时间运行测试
        metrics = await test_runner.test_high_frequency_publishing(
            message_count=50000,  # 大量消息
            frequency_hz=2000     # 中等频率
        )
        
        # 耐久性能断言
        assert metrics['latency_stats']['p99'] < 0.050, "99%延迟应小于50ms"
        assert metrics['resource_usage']['avg_cpu'] < 70, "长时间平均CPU使用率应小于70%"
        
        # 检查性能稳定性（标准差不应过大）
        if len(test_runner.monitor.latencies) > 1000:
            latency_std = statistics.stdev(test_runner.monitor.latencies)
            assert latency_std < 0.010, "延迟标准差应小于10ms（性能稳定性）"
        
        print(f"压力耐久性能测试结果: {json.dumps(metrics, indent=2)}")


if __name__ == "__main__":
    # 运行性能测试
    async def main():
        test_suite = TestHighFrequencyPerformance()
        
        print("开始高频数据传输性能测试...")
        
        print("\n1. 标准频率性能测试...")
        await test_suite.test_standard_frequency_performance()
        
        print("\n2. 高频性能测试...")
        await test_suite.test_high_frequency_performance()
        
        print("\n3. 并发订阅者性能测试...")
        await test_suite.test_concurrent_subscribers_performance()
        
        print("\n4. 大消息性能测试...")
        await test_suite.test_large_message_performance()
        
        print("\n5. 压力耐久性能测试...")
        await test_suite.test_stress_endurance_performance()
        
        print("\n所有性能测试完成！")
    
    asyncio.run(main())