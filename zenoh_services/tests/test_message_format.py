import pytest
import time
from zenoh_services.core.message_format import (
    MessageType, Priority, MessageHeader, StandardZenohMessage,
    MessageValidator, MessageBuilder, MessageFactory, MessageProcessor
)
from zenoh_services.core.data_models import TrainingConfig, RobotCommand

class TestMessageHeader:
    """Test MessageHeader functionality"""
    
    def test_default_header_creation(self):
        """Test default header creation"""
        header = MessageHeader()
        
        assert len(header.message_id) > 0
        assert header.timestamp > 0
        assert header.message_type == MessageType.DATA
        assert header.priority == Priority.NORMAL
        assert header.ttl == 300.0
        assert not header.is_expired()
        assert header.validate()
    
    def test_custom_header_creation(self):
        """Test custom header creation"""
        header = MessageHeader(
            message_type=MessageType.COMMAND,
            priority=Priority.HIGH,
            source_service="training_service",
            destination_service="simulation_service",
            ttl=60.0
        )
        
        assert header.message_type == MessageType.COMMAND
        assert header.priority == Priority.HIGH
        assert header.source_service == "training_service"
        assert header.destination_service == "simulation_service"
        assert header.ttl == 60.0
        assert header.validate()
    
    def test_header_expiration(self):
        """Test header expiration"""
        header = MessageHeader(ttl=0.1)  # Very short TTL
        assert not header.is_expired()
        
        time.sleep(0.2)  # Wait longer than TTL
        assert header.is_expired()
    
    def test_header_validation(self):
        """Test header validation"""
        # Valid header
        valid_header = MessageHeader()
        assert valid_header.validate()
        
        # Invalid header (negative TTL)
        invalid_header = MessageHeader(ttl=-1.0)
        assert not invalid_header.validate()

class TestStandardZenohMessage:
    """Test StandardZenohMessage functionality"""
    
    def test_basic_message_creation(self):
        """Test basic message creation"""
        header = MessageHeader(message_type=MessageType.DATA)
        message = StandardZenohMessage(
            header=header,
            payload={"key": "value", "number": 123}
        )
        
        assert message.validate()
        assert message.payload["key"] == "value"
        assert message.payload["number"] == 123
    
    def test_message_metadata(self):
        """Test message metadata functionality"""
        message = StandardZenohMessage(header=MessageHeader())
        
        # Add metadata
        message.add_metadata("source", "test_service")
        message.add_metadata("priority", "high")
        
        # Get metadata
        assert message.get_metadata("source") == "test_service"
        assert message.get_metadata("priority") == "high"
        assert message.get_metadata("nonexistent", "default") == "default"
    
    def test_response_correlation(self):
        """Test response correlation"""
        original_header = MessageHeader(
            message_type=MessageType.COMMAND,
            source_service="client_service"
        )
        original_message = StandardZenohMessage(header=original_header)
        
        response_header = MessageHeader(message_type=MessageType.DATA)
        response_message = StandardZenohMessage(header=response_header)
        
        # Set as response
        response_message.set_response_to(original_message)
        
        assert response_message.header.correlation_id == original_message.header.message_id
        assert response_message.header.destination_service == "client_service"
        assert response_message.header.message_type == MessageType.RESPONSE
    
    def test_message_serialization(self):
        """Test message serialization"""
        message = StandardZenohMessage(
            header=MessageHeader(message_type=MessageType.COMMAND),
            payload={"command": "start_training", "parameters": {"num_envs": 1024}}
        )
        
        # Test JSON serialization
        json_str = message.to_json()
        deserialized = StandardZenohMessage.from_json(json_str)
        
        assert deserialized.validate()
        assert deserialized.payload["command"] == "start_training"
        assert deserialized.payload["parameters"]["num_envs"] == 1024
        
        # Test MessagePack serialization
        msgpack_bytes = message.to_msgpack()
        deserialized_mp = StandardZenohMessage.from_msgpack(msgpack_bytes)
        
        assert deserialized_mp.validate()
        assert deserialized_mp.payload["command"] == "start_training"

class TestMessageBuilder:
    """Test MessageBuilder functionality"""
    
    def test_basic_builder_usage(self):
        """Test basic builder usage"""
        message = MessageBuilder(MessageType.COMMAND) \
            .with_source("client") \
            .with_destination("server") \
            .with_priority(Priority.HIGH) \
            .with_payload_data("action", "start") \
            .with_metadata("timestamp", time.time()) \
            .build()
        
        assert message.header.message_type == MessageType.COMMAND
        assert message.header.source_service == "client"
        assert message.header.destination_service == "server"
        assert message.header.priority == Priority.HIGH
        assert message.payload["action"] == "start"
        assert "timestamp" in message.metadata
    
    def test_builder_with_complete_payload(self):
        """Test builder with complete payload"""
        payload = {
            "command": "configure",
            "parameters": {"setting1": "value1", "setting2": 42}
        }
        
        message = MessageBuilder(MessageType.COMMAND) \
            .with_payload(payload) \
            .build()
        
        assert message.payload == payload
        assert message.validate()
    
    def test_builder_validation(self):
        """Test builder validation"""
        # Valid message should build successfully
        valid_message = MessageBuilder(MessageType.DATA).build()
        assert valid_message.validate()
        
        # Builder should raise error for invalid configuration
        # (This would depend on specific validation rules)

class TestMessageFactory:
    """Test MessageFactory functionality"""
    
    def test_command_message_creation(self):
        """Test command message creation"""
        message = MessageFactory.create_command(
            command="start_training",
            parameters={"num_envs": 1024, "max_iterations": 1000},
            source="gui_service",
            destination="training_service"
        )
        
        assert message.header.message_type == MessageType.COMMAND
        assert message.header.source_service == "gui_service"
        assert message.header.destination_service == "training_service"
        assert message.payload["command"] == "start_training"
        assert message.payload["parameters"]["num_envs"] == 1024
        assert message.validate()
    
    def test_response_message_creation(self):
        """Test response message creation"""
        original_message = MessageFactory.create_command("test", source="client")
        
        response = MessageFactory.create_response(
            success=True,
            message="Command executed successfully",
            data={"result": "ok"},
            original_message=original_message
        )
        
        assert response.header.message_type == MessageType.RESPONSE
        assert response.header.correlation_id == original_message.header.message_id
        assert response.header.destination_service == "client"
        assert response.payload["success"] is True
        assert response.payload["message"] == "Command executed successfully"
        assert response.payload["data"]["result"] == "ok"
    
    def test_status_message_creation(self):
        """Test status message creation"""
        status_data = {
            "service_name": "training_service",
            "status": "running",
            "current_iteration": 500,
            "progress": 0.5
        }
        
        message = MessageFactory.create_status(status_data, "training_service")
        
        assert message.header.message_type == MessageType.STATUS
        assert message.header.source_service == "training_service"
        assert message.payload == status_data
        assert message.validate()
    
    def test_error_message_creation(self):
        """Test error message creation"""
        message = MessageFactory.create_error(
            error_type="ValidationError",
            error_message="Invalid training configuration",
            context={"config_field": "num_envs", "value": -1},
            source="validation_service"
        )
        
        assert message.header.message_type == MessageType.ERROR
        assert message.header.priority == Priority.HIGH
        assert message.header.source_service == "validation_service"
        assert message.payload["error_type"] == "ValidationError"
        assert message.payload["error_message"] == "Invalid training configuration"
        assert message.payload["context"]["config_field"] == "num_envs"
    
    def test_heartbeat_message_creation(self):
        """Test heartbeat message creation"""
        status = {"cpu_usage": 45.2, "memory_usage": 67.8}
        message = MessageFactory.create_heartbeat("monitoring_service", status)
        
        assert message.header.message_type == MessageType.HEARTBEAT
        assert message.header.source_service == "monitoring_service"
        assert message.header.ttl == 60.0  # Heartbeat has shorter TTL
        assert message.payload["status"] == status
        assert "timestamp" in message.payload

class TestMessageValidator:
    """Test MessageValidator functionality"""
    
    def test_message_structure_validation(self):
        """Test message structure validation"""
        valid_message = StandardZenohMessage(header=MessageHeader())
        assert MessageValidator.validate_message_structure(valid_message)
        
        # Test with invalid structure
        invalid_message = "not a message"
        assert not MessageValidator.validate_message_structure(invalid_message)
    
    def test_message_content_validation(self):
        """Test message content validation"""
        # Valid message
        valid_message = StandardZenohMessage(header=MessageHeader())
        assert MessageValidator.validate_message_content(valid_message)
        
        # Expired message
        expired_header = MessageHeader(ttl=0.01)
        time.sleep(0.02)
        expired_message = StandardZenohMessage(header=expired_header)
        assert not MessageValidator.validate_message_content(expired_message)
    
    def test_payload_schema_validation(self):
        """Test payload schema validation"""
        message = StandardZenohMessage(
            header=MessageHeader(),
            payload={"command": "test", "parameters": {}, "timestamp": time.time()}
        )
        
        # Test with expected keys present
        assert MessageValidator.validate_payload_schema(message, ["command", "parameters"])
        
        # Test with missing keys
        assert not MessageValidator.validate_payload_schema(message, ["command", "missing_key"])
    
    def test_message_type_validation(self):
        """Test message type validation"""
        command_message = MessageFactory.create_command("test")
        assert MessageValidator.validate_message_type(command_message, MessageType.COMMAND)
        assert not MessageValidator.validate_message_type(command_message, MessageType.RESPONSE)

class TestMessageProcessor:
    """Test MessageProcessor functionality"""
    
    def test_message_processing_decision(self):
        """Test message processing decision logic"""
        # Message for specific service
        specific_message = MessageFactory.create_command("test", destination="target_service")
        assert MessageProcessor.should_process_message(specific_message, "target_service")
        assert not MessageProcessor.should_process_message(specific_message, "other_service")
        
        # Broadcast message (no destination)
        broadcast_message = MessageFactory.create_status({"status": "ok"})
        assert MessageProcessor.should_process_message(broadcast_message, "any_service")
    
    def test_payload_data_extraction(self):
        """Test payload data extraction"""
        config = TrainingConfig(task_name="test_extraction", num_envs=512)
        message = StandardZenohMessage(
            header=MessageHeader(),
            payload=config.to_dict()
        )
        
        extracted = MessageProcessor.extract_payload_data(message, TrainingConfig)
        assert isinstance(extracted, TrainingConfig)
        assert extracted.task_name == "test_extraction"
        assert extracted.num_envs == 512
    
    def test_ack_response_creation(self):
        """Test acknowledgment response creation"""
        original = MessageFactory.create_command("test", source="client")
        ack = MessageProcessor.create_ack_response(original)
        
        assert ack.header.message_type == MessageType.RESPONSE
        assert ack.header.correlation_id == original.header.message_id
        assert ack.header.destination_service == "client"
        assert ack.payload["success"] is True
        assert ack.payload["message"] == "Message acknowledged"

if __name__ == "__main__":
    pytest.main([__file__, "-v"])