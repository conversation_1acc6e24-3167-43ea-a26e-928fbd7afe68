"""
Task 15.1: End-to-End Integration Tests
Complete training workflow end-to-end testing for Legged Robot Gymnasium
"""

import asyncio
import pytest
import logging
import time
import os
import json
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from typing import Dict, List, Any, Optional

# Import all necessary services and components
from zenoh_services.core.enhanced_session_manager import EnhancedZenohSessionManager
from zenoh_services.core.topic_manager import TopicManager
from zenoh_services.core.data_models import (
    TrainingConfig, TrainingState, TrainingMetrics, PlayState, 
    DeploymentState, SimulationConfig, SystemConfig
)
from zenoh_services.core.error_handler import get_error_handler, initialize_error_handler
from zenoh_services.core.fault_detection import get_fault_detection_system

from zenoh_services.services.training_service import TrainingService, TrainingServiceConfig
from zenoh_services.services.play_service import PlayService
from zenoh_services.services.simulation_service import SimulationService
from zenoh_services.services.deployment_service import DeploymentService
from zenoh_services.services.config_service import ConfigService

# Set up logging for tests
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@pytest.fixture(scope="session")
async def test_environment():
    """Set up test environment"""
    # Create temporary directory for test outputs
    temp_dir = tempfile.mkdtemp(prefix="legged_gym_test_")
    
    # Initialize error handling system
    error_handler = initialize_error_handler(
        log_file=os.path.join(temp_dir, "test_errors.log"),
        enable_recovery=True
    )
    
    # Mock Zenoh session manager
    session_manager = Mock(spec=EnhancedZenohSessionManager)
    session_manager.session = Mock()
    session_manager.is_connected = Mock(return_value=True)
    session_manager.close = AsyncMock()
    
    yield {
        "temp_dir": temp_dir,
        "session_manager": session_manager,
        "error_handler": error_handler
    }
    
    # Cleanup
    shutil.rmtree(temp_dir, ignore_errors=True)


@pytest.fixture
async def service_manager(test_environment):
    """Create service manager with all services"""
    session_manager = test_environment["session_manager"]
    temp_dir = test_environment["temp_dir"]
    
    # Initialize all services
    services = {
        "training": TrainingService(
            session_manager, 
            TrainingServiceConfig(
                task_name="test_task",
                num_envs=64,  # Small for testing
                max_iterations=10,  # Small for testing
                log_dir=temp_dir
            )
        ),
        "play": PlayService(session_manager),
        "simulation": SimulationService(session_manager),
        "deployment": DeploymentService(session_manager),
        "config": ConfigService(session_manager)
    }
    
    # Mock initialize methods to avoid actual Isaac Gym initialization
    for service_name, service in services.items():
        service.initialize = AsyncMock(return_value=True)
        service.start = AsyncMock(return_value=True)
        service.stop = AsyncMock(return_value=True)
        service.cleanup = AsyncMock(return_value=True)
    
    return services


class TestEndToEndTrainingWorkflow:
    """Test complete training workflow from start to finish"""
    
    @pytest.mark.asyncio
    async def test_complete_training_pipeline(self, service_manager, test_environment):
        """Test complete training pipeline: config -> train -> test -> deploy"""
        
        logger.info("Starting complete training pipeline test...")
        
        # Step 1: Initialize all services
        logger.info("Step 1: Initializing services...")
        for service_name, service in service_manager.items():
            result = await service.initialize()
            assert result is True, f"Failed to initialize {service_name} service"
            
            result = await service.start()
            assert result is True, f"Failed to start {service_name} service"
        
        # Step 2: Configure training
        logger.info("Step 2: Configuring training...")
        config_service = service_manager["config"]
        
        training_config = TrainingConfig(
            task_name="test_anymal_c_flat",
            num_envs=64,
            max_iterations=10,
            save_interval=5,
            learning_rate=0.001,
            batch_size=2048,
            device="cpu"  # Use CPU for testing
        )
        
        # Mock config service operations
        config_service.set_training_config = AsyncMock(return_value=True)
        config_service.get_training_config = AsyncMock(return_value=training_config)
        
        await config_service.set_training_config(training_config)
        retrieved_config = await config_service.get_training_config()
        
        assert retrieved_config.task_name == training_config.task_name
        assert retrieved_config.num_envs == training_config.num_envs
        
        # Step 3: Start training
        logger.info("Step 3: Starting training...")
        training_service = service_manager["training"]
        
        # Mock training operations
        training_service.start_training = AsyncMock(return_value=True)
        training_service.get_training_status = AsyncMock(return_value=TrainingState.TRAINING)
        training_service.get_current_metrics = AsyncMock(return_value=TrainingMetrics(
            iteration=5,
            mean_reward=100.0,
            std_reward=10.0,
            mean_episode_length=500.0,
            learning_rate=0.001,
            total_timesteps=320000,
            fps=1000.0
        ))
        
        # Start training
        training_started = await training_service.start_training(training_config)
        assert training_started is True
        
        # Monitor training progress
        for i in range(3):
            await asyncio.sleep(0.1)  # Simulate training time
            status = await training_service.get_training_status()
            assert status == TrainingState.TRAINING
            
            metrics = await training_service.get_current_metrics()
            assert metrics is not None
            assert metrics.iteration >= 0
            
        logger.info("Training completed successfully")
        
        # Step 4: Test trained model
        logger.info("Step 4: Testing trained model...")
        play_service = service_manager["play"]
        
        # Mock play service operations
        play_service.load_policy = AsyncMock(return_value=True)
        play_service.start_testing = AsyncMock(return_value=True)
        play_service.get_test_results = AsyncMock(return_value={
            "episodes": 10,
            "mean_reward": 150.0,
            "success_rate": 0.8,
            "avg_episode_length": 600.0
        })
        
        # Load trained policy
        policy_loaded = await play_service.load_policy("test_model.pt")
        assert policy_loaded is True
        
        # Start testing
        testing_started = await play_service.start_testing()
        assert testing_started is True
        
        # Get test results
        test_results = await play_service.get_test_results()
        assert test_results is not None
        assert test_results["episodes"] > 0
        assert test_results["mean_reward"] > 0
        
        logger.info("Model testing completed successfully")
        
        # Step 5: Deploy model
        logger.info("Step 5: Deploying model...")
        deployment_service = service_manager["deployment"]
        
        # Mock deployment operations
        deployment_service.export_model = AsyncMock(return_value="test_model.onnx")
        deployment_service.validate_model = AsyncMock(return_value=True)
        deployment_service.deploy_model = AsyncMock(return_value=True)
        
        # Export model
        exported_path = await deployment_service.export_model("test_model.pt", "onnx")
        assert exported_path == "test_model.onnx"
        
        # Validate exported model
        validation_result = await deployment_service.validate_model(exported_path)
        assert validation_result is True
        
        # Deploy model
        deployment_result = await deployment_service.deploy_model(exported_path)
        assert deployment_result is True
        
        logger.info("Model deployment completed successfully")
        
        # Step 6: Cleanup
        logger.info("Step 6: Cleaning up...")
        for service_name, service in service_manager.items():
            await service.stop()
            await service.cleanup()
        
        logger.info("Complete training pipeline test passed!")
    
    @pytest.mark.asyncio
    async def test_training_with_simulation_config(self, service_manager):
        """Test training with custom simulation configuration"""
        
        logger.info("Testing training with custom simulation configuration...")
        
        simulation_service = service_manager["simulation"]
        training_service = service_manager["training"]
        
        # Initialize services
        await simulation_service.initialize()
        await training_service.initialize()
        
        # Configure simulation
        sim_config = SimulationConfig(
            physics_engine="isaac_gym",
            dt=0.005,
            substeps=2,
            gravity=[0.0, 0.0, -9.81],
            terrain_type="plane",
            terrain_size=[100.0, 100.0],
            num_threads=4
        )
        
        # Mock simulation service operations
        simulation_service.set_simulation_config = AsyncMock(return_value=True)
        simulation_service.reset_simulation = AsyncMock(return_value=True)
        simulation_service.get_simulation_state = AsyncMock(return_value="running")
        
        # Set simulation configuration
        config_set = await simulation_service.set_simulation_config(sim_config)
        assert config_set is True
        
        # Reset simulation with new config
        reset_result = await simulation_service.reset_simulation()
        assert reset_result is True
        
        # Verify simulation is running
        sim_state = await simulation_service.get_simulation_state()
        assert sim_state == "running"
        
        # Start training with simulation
        training_service.start_training = AsyncMock(return_value=True)
        training_result = await training_service.start_training()
        assert training_result is True
        
        logger.info("Training with simulation configuration test passed!")
    
    @pytest.mark.asyncio
    async def test_model_checkpoint_and_resume(self, service_manager, test_environment):
        """Test model checkpoint saving and training resume"""
        
        logger.info("Testing model checkpoint and resume functionality...")
        
        training_service = service_manager["training"]
        temp_dir = test_environment["temp_dir"]
        
        await training_service.initialize()
        
        # Mock checkpoint operations
        checkpoint_path = os.path.join(temp_dir, "checkpoint_5.pt")
        
        training_service.save_checkpoint = AsyncMock(return_value=checkpoint_path)
        training_service.load_checkpoint = AsyncMock(return_value=True)
        training_service.get_current_iteration = AsyncMock(return_value=5)
        
        # Start training
        training_service.start_training = AsyncMock(return_value=True)
        await training_service.start_training()
        
        # Save checkpoint
        saved_path = await training_service.save_checkpoint(5)
        assert saved_path == checkpoint_path
        
        # Simulate training interruption
        training_service.pause_training = AsyncMock(return_value=True)
        await training_service.pause_training()
        
        # Resume from checkpoint
        resume_result = await training_service.load_checkpoint(checkpoint_path)
        assert resume_result is True
        
        # Verify current iteration
        current_iter = await training_service.get_current_iteration()
        assert current_iter == 5
        
        logger.info("Checkpoint and resume test passed!")
    
    @pytest.mark.asyncio
    async def test_multi_task_training(self, service_manager):
        """Test training with multiple tasks/environments"""
        
        logger.info("Testing multi-task training scenario...")
        
        training_service = service_manager["training"]
        
        await training_service.initialize()
        
        # Define multiple tasks
        tasks = ["anymal_c_flat", "anymal_c_rough", "anymal_c_stairs"]
        
        # Mock multi-task operations
        training_service.set_multi_task_config = AsyncMock(return_value=True)
        training_service.start_multi_task_training = AsyncMock(return_value=True)
        training_service.get_task_progress = AsyncMock(return_value={
            task: {"iteration": 5, "reward": 100.0 + i * 10} 
            for i, task in enumerate(tasks)
        })
        
        # Configure multi-task training
        multi_config = {
            "tasks": tasks,
            "task_weights": [1.0, 0.8, 0.6],
            "switch_frequency": 1000,
            "shared_policy": True
        }
        
        config_result = await training_service.set_multi_task_config(multi_config)
        assert config_result is True
        
        # Start multi-task training
        training_result = await training_service.start_multi_task_training()
        assert training_result is True
        
        # Check progress for all tasks
        progress = await training_service.get_task_progress()
        assert len(progress) == len(tasks)
        
        for task in tasks:
            assert task in progress
            assert progress[task]["iteration"] > 0
            assert progress[task]["reward"] > 0
        
        logger.info("Multi-task training test passed!")
    
    @pytest.mark.asyncio
    async def test_distributed_training(self, service_manager):
        """Test distributed training across multiple workers"""
        
        logger.info("Testing distributed training scenario...")
        
        training_service = service_manager["training"]
        
        await training_service.initialize()
        
        # Mock distributed training operations
        training_service.setup_distributed_training = AsyncMock(return_value=True)
        training_service.start_distributed_training = AsyncMock(return_value=True)
        training_service.get_worker_status = AsyncMock(return_value={
            "worker_0": {"status": "training", "iteration": 5, "fps": 1000},
            "worker_1": {"status": "training", "iteration": 5, "fps": 950},
            "worker_2": {"status": "training", "iteration": 4, "fps": 1020}
        })
        training_service.aggregate_gradients = AsyncMock(return_value=True)
        
        # Setup distributed training
        distributed_config = {
            "num_workers": 3,
            "backend": "nccl",
            "master_addr": "localhost",
            "master_port": "12355"
        }
        
        setup_result = await training_service.setup_distributed_training(distributed_config)
        assert setup_result is True
        
        # Start distributed training
        training_result = await training_service.start_distributed_training()
        assert training_result is True
        
        # Check worker status
        worker_status = await training_service.get_worker_status()
        assert len(worker_status) == 3
        
        for worker_id, status in worker_status.items():
            assert status["status"] == "training"
            assert status["iteration"] > 0
            assert status["fps"] > 0
        
        # Test gradient aggregation
        aggregation_result = await training_service.aggregate_gradients()
        assert aggregation_result is True
        
        logger.info("Distributed training test passed!")


class TestSystemIntegration:
    """Test system-level integration scenarios"""
    
    @pytest.mark.asyncio
    async def test_concurrent_services(self, service_manager):
        """Test multiple services running concurrently"""
        
        logger.info("Testing concurrent services operation...")
        
        # Initialize all services
        init_tasks = []
        for service_name, service in service_manager.items():
            init_tasks.append(service.initialize())
        
        # Wait for all services to initialize
        init_results = await asyncio.gather(*init_tasks, return_exceptions=True)
        
        # Check all services initialized successfully
        for i, result in enumerate(init_results):
            service_name = list(service_manager.keys())[i]
            assert result is True, f"Service {service_name} failed to initialize: {result}"
        
        # Start all services concurrently
        start_tasks = []
        for service in service_manager.values():
            start_tasks.append(service.start())
        
        start_results = await asyncio.gather(*start_tasks, return_exceptions=True)
        
        # Check all services started successfully
        for i, result in enumerate(start_results):
            service_name = list(service_manager.keys())[i]
            assert result is True, f"Service {service_name} failed to start: {result}"
        
        # Simulate concurrent operations
        operation_tasks = []
        
        # Training service operations
        training_service = service_manager["training"]
        training_service.get_training_status = AsyncMock(return_value=TrainingState.TRAINING)
        operation_tasks.append(training_service.get_training_status())
        
        # Play service operations
        play_service = service_manager["play"]
        play_service.get_play_status = AsyncMock(return_value=PlayState.RUNNING)
        operation_tasks.append(play_service.get_play_status())
        
        # Deployment service operations
        deployment_service = service_manager["deployment"]
        deployment_service.get_deployment_status = AsyncMock(return_value=DeploymentState.COMPLETED)
        operation_tasks.append(deployment_service.get_deployment_status())
        
        # Execute all operations concurrently
        operation_results = await asyncio.gather(*operation_tasks, return_exceptions=True)
        
        # Verify all operations completed successfully
        assert operation_results[0] == TrainingState.TRAINING
        assert operation_results[1] == PlayState.RUNNING
        assert operation_results[2] == DeploymentState.COMPLETED
        
        logger.info("Concurrent services test passed!")
    
    @pytest.mark.asyncio
    async def test_service_dependency_chain(self, service_manager):
        """Test service dependency chain (config -> training -> play -> deploy)"""
        
        logger.info("Testing service dependency chain...")
        
        # Initialize services in dependency order
        config_service = service_manager["config"]
        training_service = service_manager["training"]
        play_service = service_manager["play"]
        deployment_service = service_manager["deployment"]
        
        # Step 1: Config service
        await config_service.initialize()
        await config_service.start()
        
        config_service.validate_config = AsyncMock(return_value=True)
        config_valid = await config_service.validate_config()
        assert config_valid is True
        
        # Step 2: Training service (depends on config)
        await training_service.initialize()
        await training_service.start()
        
        training_service.start_training = AsyncMock(return_value=True)
        training_service.wait_for_completion = AsyncMock(return_value=True)
        
        training_started = await training_service.start_training()
        assert training_started is True
        
        training_completed = await training_service.wait_for_completion()
        assert training_completed is True
        
        # Step 3: Play service (depends on trained model)
        await play_service.initialize()
        await play_service.start()
        
        play_service.load_trained_model = AsyncMock(return_value=True)
        play_service.run_evaluation = AsyncMock(return_value={"success": True})
        
        model_loaded = await play_service.load_trained_model()
        assert model_loaded is True
        
        eval_results = await play_service.run_evaluation()
        assert eval_results["success"] is True
        
        # Step 4: Deployment service (depends on validated model)
        await deployment_service.initialize()
        await deployment_service.start()
        
        deployment_service.prepare_deployment = AsyncMock(return_value=True)
        deployment_service.execute_deployment = AsyncMock(return_value=True)
        
        deployment_prepared = await deployment_service.prepare_deployment()
        assert deployment_prepared is True
        
        deployment_executed = await deployment_service.execute_deployment()
        assert deployment_executed is True
        
        logger.info("Service dependency chain test passed!")
    
    @pytest.mark.asyncio
    async def test_system_resource_monitoring(self, service_manager, test_environment):
        """Test system resource monitoring during operations"""
        
        logger.info("Testing system resource monitoring...")
        
        # Initialize resource monitoring
        from zenoh_services.core.fault_detection import get_fault_detection_system
        
        fault_detection = get_fault_detection_system()
        
        # Register all services for monitoring
        for service_name, service in service_manager.items():
            monitor = fault_detection.register_service(service_name)
            
            # Add basic health check
            def health_check():
                return True  # Mock healthy state
            
            from zenoh_services.core.fault_detection import HealthCheck
            health_check_obj = HealthCheck(
                name=f"{service_name}_health",
                check_function=health_check,
                interval=1.0,
                timeout=0.5
            )
            
            monitor.add_health_check(health_check_obj)
        
        # Start system monitoring
        await fault_detection.start_system_monitoring()
        
        # Let monitoring run for a short period
        await asyncio.sleep(2.0)
        
        # Check system health
        system_health = fault_detection.get_system_health()
        
        assert "services" in system_health
        assert "system_metrics" in system_health
        assert "global_checks" in system_health
        
        # Verify all registered services are being monitored
        for service_name in service_manager.keys():
            assert service_name in system_health["services"]
            service_health = system_health["services"][service_name]
            assert "status" in service_health
            assert "metrics" in service_health
        
        # Stop monitoring
        await fault_detection.stop_system_monitoring()
        
        logger.info("System resource monitoring test passed!")


class TestDataFlowIntegration:
    """Test data flow between services"""
    
    @pytest.mark.asyncio
    async def test_training_data_pipeline(self, service_manager):
        """Test training data pipeline flow"""
        
        logger.info("Testing training data pipeline...")
        
        training_service = service_manager["training"]
        simulation_service = service_manager["simulation"]
        
        await training_service.initialize()
        await simulation_service.initialize()
        
        # Mock data pipeline operations
        simulation_service.generate_training_data = AsyncMock(return_value={
            "observations": [1, 2, 3, 4],
            "actions": [0.1, 0.2],
            "rewards": [1.0, 0.5],
            "dones": [False, True]
        })
        
        training_service.process_training_batch = AsyncMock(return_value=True)
        training_service.update_policy = AsyncMock(return_value={"loss": 0.1})
        
        # Generate training data
        training_data = await simulation_service.generate_training_data()
        assert training_data is not None
        assert "observations" in training_data
        assert "actions" in training_data
        assert "rewards" in training_data
        assert "dones" in training_data
        
        # Process training batch
        batch_processed = await training_service.process_training_batch(training_data)
        assert batch_processed is True
        
        # Update policy
        update_result = await training_service.update_policy()
        assert update_result is not None
        assert "loss" in update_result
        assert isinstance(update_result["loss"], (int, float))
        
        logger.info("Training data pipeline test passed!")
    
    @pytest.mark.asyncio
    async def test_model_deployment_pipeline(self, service_manager, test_environment):
        """Test model deployment pipeline"""
        
        logger.info("Testing model deployment pipeline...")
        
        training_service = service_manager["training"]
        deployment_service = service_manager["deployment"]
        temp_dir = test_environment["temp_dir"]
        
        await training_service.initialize()
        await deployment_service.initialize()
        
        # Mock model pipeline operations
        model_path = os.path.join(temp_dir, "trained_model.pt")
        onnx_path = os.path.join(temp_dir, "deployed_model.onnx")
        
        training_service.save_model = AsyncMock(return_value=model_path)
        deployment_service.convert_model = AsyncMock(return_value=onnx_path)
        deployment_service.optimize_model = AsyncMock(return_value=True)
        deployment_service.validate_deployment = AsyncMock(return_value={
            "latency_ms": 5.2,
            "accuracy": 0.95,
            "memory_mb": 128
        })
        
        # Save trained model
        saved_model = await training_service.save_model()
        assert saved_model == model_path
        
        # Convert model for deployment
        converted_model = await deployment_service.convert_model(saved_model, "onnx")
        assert converted_model == onnx_path
        
        # Optimize model
        optimization_result = await deployment_service.optimize_model(converted_model)
        assert optimization_result is True
        
        # Validate deployment
        validation_results = await deployment_service.validate_deployment(converted_model)
        assert validation_results is not None
        assert "latency_ms" in validation_results
        assert "accuracy" in validation_results
        assert "memory_mb" in validation_results
        
        logger.info("Model deployment pipeline test passed!")
    
    @pytest.mark.asyncio
    async def test_configuration_propagation(self, service_manager):
        """Test configuration changes propagating through system"""
        
        logger.info("Testing configuration propagation...")
        
        config_service = service_manager["config"]
        training_service = service_manager["training"]
        simulation_service = service_manager["simulation"]
        
        # Initialize services
        for service in [config_service, training_service, simulation_service]:
            await service.initialize()
        
        # Mock configuration operations
        new_config = {
            "learning_rate": 0.002,
            "batch_size": 4096,
            "num_envs": 128,
            "dt": 0.005
        }
        
        config_service.update_global_config = AsyncMock(return_value=True)
        config_service.notify_config_change = AsyncMock(return_value=True)
        
        training_service.apply_config_update = AsyncMock(return_value=True)
        simulation_service.apply_config_update = AsyncMock(return_value=True)
        
        # Update configuration
        config_updated = await config_service.update_global_config(new_config)
        assert config_updated is True
        
        # Notify all services of configuration change
        notification_sent = await config_service.notify_config_change()
        assert notification_sent is True
        
        # Verify services received and applied configuration
        training_applied = await training_service.apply_config_update(new_config)
        assert training_applied is True
        
        simulation_applied = await simulation_service.apply_config_update(new_config)
        assert simulation_applied is True
        
        logger.info("Configuration propagation test passed!")


# Performance and stress testing helper functions
def generate_load_test_data(num_samples: int = 1000) -> List[Dict]:
    """Generate test data for load testing"""
    import random
    
    return [
        {
            "timestamp": time.time() + i * 0.001,
            "observation": [random.random() for _ in range(48)],
            "action": [random.random() for _ in range(12)],
            "reward": random.random() * 100,
            "done": random.choice([True, False])
        }
        for i in range(num_samples)
    ]


async def simulate_concurrent_users(num_users: int, operation_func, *args, **kwargs):
    """Simulate concurrent users performing operations"""
    tasks = []
    
    for user_id in range(num_users):
        task = asyncio.create_task(operation_func(f"user_{user_id}", *args, **kwargs))
        tasks.append(task)
    
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # Count successful operations
    successful = sum(1 for result in results if not isinstance(result, Exception))
    failed = len(results) - successful
    
    return {
        "total_users": num_users,
        "successful_operations": successful,
        "failed_operations": failed,
        "success_rate": successful / num_users if num_users > 0 else 0.0,
        "results": results
    }


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v", "-s", "--tb=short"])