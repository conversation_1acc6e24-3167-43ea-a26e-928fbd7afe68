"""
Simple integration test to verify basic functionality.
"""

import pytest
import asyncio
import tempfile
import shutil
from unittest.mock import <PERSON><PERSON>ock, patch

from zenoh_services.core.enhanced_session_manager import EnhancedZenohSessionManager, EnhancedZenohConfig
from zenoh_services.core.topic_manager import TopicManager
from zenoh_services.core.qos_persistence import Q<PERSON>SManager, QoSProfile
from zenoh_services.core.message_format import MessageFactory


class MockZenohSession:
    """Mock Zenoh session for testing"""
    
    def __init__(self):
        self.publishers = {}
        self.subscribers = {}
        self.is_closed = False
    
    def declare_publisher(self, topic):
        mock_pub = MagicMock()
        mock_pub.put = MagicMock()
        self.publishers[topic] = mock_pub
        return mock_pub
    
    def declare_subscriber(self, topic, callback):
        mock_sub = MagicMock()
        mock_sub.undeclare = MagicMock()
        self.subscribers[topic] = mock_sub
        return mock_sub
    
    def close(self):
        self.is_closed = True


@pytest.mark.asyncio
async def test_basic_integration():
    """Test basic integration of components"""
    # Create mock session
    mock_session = MockZenohSession()
    
    # Create session manager
    config = EnhancedZenohConfig(
        router_endpoints=["tcp/127.0.0.1:7447"],
        enable_heartbeat=False,
        enable_metrics=True
    )
    
    session_manager = EnhancedZenohSessionManager(config, "test_service")
    
    with patch('zenoh.open', return_value=mock_session):
        # Initialize session manager
        result = await session_manager.initialize()
        assert result is True
        assert session_manager.is_initialized is True
        
        # Create topic manager
        topic_manager = TopicManager(session_manager)
        
        # Test topic registration
        topic = "legged_gym/test/integration"
        result = await topic_manager.register_topic(topic)
        assert result is True
        assert topic in topic_manager.registered_topics
        
        # Test publisher creation
        result = await topic_manager.create_publisher(topic)
        assert result is True
        
        # Test QoS manager
        qos_manager = QoSManager()
        qos_manager.set_topic_profile(topic, QoSProfile.SENSOR_DATA)
        qos_config = qos_manager.get_topic_qos(topic)
        assert qos_config is not None
        
        # Test message creation and publishing
        message = MessageFactory.create_data({"test": "data"}, "test_source")
        await topic_manager.publish_message(topic, message)
        
        # Verify publisher was called
        assert mock_session.publishers[topic].put.called
        
        # Cleanup
        await session_manager.shutdown()


@pytest.mark.asyncio
async def test_pattern_matching():
    """Test pattern matching functionality"""
    # Create components without full initialization
    mock_session = MockZenohSession()
    config = EnhancedZenohConfig(enable_heartbeat=False, enable_metrics=False)
    session_manager = EnhancedZenohSessionManager(config, "test_service")
    
    with patch('zenoh.open', return_value=mock_session):
        await session_manager.initialize()
        
        topic_manager = TopicManager(session_manager)
        
        # Test pattern matching
        system_topic = "legged_gym/system/health"
        pattern_match = topic_manager._find_matching_pattern(system_topic)
        
        assert pattern_match is not None
        assert pattern_match.pattern == "legged_gym/system/*"
        
        await session_manager.shutdown()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])