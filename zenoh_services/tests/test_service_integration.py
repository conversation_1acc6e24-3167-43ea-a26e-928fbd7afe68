"""
Integration tests for training and play services
Tests end-to-end functionality and service coordination
"""

import pytest
import asyncio
import time
import tempfile
import shutil
from unittest.mock import MagicMock, patch, AsyncMock
from dataclasses import dataclass

from zenoh_services.core.enhanced_session_manager import (
    EnhancedZenohSessionManager, EnhancedZenohConfig
)
from zenoh_services.core.topic_manager import TopicManager
from zenoh_services.services.training_service import (
    TrainingService, TrainingServiceConfig, create_training_service
)
from zenoh_services.services.play_service import (
    PlayService, PlayServiceConfig, create_play_service
)
from zenoh_services.core.data_models import TrainingConfig, PlayConfig, TrainingCommand
from zenoh_services.core.message_format import MessageFactory
from zenoh_services.core import topics


class MockZenohSession:
    """Mock Zenoh session for testing"""
    
    def __init__(self):
        self.publishers = {}
        self.subscribers = {}
        self.is_closed = False
        self.published_messages = []
    
    def declare_publisher(self, topic):
        mock_pub = MagicMock()
        mock_pub.put = MagicMock(side_effect=lambda data: self.published_messages.append((topic, data)))
        self.publishers[topic] = mock_pub
        return mock_pub
    
    def declare_subscriber(self, topic, callback):
        mock_sub = MagicMock()
        mock_sub.undeclare = MagicMock()
        self.subscribers[topic] = mock_sub
        return mock_sub
    
    def close(self):
        self.is_closed = True


class MockEnvironment:
    """Mock environment for testing without Isaac Gym"""
    
    def __init__(self, num_envs=4):
        self.num_envs = num_envs
        self.num_obs = 48
        self.num_privileged_obs = None
        self.num_actions = 12
        self.device = "cpu"
        
        # Mock Isaac Gym attributes
        self.cfg = MagicMock()
        self.cfg.control.action_scale = 1.0
        
        self.dof_pos = MagicMock()
        self.dof_vel = MagicMock()
        self.torques = MagicMock()
        self.root_states = MagicMock()
        self.base_lin_vel = MagicMock()
        self.base_ang_vel = MagicMock()
        self.commands = MagicMock()
        self.contact_forces = MagicMock()
        self.feet_indices = [0, 1, 2, 3]
        
    def get_observations(self):
        import torch
        return torch.zeros(self.num_envs, self.num_obs)
    
    def get_privileged_observations(self):
        return None
    
    def step(self, actions):
        import torch
        obs = self.get_observations()
        critic_obs = None
        rewards = torch.zeros(self.num_envs)
        dones = torch.zeros(self.num_envs, dtype=torch.bool)
        infos = {}
        return obs, critic_obs, rewards, dones, infos
    
    def reset(self):
        obs = self.get_observations()
        return obs, None


class MockPPORunner:
    """Mock PPO runner for testing"""
    
    def __init__(self):
        self.current_learning_iteration = 0
        self.save_interval = 50
        self.log_dir = None
        
    def learn(self, num_learning_iterations, init_at_random_ep_len=False):
        # Simulate training iterations
        for i in range(num_learning_iterations):
            self.current_learning_iteration += i + 1
            time.sleep(0.01)  # Simulate some work
        
    def save(self, path):
        pass
        
    def get_inference_policy(self, device="cpu"):
        def mock_policy(obs):
            import torch
            return torch.zeros(obs.shape[0], 12)  # 12 actions
        return mock_policy


@pytest.fixture
def mock_zenoh_session():
    """Create mock Zenoh session"""
    return MockZenohSession()


@pytest.fixture
async def session_manager(mock_zenoh_session):
    """Create session manager with mocked Zenoh"""
    config = EnhancedZenohConfig(
        router_endpoints=["tcp/127.0.0.1:7447"],
        enable_heartbeat=False,
        enable_metrics=False
    )
    
    manager = EnhancedZenohSessionManager(config, "test_service")
    
    with patch('zenoh.open', return_value=mock_zenoh_session):
        await manager.initialize()
    
    yield manager
    
    await manager.shutdown()


class TestTrainingServiceIntegration:
    """Integration tests for training service"""
    
    @pytest.mark.asyncio
    async def test_training_service_lifecycle(self, session_manager):
        """Test complete training service lifecycle"""
        config = TrainingServiceConfig(
            task_name="test_task",
            num_envs=4,
            max_iterations=10,
            metrics_publish_interval=0.1,
            status_publish_interval=0.2
        )
        
        # Mock the task registry and environment creation
        with patch('zenoh_services.services.training_service.task_registry') as mock_registry, \
             patch('zenoh_services.services.enhanced_ppo_runner.task_registry') as mock_ppo_registry:
            
            # Setup mock environment and runner
            mock_env = MockEnvironment()
            mock_runner = MockPPORunner()
            mock_train_cfg = {"runner": {"experiment_name": "test", "max_iterations": 10}}
            
            mock_registry.make_env.return_value = (mock_env, {})
            mock_registry.make_alg_runner.return_value = (mock_runner, mock_train_cfg, "/tmp/logs")
            mock_ppo_registry.make_env.return_value = (mock_env, {})
            mock_ppo_registry.make_alg_runner.return_value = (mock_runner, mock_train_cfg, "/tmp/logs")
            
            # Create training service
            service = await create_training_service(session_manager, config)
            
            assert service.is_initialized
            assert service.state.value == "idle"
            
            # Test training start
            training_config = TrainingConfig(
                task_name="test_task",
                num_envs=4,
                max_iterations=10
            )
            
            result = await service.start_training(training_config)
            assert result is True
            assert service.state.value == "training"
            
            # Wait for training to start
            await asyncio.sleep(0.5)
            
            # Test training stop
            result = await service.stop_training()
            assert result is True
            assert service.state.value == "idle"
            
            # Cleanup
            await service.shutdown()
    
    @pytest.mark.asyncio
    async def test_training_command_handling(self, session_manager):
        """Test training command message handling"""
        config = TrainingServiceConfig(task_name="test_task", max_iterations=5)
        
        with patch('zenoh_services.services.training_service.task_registry') as mock_registry:
            mock_env = MockEnvironment()
            mock_runner = MockPPORunner()
            mock_train_cfg = {"runner": {"experiment_name": "test", "max_iterations": 5}}
            
            mock_registry.make_env.return_value = (mock_env, {})
            mock_registry.make_alg_runner.return_value = (mock_runner, mock_train_cfg, "/tmp/logs")
            
            service = await create_training_service(session_manager, config)
            
            # Create training command message
            command = TrainingCommand(
                command="start",
                config=TrainingConfig(task_name="test_task", max_iterations=5)
            )
            
            # Simulate receiving command
            await service._handle_training_command(command.to_dict())
            
            # Should have started training
            assert service.state.value == "training"
            
            # Send stop command
            stop_command = TrainingCommand(command="stop")
            await service._handle_training_command(stop_command.to_dict())
            
            # Should have stopped
            await asyncio.sleep(0.1)
            assert service.state.value == "idle"
            
            await service.shutdown()


class TestPlayServiceIntegration:
    """Integration tests for play service"""
    
    @pytest.mark.asyncio
    async def test_play_service_lifecycle(self, session_manager):
        """Test complete play service lifecycle"""
        config = PlayServiceConfig(
            task_name="test_task",
            num_envs=4,
            render=False,
            record_video=False,
            robot_state_publish_interval=0.1
        )
        
        # Mock the task registry and environment creation
        with patch('zenoh_services.services.play_service.task_registry') as mock_registry:
            
            # Setup mock environment and configs
            mock_env = MockEnvironment()
            mock_env.set_camera = MagicMock()
            
            mock_env_cfg = MagicMock()
            mock_env_cfg.env.num_envs = 50
            mock_env_cfg.terrain.mesh_type = "plane" 
            mock_env_cfg.terrain.num_rows = 5
            mock_env_cfg.terrain.num_cols = 5
            mock_env_cfg.noise.add_noise = True
            mock_env_cfg.noise.curriculum = True
            mock_env_cfg.domain_rand = MagicMock()
            mock_env_cfg.sim.dt = 0.02
            
            mock_train_cfg = MagicMock()
            mock_train_cfg.runner.resume = False
            mock_train_cfg.runner.experiment_name = "test"
            
            mock_ppo_runner = MockPPORunner()
            mock_ppo_runner.alg = MagicMock()
            mock_ppo_runner.alg.actor_critic = MagicMock()
            
            mock_registry.get_cfgs.return_value = (mock_env_cfg, mock_train_cfg)
            mock_registry.make_env.return_value = (mock_env, {})
            mock_registry.make_alg_runner.return_value = (mock_ppo_runner, mock_train_cfg, "/tmp/logs")
            
            # Create play service
            service = await create_play_service(session_manager, config)
            
            assert service.is_initialized
            assert service.state.value == "idle"
            
            # Test simulation start
            result = await service.start_simulation()
            assert result is True
            assert service.state.value == "running"
            
            # Wait for simulation to run briefly
            await asyncio.sleep(0.3)
            
            # Test robot command handling
            robot_command = {
                "velocity": {
                    "x": 1.0,
                    "y": 0.5,
                    "yaw": 0.2,
                    "height": 0.0
                }
            }
            
            await service._handle_robot_command(robot_command)
            
            # Check velocity commands were updated
            assert service.velocity_commands[0] == 1.0
            assert service.velocity_commands[1] == 0.5
            assert service.velocity_commands[2] == 0.2
            
            # Test simulation stop
            result = await service.stop_simulation()
            assert result is True
            assert service.state.value == "idle"
            
            # Cleanup
            await service.shutdown()


class TestServiceCoordination:
    """Test coordination between training and play services"""
    
    @pytest.mark.asyncio
    async def test_training_to_play_workflow(self, session_manager):
        """Test workflow from training to play testing"""
        # Setup training service
        training_config = TrainingServiceConfig(
            task_name="test_task",
            max_iterations=5,
            experiment_name="integration_test"
        )
        
        # Setup play service  
        play_config = PlayServiceConfig(
            task_name="test_task",
            num_envs=4,
            render=False
        )
        
        with patch('zenoh_services.services.training_service.task_registry') as mock_train_registry, \
             patch('zenoh_services.services.play_service.task_registry') as mock_play_registry:
            
            # Mock training environment
            mock_train_env = MockEnvironment()
            mock_train_runner = MockPPORunner()
            mock_train_cfg = {"runner": {"experiment_name": "integration_test", "max_iterations": 5}}
            
            mock_train_registry.make_env.return_value = (mock_train_env, {})
            mock_train_registry.make_alg_runner.return_value = (mock_train_runner, mock_train_cfg, "/tmp/logs")
            
            # Mock play environment
            mock_play_env = MockEnvironment()
            mock_play_env.set_camera = MagicMock()
            
            mock_env_cfg = MagicMock()
            mock_env_cfg.env.num_envs = 50
            mock_env_cfg.terrain.mesh_type = "plane"
            mock_env_cfg.terrain.num_rows = 5
            mock_env_cfg.terrain.num_cols = 5
            mock_env_cfg.noise.add_noise = True
            mock_env_cfg.domain_rand = MagicMock()
            mock_env_cfg.sim.dt = 0.02
            
            mock_train_cfg_play = MagicMock()
            mock_train_cfg_play.runner.resume = False
            mock_train_cfg_play.runner.experiment_name = "integration_test"
            
            mock_play_runner = MockPPORunner()
            mock_play_runner.alg = MagicMock()
            mock_play_runner.alg.actor_critic = MagicMock()
            
            mock_play_registry.get_cfgs.return_value = (mock_env_cfg, mock_train_cfg_play)
            mock_play_registry.make_env.return_value = (mock_play_env, {})
            mock_play_registry.make_alg_runner.return_value = (mock_play_runner, mock_train_cfg_play, "/tmp/logs")
            
            # Create services
            training_service = await create_training_service(session_manager, training_config)
            play_service = await create_play_service(session_manager, play_config)
            
            # Start training
            training_cfg = TrainingConfig(task_name="test_task", max_iterations=5)
            await training_service.start_training(training_cfg)
            
            # Wait for training to complete
            await asyncio.sleep(0.5)
            await training_service.stop_training()
            
            # Start play service to test trained model
            await play_service.start_simulation()
            
            # Let it run briefly
            await asyncio.sleep(0.3)
            
            # Stop play service
            await play_service.stop_simulation()
            
            # Cleanup
            await training_service.shutdown()
            await play_service.shutdown()
    
    @pytest.mark.asyncio
    async def test_concurrent_services(self, session_manager):
        """Test running multiple services concurrently"""
        # This test verifies that services can run concurrently without conflicts
        
        training_config = TrainingServiceConfig(
            task_name="test_task_1",
            max_iterations=5
        )
        
        play_config = PlayServiceConfig(
            task_name="test_task_2", 
            num_envs=4,
            render=False
        )
        
        with patch('zenoh_services.services.training_service.task_registry') as mock_train_registry, \
             patch('zenoh_services.services.play_service.task_registry') as mock_play_registry, \
             patch('zenoh_services.services.enhanced_ppo_runner.task_registry') as mock_ppo_registry:
            
            # Setup mocks for training
            mock_train_env = MockEnvironment()
            mock_train_runner = MockPPORunner()
            mock_train_cfg = {"runner": {"experiment_name": "concurrent_test", "max_iterations": 5}}
            
            mock_train_registry.make_env.return_value = (mock_train_env, {})
            mock_train_registry.make_alg_runner.return_value = (mock_train_runner, mock_train_cfg, "/tmp/logs")
            mock_ppo_registry.make_env.return_value = (mock_train_env, {})
            mock_ppo_registry.make_alg_runner.return_value = (mock_train_runner, mock_train_cfg, "/tmp/logs")
            
            # Setup mocks for play
            mock_play_env = MockEnvironment()
            mock_play_env.set_camera = MagicMock()
            
            mock_env_cfg = MagicMock()
            mock_env_cfg.env.num_envs = 50
            mock_env_cfg.terrain.mesh_type = "plane"
            mock_env_cfg.terrain.num_rows = 5
            mock_env_cfg.terrain.num_cols = 5
            mock_env_cfg.noise.add_noise = True
            mock_env_cfg.domain_rand = MagicMock()
            mock_env_cfg.sim.dt = 0.02
            
            mock_play_train_cfg = MagicMock()
            mock_play_train_cfg.runner.resume = False
            mock_play_train_cfg.runner.experiment_name = "concurrent_test"
            
            mock_play_runner = MockPPORunner()
            mock_play_runner.alg = MagicMock()
            mock_play_runner.alg.actor_critic = MagicMock()
            
            mock_play_registry.get_cfgs.return_value = (mock_env_cfg, mock_play_train_cfg)
            mock_play_registry.make_env.return_value = (mock_play_env, {})
            mock_play_registry.make_alg_runner.return_value = (mock_play_runner, mock_play_train_cfg, "/tmp/logs")
            
            # Create services
            training_service = await create_training_service(session_manager, training_config)
            play_service = await create_play_service(session_manager, play_config)
            
            # Start both services concurrently
            training_cfg = TrainingConfig(task_name="test_task_1", max_iterations=3)
            
            await asyncio.gather(
                training_service.start_training(training_cfg),
                play_service.start_simulation()
            )
            
            # Let them run concurrently
            await asyncio.sleep(0.5)
            
            # Stop both services
            await asyncio.gather(
                training_service.stop_training(),
                play_service.stop_simulation()
            )
            
            # Verify both services are idle
            assert training_service.state.value == "idle"
            assert play_service.state.value == "idle"
            
            # Cleanup
            await training_service.shutdown()
            await play_service.shutdown()


class TestErrorHandling:
    """Test error handling in service integration"""
    
    @pytest.mark.asyncio
    async def test_training_service_initialization_error(self, session_manager):
        """Test training service handles initialization errors gracefully"""
        config = TrainingServiceConfig(task_name="invalid_task")
        
        # Mock registry to raise exception
        with patch('zenoh_services.services.training_service.task_registry') as mock_registry:
            mock_registry.make_env.side_effect = Exception("Invalid task")
            
            service = TrainingService(session_manager, config)
            
            # Initialize should succeed but training start should fail
            init_result = await service.initialize()
            assert init_result is True
            
            # Start training should fail gracefully
            training_cfg = TrainingConfig(task_name="invalid_task")
            start_result = await service.start_training(training_cfg)
            assert start_result is False
            assert service.state.value == "error"
            
            await service.shutdown()
    
    @pytest.mark.asyncio
    async def test_play_service_environment_error(self, session_manager):
        """Test play service handles environment errors gracefully"""
        config = PlayServiceConfig(task_name="invalid_task")
        
        with patch('zenoh_services.services.play_service.task_registry') as mock_registry:
            mock_registry.get_cfgs.side_effect = Exception("Invalid task configuration")
            
            service = PlayService(session_manager, config)
            
            # Initialize should succeed
            init_result = await service.initialize() 
            assert init_result is True
            
            # Start simulation should fail gracefully
            start_result = await service.start_simulation()
            assert start_result is False
            assert service.state.value == "error"
            
            await service.shutdown()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])