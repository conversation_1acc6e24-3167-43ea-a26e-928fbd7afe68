"""
Unit tests for error handling and fault detection system
"""

import pytest
import asyncio
import time
import threading
from unittest.mock import Mo<PERSON>, AsyncMock, patch
from typing import Dict, Any

from zenoh_services.core.error_handler import (
    <PERSON>rror<PERSON><PERSON><PERSON>, ErrorInfo, ErrorContext, ErrorCategory, ErrorSeverity,
    RecoveryStrategy, RecoveryAction, ServiceHealth, HealthStatus,
    get_error_handler, initialize_error_handler
)
from zenoh_services.core.fault_detection import (
    FaultDetectionSystem, ServiceMonitor, HealthCheck, ServiceMetrics,
    FailureType, RecoveryAction as FaultRecoveryAction
)
from zenoh_services.core.notification_system import (
    NotificationManager, Notification, NotificationType, NotificationPriority,
    UserPrompt
)


class TestErrorHandler:
    """Test cases for ErrorHandler"""
    
    @pytest.fixture
    def error_handler(self):
        """Create error handler for testing"""
        return ErrorHandler(enable_recovery=True)
    
    @pytest.fixture
    def sample_error_context(self):
        """Create sample error context"""
        return ErrorContext(
            service_name="test_service",
            service_state="running",
            timestamp=time.time(),
            thread_id=threading.get_ident(),
            function_name="test_function"
        )
    
    def test_error_handler_initialization(self, error_handler):
        """Test error handler initialization"""
        assert error_handler is not None
        assert error_handler.enable_recovery is True
        assert len(error_handler.recovery_strategies) > 0
        assert error_handler.logger is not None
    
    @pytest.mark.asyncio
    async def test_handle_error(self, error_handler, sample_error_context):
        """Test error handling"""
        exception = ValueError("Test error")
        
        error_id = await error_handler.handle_error(
            exception, sample_error_context, ErrorCategory.SERVICE, ErrorSeverity.MEDIUM
        )
        
        assert error_id is not None
        assert error_id in error_handler.errors
        
        error_info = error_handler.errors[error_id]
        assert error_info.message == "Test error"
        assert error_info.category == ErrorCategory.SERVICE
        assert error_info.severity == ErrorSeverity.MEDIUM
        assert error_info.context.service_name == "test_service"
    
    def test_register_strategy(self, error_handler):
        """Test registering recovery strategy"""
        strategy = RecoveryStrategy(
            error_pattern="test.*error",
            category=ErrorCategory.SERVICE,
            severity=ErrorSeverity.HIGH,
            action=RecoveryAction.RETRY
        )
        
        initial_count = len(error_handler.recovery_strategies)
        error_handler.register_strategy(strategy)
        
        assert len(error_handler.recovery_strategies) == initial_count + 1
        assert strategy in error_handler.recovery_strategies
    
    def test_service_health_tracking(self, error_handler, sample_error_context):
        """Test service health tracking"""
        service_name = "test_service"
        error_info = ErrorInfo(
            error_id="test_error",
            timestamp=time.time(),
            category=ErrorCategory.SERVICE,
            severity=ErrorSeverity.HIGH,
            message="Test error",
            exception_type="ValueError",
            traceback="Test traceback",
            context=sample_error_context
        )
        
        error_handler._update_service_health(service_name, error_info)
        
        health = error_handler.get_service_health(service_name)
        assert health is not None
        assert health.service_name == service_name
        assert health.error_count == 1
        assert health.status in [HealthStatus.DEGRADED, HealthStatus.UNHEALTHY]
    
    def test_error_statistics(self, error_handler, sample_error_context):
        """Test error statistics generation"""
        # Add some test errors
        for i in range(5):
            error_info = ErrorInfo(
                error_id=f"error_{i}",
                timestamp=time.time(),
                category=ErrorCategory.SERVICE,
                severity=ErrorSeverity.MEDIUM,
                message=f"Test error {i}",
                exception_type="ValueError",
                traceback="Test traceback",
                context=sample_error_context
            )
            error_handler.errors[error_info.error_id] = error_info
            error_handler.error_history.append(error_info)
        
        stats = error_handler.get_error_statistics()
        
        assert stats["total_errors"] == 5
        assert "service" in stats["errors_by_category"]
        assert "medium" in stats["errors_by_severity"]
        assert "test_service" in stats["errors_by_service"]
    
    @pytest.mark.asyncio
    async def test_error_callbacks(self, error_handler, sample_error_context):
        """Test error callbacks"""
        callback_called = False
        received_error = None
        
        def error_callback(error_info):
            nonlocal callback_called, received_error
            callback_called = True
            received_error = error_info
        
        error_handler.add_error_callback(error_callback)
        
        exception = ValueError("Callback test")
        await error_handler.handle_error(
            exception, sample_error_context, ErrorCategory.SERVICE, ErrorSeverity.MEDIUM
        )
        
        assert callback_called is True
        assert received_error is not None
        assert received_error.message == "Callback test"


class TestFaultDetectionSystem:
    """Test cases for FaultDetectionSystem"""
    
    @pytest.fixture
    def fault_detection_system(self):
        """Create fault detection system for testing"""
        return FaultDetectionSystem()
    
    @pytest.fixture
    def mock_health_check(self):
        """Create mock health check"""
        return HealthCheck(
            name="test_check",
            check_function=lambda: True,
            interval=1.0,
            timeout=0.5
        )
    
    def test_fault_detection_initialization(self, fault_detection_system):
        """Test fault detection system initialization"""
        assert fault_detection_system is not None
        assert len(fault_detection_system.global_health_checks) > 0
        assert fault_detection_system.system_monitoring is False
    
    def test_service_registration(self, fault_detection_system):
        """Test service registration"""
        service_name = "test_service"
        monitor = fault_detection_system.register_service(service_name)
        
        assert monitor is not None
        assert monitor.service_name == service_name
        assert service_name in fault_detection_system.service_monitors
    
    def test_global_health_check_addition(self, fault_detection_system, mock_health_check):
        """Test adding global health check"""
        initial_count = len(fault_detection_system.global_health_checks)
        fault_detection_system.add_global_health_check(mock_health_check)
        
        assert len(fault_detection_system.global_health_checks) == initial_count + 1
        assert "test_check" in fault_detection_system.global_health_checks
    
    def test_system_health_retrieval(self, fault_detection_system):
        """Test system health retrieval"""
        # Register a service
        service_name = "test_service"
        fault_detection_system.register_service(service_name)
        
        health = fault_detection_system.get_system_health()
        
        assert "services" in health
        assert "global_checks" in health
        assert "system_metrics" in health
        assert service_name in health["services"]


class TestServiceMonitor:
    """Test cases for ServiceMonitor"""
    
    @pytest.fixture
    def service_monitor(self):
        """Create service monitor for testing"""
        error_handler = ErrorHandler()
        return ServiceMonitor("test_service", error_handler)
    
    @pytest.fixture
    def mock_health_check(self):
        """Create mock health check"""
        return HealthCheck(
            name="test_check",
            check_function=lambda: True,
            interval=0.1,
            timeout=0.05
        )
    
    def test_service_monitor_initialization(self, service_monitor):
        """Test service monitor initialization"""
        assert service_monitor.service_name == "test_service"
        assert service_monitor.is_monitoring is False
        assert service_monitor.metrics is not None
    
    def test_health_check_addition(self, service_monitor, mock_health_check):
        """Test adding health check"""
        service_monitor.add_health_check(mock_health_check)
        
        assert "test_check" in service_monitor.health_checks
        assert service_monitor.health_checks["test_check"] == mock_health_check
    
    def test_recovery_action_addition(self, service_monitor):
        """Test adding recovery action"""
        action = FaultRecoveryAction(
            action_name="test_action",
            action_function=lambda: True
        )
        
        service_monitor.add_recovery_action(FailureType.UNRESPONSIVE, action)
        
        assert FailureType.UNRESPONSIVE in service_monitor.recovery_actions
        assert action in service_monitor.recovery_actions[FailureType.UNRESPONSIVE]
    
    def test_metrics_retrieval(self, service_monitor):
        """Test metrics retrieval"""
        metrics = service_monitor.get_metrics()
        assert isinstance(metrics, ServiceMetrics)
        
        history = service_monitor.get_metrics_history()
        assert isinstance(history, list)
    
    def test_failure_detection(self, service_monitor):
        """Test failure detection"""
        # Set high resource usage to trigger failure detection
        service_monitor.metrics.cpu_usage = 95.0
        service_monitor.metrics.memory_usage = 95.0
        service_monitor.metrics.response_time = 10.0
        service_monitor.metrics.error_rate = 0.5
        
        failures = service_monitor._detect_failures()
        
        assert FailureType.RESOURCE_EXHAUSTION in failures
        assert FailureType.PERFORMANCE_DEGRADED in failures
        assert FailureType.HIGH_ERROR_RATE in failures


class TestNotificationSystem:
    """Test cases for NotificationSystem"""
    
    @pytest.fixture
    def mock_session_manager(self):
        """Create mock session manager"""
        session_manager = Mock()
        session_manager.session = Mock()
        return session_manager
    
    @pytest.fixture
    def notification_manager(self, mock_session_manager):
        """Create notification manager for testing"""
        return NotificationManager(mock_session_manager)
    
    @pytest.fixture
    def sample_notification(self):
        """Create sample notification"""
        return Notification(
            notification_id="test_notification",
            type=NotificationType.ERROR,
            priority=NotificationPriority.HIGH,
            title="Test Error",
            message="This is a test error notification",
            timestamp=time.time(),
            service_name="test_service"
        )
    
    @pytest.fixture
    def sample_user_prompt(self):
        """Create sample user prompt"""
        return UserPrompt(
            prompt_id="test_prompt",
            title="Test Prompt",
            message="Please confirm this action",
            prompt_type="confirm",
            timeout=30.0
        )
    
    def test_notification_creation(self, sample_notification):
        """Test notification creation and serialization"""
        assert sample_notification.notification_id == "test_notification"
        assert sample_notification.type == NotificationType.ERROR
        assert sample_notification.priority == NotificationPriority.HIGH
        
        notification_dict = sample_notification.to_dict()
        assert isinstance(notification_dict, dict)
        assert notification_dict["notification_id"] == "test_notification"
        assert notification_dict["type"] == "error"
    
    def test_user_prompt_creation(self, sample_user_prompt):
        """Test user prompt creation and serialization"""
        assert sample_user_prompt.prompt_id == "test_prompt"
        assert sample_user_prompt.prompt_type == "confirm"
        
        prompt_dict = sample_user_prompt.to_dict()
        assert isinstance(prompt_dict, dict)
        assert prompt_dict["prompt_id"] == "test_prompt"
        assert prompt_dict["prompt_type"] == "confirm"
    
    @pytest.mark.asyncio
    async def test_notification_manager_initialization(self, notification_manager):
        """Test notification manager initialization"""
        with patch.object(notification_manager, '_register_topics', new_callable=AsyncMock):
            with patch.object(notification_manager, '_setup_response_subscribers', new_callable=AsyncMock):
                await notification_manager.initialize()
                assert notification_manager.is_initialized is True
    
    def test_error_notification_creation(self, notification_manager):
        """Test creating notification from error info"""
        error_context = ErrorContext(
            service_name="test_service",
            service_state="running",
            timestamp=time.time(),
            thread_id=threading.get_ident(),
            function_name="test_function"
        )
        
        error_info = ErrorInfo(
            error_id="test_error",
            timestamp=time.time(),
            category=ErrorCategory.SERVICE,
            severity=ErrorSeverity.HIGH,
            message="Test error message",
            exception_type="ValueError",
            traceback="Test traceback",
            context=error_context
        )
        
        # Mock the send_notification method
        with patch.object(notification_manager, 'send_notification', new_callable=AsyncMock):
            notification = asyncio.run(notification_manager.create_error_notification(error_info))
            
            assert notification.type == NotificationType.ERROR
            assert notification.priority == NotificationPriority.HIGH
            assert notification.service_name == "test_service"
            assert "Test error message" in notification.message


class TestFaultSimulation:
    """Test cases for fault simulation and recovery"""
    
    @pytest.fixture
    def integrated_system(self):
        """Create integrated system for fault simulation"""
        error_handler = ErrorHandler(enable_recovery=True)
        fault_detection = FaultDetectionSystem(error_handler)
        
        return {
            "error_handler": error_handler,
            "fault_detection": fault_detection
        }
    
    @pytest.mark.asyncio
    async def test_network_failure_simulation(self, integrated_system):
        """Simulate network failure and test recovery"""
        error_handler = integrated_system["error_handler"]
        
        # Simulate network error
        network_exception = ConnectionError("Connection failed")
        error_context = ErrorContext(
            service_name="network_service",
            service_state="connecting",
            timestamp=time.time(),
            thread_id=threading.get_ident(),
            function_name="connect_to_server"
        )
        
        error_id = await error_handler.handle_error(
            network_exception, error_context, ErrorCategory.NETWORK, ErrorSeverity.HIGH
        )
        
        # Verify error was recorded
        assert error_id in error_handler.errors
        error_info = error_handler.errors[error_id]
        assert error_info.category == ErrorCategory.NETWORK
        assert error_info.severity == ErrorSeverity.HIGH
    
    @pytest.mark.asyncio
    async def test_service_crash_simulation(self, integrated_system):
        """Simulate service crash and test recovery"""
        fault_detection = integrated_system["fault_detection"]
        
        # Register a service
        service_monitor = fault_detection.register_service("crash_test_service")
        
        # Create a health check that fails
        crash_check = HealthCheck(
            name="crash_check",
            check_function=lambda: False,  # Always fails
            interval=0.1,
            timeout=0.05,
            failure_threshold=1
        )
        
        service_monitor.add_health_check(crash_check)
        
        # Start monitoring
        await service_monitor.start_monitoring()
        
        # Wait for health check to fail
        await asyncio.sleep(0.2)
        
        # Verify service is marked as unhealthy
        health_status = await service_monitor._run_health_checks()
        assert health_status in [HealthStatus.DEGRADED, HealthStatus.UNHEALTHY, HealthStatus.CRITICAL]
        
        # Stop monitoring
        await service_monitor.stop_monitoring()
    
    @pytest.mark.asyncio
    async def test_resource_exhaustion_simulation(self, integrated_system):
        """Simulate resource exhaustion and test detection"""
        fault_detection = integrated_system["fault_detection"]
        
        # Register a service
        service_monitor = fault_detection.register_service("resource_test_service")
        
        # Simulate high resource usage
        service_monitor.metrics.cpu_usage = 95.0
        service_monitor.metrics.memory_usage = 90.0
        
        # Detect failures
        failures = service_monitor._detect_failures()
        
        assert FailureType.RESOURCE_EXHAUSTION in failures
    
    @pytest.mark.asyncio
    async def test_recovery_action_execution(self, integrated_system):
        """Test recovery action execution"""
        fault_detection = integrated_system["fault_detection"]
        
        # Register a service
        service_monitor = fault_detection.register_service("recovery_test_service")
        
        # Track if recovery action was called
        recovery_called = False
        
        def mock_recovery():
            nonlocal recovery_called
            recovery_called = True
            return True
        
        # Add recovery action
        recovery_action = FaultRecoveryAction(
            action_name="mock_recovery",
            action_function=mock_recovery,
            timeout=1.0
        )
        
        service_monitor.add_recovery_action(FailureType.UNRESPONSIVE, recovery_action)
        
        # Execute recovery
        success = await service_monitor._execute_recovery_action(recovery_action)
        
        assert success is True
        assert recovery_called is True
    
    @pytest.mark.asyncio
    async def test_cascading_failure_handling(self, integrated_system):
        """Test handling of cascading failures"""
        error_handler = integrated_system["error_handler"]
        
        # Simulate multiple related failures
        services = ["service_a", "service_b", "service_c"]
        error_ids = []
        
        for service_name in services:
            exception = RuntimeError(f"{service_name} dependency failed")
            context = ErrorContext(
                service_name=service_name,
                service_state="error",
                timestamp=time.time(),
                thread_id=threading.get_ident(),
                function_name="process_request"
            )
            
            error_id = await error_handler.handle_error(
                exception, context, ErrorCategory.SERVICE, ErrorSeverity.HIGH
            )
            error_ids.append(error_id)
        
        # Verify all errors were recorded
        for error_id in error_ids:
            assert error_id in error_handler.errors
        
        # Check service health degradation
        for service_name in services:
            health = error_handler.get_service_health(service_name)
            assert health is not None
            assert health.status in [HealthStatus.DEGRADED, HealthStatus.UNHEALTHY, HealthStatus.CRITICAL]


class TestErrorHandlerIntegration:
    """Integration tests for complete error handling system"""
    
    @pytest.mark.asyncio
    async def test_end_to_end_error_handling(self):
        """Test complete error handling flow"""
        # Initialize components
        error_handler = ErrorHandler(enable_recovery=True)
        fault_detection = FaultDetectionSystem(error_handler)
        
        # Mock session manager for notification system
        mock_session_manager = Mock()
        mock_session_manager.session = Mock()
        notification_manager = NotificationManager(mock_session_manager)
        
        # Mock topic manager methods
        with patch.object(notification_manager, '_register_topics', new_callable=AsyncMock):
            with patch.object(notification_manager, '_setup_response_subscribers', new_callable=AsyncMock):
                await notification_manager.initialize()
        
        # Register service
        service_monitor = fault_detection.register_service("integration_test_service")
        
        # Add error callback to create notifications
        async def error_notification_callback(error_info):
            await notification_manager.create_error_notification(error_info)
        
        error_handler.add_error_callback(
            lambda error_info: asyncio.create_task(error_notification_callback(error_info))
        )
        
        # Simulate error
        exception = ValueError("Integration test error")
        context = ErrorContext(
            service_name="integration_test_service",
            service_state="processing",
            timestamp=time.time(),
            thread_id=threading.get_ident(),
            function_name="process_data"
        )
        
        # Handle error
        with patch.object(notification_manager, 'send_notification', new_callable=AsyncMock) as mock_send:
            error_id = await error_handler.handle_error(
                exception, context, ErrorCategory.SERVICE, ErrorSeverity.HIGH
            )
            
            # Verify error was handled
            assert error_id in error_handler.errors
            
            # Give some time for callback processing
            await asyncio.sleep(0.1)
        
        # Cleanup
        await fault_detection.shutdown()
        await notification_manager.shutdown()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])