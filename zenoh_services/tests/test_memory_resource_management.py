"""
Task 15.2.2 - 内存管理和资源清理机制测试 (Memory Management and Resource Cleanup Testing)

本模块实现内存管理和资源清理的测试，包括：
- 内存泄漏检测
- 资源清理验证
- 垃圾回收性能测试
- 内存使用优化测试
"""

import asyncio
import gc
import threading
import time
import weakref
import tracemalloc
import psutil
import pytest
from typing import List, Dict, Any, Optional
from unittest.mock import Mock, patch
import json
import sys
import os

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from core.session_manager import SessionManager
from core.topics import TopicManager
from core.message_format import MessageFormatter
from services.training_service import TrainingService
from services.simulation_service import SimulationService


class MemoryTracker:
    """内存跟踪器"""
    
    def __init__(self):
        self.initial_memory = None
        self.peak_memory = 0
        self.memory_samples = []
        self.leaked_objects = []
        self.weak_references = []
        self.monitoring = False
        self.monitor_thread = None
        tracemalloc.start()
    
    def start_tracking(self):
        """开始内存跟踪"""
        gc.collect()  # 强制垃圾回收
        self.initial_memory = psutil.Process().memory_info().rss
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_memory)
        self.monitor_thread.start()
    
    def stop_tracking(self):
        """停止内存跟踪"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join()
        gc.collect()  # 最终垃圾回收
    
    def _monitor_memory(self):
        """监控内存使用"""
        while self.monitoring:
            current_memory = psutil.Process().memory_info().rss
            self.memory_samples.append(current_memory)
            if current_memory > self.peak_memory:
                self.peak_memory = current_memory
            time.sleep(0.1)  # 100ms采样
    
    def add_weak_reference(self, obj):
        """添加弱引用以检测对象是否被正确释放"""
        weak_ref = weakref.ref(obj)
        self.weak_references.append(weak_ref)
        return weak_ref
    
    def check_memory_leak(self) -> Dict[str, Any]:
        """检查内存泄漏"""
        current_memory = psutil.Process().memory_info().rss
        memory_diff = current_memory - self.initial_memory
        
        # 检查弱引用对象是否被释放
        still_alive = [ref for ref in self.weak_references if ref() is not None]
        
        # 获取内存统计
        snapshot = tracemalloc.take_snapshot()
        top_stats = snapshot.statistics('lineno')
        
        return {
            'memory_diff_mb': memory_diff / (1024 * 1024),
            'peak_memory_mb': self.peak_memory / (1024 * 1024),
            'objects_not_released': len(still_alive),
            'memory_growth_rate': self._calculate_growth_rate(),
            'top_memory_usage': [(stat.filename, stat.lineno, stat.size) 
                                for stat in top_stats[:10]]
        }
    
    def _calculate_growth_rate(self) -> float:
        """计算内存增长率"""
        if len(self.memory_samples) < 2:
            return 0.0
        
        # 计算线性增长趋势
        samples = self.memory_samples[-100:]  # 最近100个样本
        if len(samples) < 10:
            return 0.0
        
        # 简单线性回归计算增长率
        n = len(samples)
        sum_x = sum(range(n))
        sum_y = sum(samples)
        sum_xy = sum(i * samples[i] for i in range(n))
        sum_x2 = sum(i * i for i in range(n))
        
        slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)
        return slope


class ResourceManager:
    """资源管理器"""
    
    def __init__(self):
        self.allocated_resources = {}
        self.resource_counter = 0
        self.cleanup_callbacks = []
    
    def allocate_resource(self, resource_type: str, size: int = 1024) -> str:
        """分配资源"""
        resource_id = f"{resource_type}_{self.resource_counter}"
        self.resource_counter += 1
        
        # 模拟资源分配（创建大对象）
        resource_data = bytearray(size)
        self.allocated_resources[resource_id] = {
            'type': resource_type,
            'data': resource_data,
            'size': size,
            'allocated_time': time.time()
        }
        
        return resource_id
    
    def deallocate_resource(self, resource_id: str) -> bool:
        """释放资源"""
        if resource_id in self.allocated_resources:
            del self.allocated_resources[resource_id]
            return True
        return False
    
    def register_cleanup_callback(self, callback):
        """注册清理回调"""
        self.cleanup_callbacks.append(callback)
    
    def cleanup_all(self):
        """清理所有资源"""
        for callback in self.cleanup_callbacks:
            try:
                callback()
            except Exception as e:
                print(f"Cleanup callback error: {e}")
        
        self.allocated_resources.clear()
        self.cleanup_callbacks.clear()
    
    def get_resource_stats(self) -> Dict[str, Any]:
        """获取资源统计"""
        total_size = sum(res['size'] for res in self.allocated_resources.values())
        resource_types = {}
        
        for res in self.allocated_resources.values():
            res_type = res['type']
            if res_type not in resource_types:
                resource_types[res_type] = {'count': 0, 'size': 0}
            resource_types[res_type]['count'] += 1
            resource_types[res_type]['size'] += res['size']
        
        return {
            'total_resources': len(self.allocated_resources),
            'total_size_bytes': total_size,
            'resource_types': resource_types
        }


class MemoryResourceTest:
    """内存和资源管理测试"""
    
    def __init__(self):
        self.memory_tracker = MemoryTracker()
        self.resource_manager = ResourceManager()
        self.session_manager = None
        self.services = []
    
    async def setup(self):
        """设置测试环境"""
        with patch('core.session_manager.zenoh'):
            self.session_manager = SessionManager()
            await self.session_manager.initialize()
    
    async def test_memory_leak_detection(self, object_count: int = 10000):
        """测试内存泄漏检测"""
        self.memory_tracker.start_tracking()
        
        try:
            # 创建大量对象并添加弱引用
            objects = []
            for i in range(object_count):
                # 创建测试对象
                test_obj = {
                    'id': i,
                    'data': list(range(100)),
                    'timestamp': time.time()
                }
                objects.append(test_obj)
                self.memory_tracker.add_weak_reference(test_obj)
                
                # 每1000个对象暂停一下
                if i % 1000 == 0:
                    await asyncio.sleep(0.001)
            
            # 模拟对象使用
            await asyncio.sleep(0.5)
            
            # 删除对象引用
            objects.clear()
            
            # 强制垃圾回收
            gc.collect()
            await asyncio.sleep(0.5)  # 等待垃圾回收完成
            
        finally:
            self.memory_tracker.stop_tracking()
        
        return self.memory_tracker.check_memory_leak()
    
    async def test_resource_cleanup(self, resource_count: int = 1000):
        """测试资源清理"""
        self.memory_tracker.start_tracking()
        
        try:
            # 分配各种类型的资源
            resource_ids = []
            for i in range(resource_count):
                resource_type = f"type_{i % 10}"  # 10种不同类型
                size = 1024 * (1 + i % 10)  # 不同大小
                
                resource_id = self.resource_manager.allocate_resource(resource_type, size)
                resource_ids.append(resource_id)
                
                # 每100个资源暂停一下
                if i % 100 == 0:
                    await asyncio.sleep(0.001)
            
            # 获取分配后的统计
            before_cleanup_stats = self.resource_manager.get_resource_stats()
            
            # 清理一半资源
            for i in range(0, len(resource_ids), 2):
                self.resource_manager.deallocate_resource(resource_ids[i])
            
            partial_cleanup_stats = self.resource_manager.get_resource_stats()
            
            # 清理所有剩余资源
            self.resource_manager.cleanup_all()
            after_cleanup_stats = self.resource_manager.get_resource_stats()
            
            return {
                'before_cleanup': before_cleanup_stats,
                'partial_cleanup': partial_cleanup_stats,
                'after_cleanup': after_cleanup_stats
            }
            
        finally:
            self.memory_tracker.stop_tracking()
    
    async def test_service_resource_management(self):
        """测试服务资源管理"""
        await self.setup()
        self.memory_tracker.start_tracking()
        
        try:
            # 创建多个服务实例
            services = []
            for i in range(10):
                # 模拟创建训练服务
                with patch('services.training_service.TrainingService'):
                    service = Mock()
                    service.service_id = f"training_service_{i}"
                    service.cleanup = Mock()
                    services.append(service)
                    
                    # 添加弱引用跟踪
                    self.memory_tracker.add_weak_reference(service)
            
            # 注册清理回调
            for service in services:
                self.resource_manager.register_cleanup_callback(service.cleanup)
            
            # 模拟服务工作
            await asyncio.sleep(1.0)
            
            # 验证服务清理
            self.resource_manager.cleanup_all()
            
            # 验证所有清理回调被调用
            for service in services:
                service.cleanup.assert_called_once()
            
            # 清除服务引用
            services.clear()
            
            # 强制垃圾回收
            gc.collect()
            await asyncio.sleep(0.5)
            
        finally:
            self.memory_tracker.stop_tracking()
        
        return self.memory_tracker.check_memory_leak()
    
    async def test_long_running_memory_stability(self, duration_seconds: int = 30):
        """测试长期运行内存稳定性"""
        self.memory_tracker.start_tracking()
        
        try:
            start_time = time.time()
            iteration = 0
            
            while time.time() - start_time < duration_seconds:
                # 每次迭代创建和清理资源
                temp_resources = []
                
                # 分配临时资源
                for j in range(100):
                    resource_id = self.resource_manager.allocate_resource(
                        f"temp_type_{j % 5}", 
                        1024 * (1 + j % 4)
                    )
                    temp_resources.append(resource_id)
                
                # 模拟工作负载
                await asyncio.sleep(0.1)
                
                # 清理临时资源
                for resource_id in temp_resources:
                    self.resource_manager.deallocate_resource(resource_id)
                
                # 定期垃圾回收
                if iteration % 10 == 0:
                    gc.collect()
                
                iteration += 1
                await asyncio.sleep(0.01)  # 短暂暂停
            
        finally:
            self.memory_tracker.stop_tracking()
        
        memory_result = self.memory_tracker.check_memory_leak()
        memory_result['iterations'] = iteration
        memory_result['duration'] = duration_seconds
        
        return memory_result


@pytest.mark.asyncio
class TestMemoryResourceManagement:
    """内存和资源管理测试用例"""
    
    async def test_memory_leak_detection(self):
        """测试内存泄漏检测"""
        test_runner = MemoryResourceTest()
        result = await test_runner.test_memory_leak_detection(object_count=5000)
        
        # 内存泄漏检测断言
        assert result['memory_diff_mb'] < 50, "内存增长应小于50MB"
        assert result['objects_not_released'] == 0, "所有对象应被正确释放"
        assert abs(result['memory_growth_rate']) < 1000000, "内存增长率应稳定"
        
        print(f"内存泄漏检测结果: {json.dumps(result, indent=2)}")
    
    async def test_resource_cleanup_effectiveness(self):
        """测试资源清理有效性"""
        test_runner = MemoryResourceTest()
        result = await test_runner.test_resource_cleanup(resource_count=500)
        
        # 资源清理断言
        assert result['before_cleanup']['total_resources'] == 500, "应分配500个资源"
        assert result['partial_cleanup']['total_resources'] == 250, "部分清理后应剩余250个资源"
        assert result['after_cleanup']['total_resources'] == 0, "完全清理后应无剩余资源"
        assert result['after_cleanup']['total_size_bytes'] == 0, "清理后资源大小应为0"
        
        print(f"资源清理测试结果: {json.dumps(result, indent=2)}")
    
    async def test_service_resource_management(self):
        """测试服务资源管理"""
        test_runner = MemoryResourceTest()
        result = await test_runner.test_service_resource_management()
        
        # 服务资源管理断言
        assert result['memory_diff_mb'] < 20, "服务内存增长应小于20MB"
        assert result['objects_not_released'] <= 1, "服务对象应被正确释放"
        
        print(f"服务资源管理测试结果: {json.dumps(result, indent=2)}")
    
    async def test_memory_stability_long_term(self):
        """测试长期内存稳定性"""
        test_runner = MemoryResourceTest()
        result = await test_runner.test_long_running_memory_stability(duration_seconds=15)
        
        # 长期稳定性断言
        assert result['memory_diff_mb'] < 30, "长期运行内存增长应小于30MB"
        assert abs(result['memory_growth_rate']) < 500000, "长期内存增长率应稳定"
        assert result['iterations'] > 10, "应完成足够多的迭代"
        
        print(f"长期内存稳定性测试结果: {json.dumps(result, indent=2)}")
    
    async def test_garbage_collection_efficiency(self):
        """测试垃圾回收效率"""
        test_runner = MemoryResourceTest()
        test_runner.memory_tracker.start_tracking()
        
        try:
            # 创建大量临时对象
            temp_objects = []
            for i in range(10000):
                temp_obj = {
                    'id': i,
                    'data': list(range(50)),
                    'nested': {'values': list(range(10))}
                }
                temp_objects.append(temp_obj)
                test_runner.memory_tracker.add_weak_reference(temp_obj)
                
                if i % 1000 == 0:
                    await asyncio.sleep(0.001)
            
            # 记录创建后内存
            before_gc = psutil.Process().memory_info().rss
            
            # 清除引用
            temp_objects.clear()
            
            # 手动垃圾回收
            gc_start = time.time()
            collected = gc.collect()
            gc_duration = time.time() - gc_start
            
            await asyncio.sleep(0.5)  # 等待完成
            after_gc = psutil.Process().memory_info().rss
            
        finally:
            test_runner.memory_tracker.stop_tracking()
        
        memory_result = test_runner.memory_tracker.check_memory_leak()
        
        gc_result = {
            'gc_duration': gc_duration,
            'objects_collected': collected,
            'memory_freed_mb': (before_gc - after_gc) / (1024 * 1024),
            'objects_not_released': memory_result['objects_not_released']
        }
        
        # 垃圾回收效率断言
        assert gc_result['gc_duration'] < 1.0, "垃圾回收时间应小于1秒"
        assert gc_result['memory_freed_mb'] > 0, "应释放一些内存"
        assert gc_result['objects_not_released'] == 0, "所有对象应被释放"
        
        print(f"垃圾回收效率测试结果: {json.dumps(gc_result, indent=2)}")


if __name__ == "__main__":
    # 运行内存管理测试
    async def main():
        test_suite = TestMemoryResourceManagement()
        
        print("开始内存管理和资源清理测试...")
        
        print("\n1. 内存泄漏检测测试...")
        await test_suite.test_memory_leak_detection()
        
        print("\n2. 资源清理有效性测试...")
        await test_suite.test_resource_cleanup_effectiveness()
        
        print("\n3. 服务资源管理测试...")
        await test_suite.test_service_resource_management()
        
        print("\n4. 长期内存稳定性测试...")
        await test_suite.test_memory_stability_long_term()
        
        print("\n5. 垃圾回收效率测试...")
        await test_suite.test_garbage_collection_efficiency()
        
        print("\n所有内存管理测试完成！")
    
    asyncio.run(main())