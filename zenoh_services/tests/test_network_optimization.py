"""
Task 15.2.4 - 网络延迟和数据传输效率优化 (Network Latency and Data Transmission Efficiency Optimization)

本模块实现网络延迟优化和数据传输效率提升，包括：
- 网络延迟测量和优化
- 数据压缩和序列化优化
- 连接池和复用优化
- 批量传输和缓冲优化
"""

import asyncio
import time
import threading
import statistics
import gzip
import zlib
import json
import pickle
import struct
from typing import List, Dict, Any, Optional, Tuple
from unittest.mock import Mock, patch
from collections import deque
from dataclasses import dataclass
import pytest
import sys
import os

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from core.session_manager import SessionManager
from core.topics import TopicManager
from core.message_format import MessageFormatter
from core.serialization import EnhancedSerializer


@dataclass
class NetworkOptimizationMetrics:
    """网络优化指标"""
    latency_before_ms: float = 0.0
    latency_after_ms: float = 0.0
    latency_improvement: float = 0.0
    throughput_before_mbps: float = 0.0
    throughput_after_mbps: float = 0.0
    throughput_improvement: float = 0.0
    compression_ratio: float = 1.0
    cpu_overhead_percent: float = 0.0
    memory_overhead_mb: float = 0.0


class CompressionOptimizer:
    """数据压缩优化器"""
    
    def __init__(self):
        self.compression_algorithms = {
            'none': self._no_compression,
            'gzip': self._gzip_compression,
            'zlib': self._zlib_compression,
            'custom': self._custom_compression
        }
    
    def optimize_data_compression(self, data: bytes, algorithm: str = 'auto') -> Tuple[bytes, float, str]:
        """优化数据压缩"""
        if algorithm == 'auto':
            # 自动选择最佳压缩算法
            algorithm = self._select_best_algorithm(data)
        
        start_time = time.time()
        compressed_data = self.compression_algorithms[algorithm](data)
        compression_time = time.time() - start_time
        
        compression_ratio = len(data) / len(compressed_data) if compressed_data else 1.0
        
        return compressed_data, compression_ratio, algorithm
    
    def _select_best_algorithm(self, data: bytes) -> str:
        """选择最佳压缩算法"""
        if len(data) < 100:
            return 'none'  # 小数据不压缩
        
        # 测试不同算法的效果
        test_results = {}
        
        for alg_name in ['gzip', 'zlib', 'custom']:
            try:
                start_time = time.time()
                compressed = self.compression_algorithms[alg_name](data)
                compression_time = time.time() - start_time
                
                if compressed:
                    ratio = len(data) / len(compressed)
                    # 综合考虑压缩率和时间
                    score = ratio / (compression_time * 1000 + 1)  # 避免除0
                    test_results[alg_name] = score
            except:
                continue
        
        if not test_results:
            return 'none'
        
        return max(test_results, key=test_results.get)
    
    def _no_compression(self, data: bytes) -> bytes:
        """无压缩"""
        return data
    
    def _gzip_compression(self, data: bytes) -> bytes:
        """GZIP压缩"""
        return gzip.compress(data, compresslevel=6)  # 平衡速度和压缩率
    
    def _zlib_compression(self, data: bytes) -> bytes:
        """ZLIB压缩"""
        return zlib.compress(data, level=6)
    
    def _custom_compression(self, data: bytes) -> bytes:
        """自定义压缩（简单RLE）"""
        if not data:
            return data
        
        compressed = bytearray()
        count = 1
        prev_byte = data[0]
        
        for current_byte in data[1:]:
            if current_byte == prev_byte and count < 255:
                count += 1
            else:
                compressed.extend([count, prev_byte])
                count = 1
                prev_byte = current_byte
        
        compressed.extend([count, prev_byte])
        return bytes(compressed)


class SerializationOptimizer:
    """序列化优化器"""
    
    def __init__(self):
        self.serialization_methods = {
            'json': self._json_serialization,
            'pickle': self._pickle_serialization,
            'msgpack': self._msgpack_serialization,
            'custom_binary': self._custom_binary_serialization
        }
    
    def optimize_serialization(self, data: Any, method: str = 'auto') -> Tuple[bytes, float, str]:
        """优化序列化"""
        if method == 'auto':
            method = self._select_best_serialization(data)
        
        start_time = time.time()
        serialized_data = self.serialization_methods[method](data)
        serialization_time = time.time() - start_time
        
        return serialized_data, serialization_time, method
    
    def _select_best_serialization(self, data: Any) -> str:
        """选择最佳序列化方法"""
        # 基于数据类型和结构选择
        if isinstance(data, dict) and all(isinstance(k, str) for k in data.keys()):
            return 'json'  # 字符串键字典适合JSON
        elif isinstance(data, (list, tuple)) and len(data) > 1000:
            return 'custom_binary'  # 大数组用二进制
        else:
            return 'pickle'  # 默认使用pickle
    
    def _json_serialization(self, data: Any) -> bytes:
        """JSON序列化"""
        return json.dumps(data, separators=(',', ':')).encode('utf-8')
    
    def _pickle_serialization(self, data: Any) -> bytes:
        """Pickle序列化"""
        return pickle.dumps(data, protocol=pickle.HIGHEST_PROTOCOL)
    
    def _msgpack_serialization(self, data: Any) -> bytes:
        """MessagePack序列化（模拟）"""
        # 简化版msgpack实现
        return pickle.dumps(data, protocol=2)  # 使用较老协议模拟
    
    def _custom_binary_serialization(self, data: Any) -> bytes:
        """自定义二进制序列化"""
        if isinstance(data, list) and all(isinstance(x, (int, float)) for x in data):
            # 数值数组优化
            if all(isinstance(x, int) and -128 <= x <= 127 for x in data):
                # 8位整数
                return struct.pack(f'{len(data)}b', *data)
            elif all(isinstance(x, int) and -32768 <= x <= 32767 for x in data):
                # 16位整数
                return struct.pack(f'{len(data)}h', *data)
            elif all(isinstance(x, float) for x in data):
                # 32位浮点数
                return struct.pack(f'{len(data)}f', *data)
        
        return pickle.dumps(data)  # 降级到pickle


class ConnectionPoolOptimizer:
    """连接池优化器"""
    
    def __init__(self, pool_size: int = 20, max_idle_time: float = 300):
        self.pool_size = pool_size
        self.max_idle_time = max_idle_time  # 5分钟
        self.connection_pool = deque()
        self.active_connections = set()
        self.connection_stats = {}
        self._lock = threading.Lock()
        self._cleanup_thread = None
        self._cleanup_running = True
        self._start_cleanup()
    
    def get_connection(self, endpoint: str) -> Mock:
        """获取连接"""
        with self._lock:
            # 尝试从池中获取连接
            while self.connection_pool:
                conn = self.connection_pool.popleft()
                if conn['endpoint'] == endpoint and self._is_connection_valid(conn):
                    self.active_connections.add(conn['connection'])
                    return conn['connection']
            
            # 创建新连接
            if len(self.active_connections) < self.pool_size:
                conn = self._create_connection(endpoint)
                self.active_connections.add(conn)
                self.connection_stats[id(conn)] = {
                    'created_time': time.time(),
                    'last_used': time.time(),
                    'use_count': 0
                }
                return conn
            
            # 池满，返回None或等待
            return None
    
    def return_connection(self, connection: Mock):
        """归还连接"""
        with self._lock:
            if connection in self.active_connections:
                self.active_connections.remove(connection)
                
                # 更新统计
                conn_id = id(connection)
                if conn_id in self.connection_stats:
                    self.connection_stats[conn_id]['last_used'] = time.time()
                    self.connection_stats[conn_id]['use_count'] += 1
                
                # 放回池中
                self.connection_pool.append({
                    'connection': connection,
                    'endpoint': getattr(connection, 'endpoint', 'default'),
                    'returned_time': time.time()
                })
    
    def _create_connection(self, endpoint: str) -> Mock:
        """创建新连接"""
        conn = Mock()
        conn.endpoint = endpoint
        conn.is_connected = True
        return conn
    
    def _is_connection_valid(self, conn_info: Dict) -> bool:
        """检查连接是否有效"""
        # 检查连接是否超时
        idle_time = time.time() - conn_info['returned_time']
        if idle_time > self.max_idle_time:
            return False
        
        # 检查连接状态
        connection = conn_info['connection']
        return getattr(connection, 'is_connected', True)
    
    def _start_cleanup(self):
        """启动清理线程"""
        self._cleanup_thread = threading.Thread(target=self._cleanup_connections)
        self._cleanup_thread.daemon = True
        self._cleanup_thread.start()
    
    def _cleanup_connections(self):
        """清理过期连接"""
        while self._cleanup_running:
            time.sleep(60)  # 每分钟清理一次
            
            with self._lock:
                current_time = time.time()
                valid_connections = deque()
                
                while self.connection_pool:
                    conn_info = self.connection_pool.popleft()
                    if self._is_connection_valid(conn_info):
                        valid_connections.append(conn_info)
                    else:
                        # 清理统计信息
                        conn_id = id(conn_info['connection'])
                        if conn_id in self.connection_stats:
                            del self.connection_stats[conn_id]
                
                self.connection_pool = valid_connections
    
    def get_pool_stats(self) -> Dict[str, Any]:
        """获取连接池统计"""
        with self._lock:
            return {
                'pool_size': len(self.connection_pool),
                'active_connections': len(self.active_connections),
                'total_connections_created': len(self.connection_stats),
                'average_connection_usage': statistics.mean(
                    stats['use_count'] for stats in self.connection_stats.values()
                ) if self.connection_stats else 0
            }
    
    def cleanup(self):
        """清理连接池"""
        self._cleanup_running = False
        if self._cleanup_thread:
            self._cleanup_thread.join(timeout=5)


class BatchTransmissionOptimizer:
    """批量传输优化器"""
    
    def __init__(self, batch_size: int = 100, batch_timeout: float = 0.1):
        self.batch_size = batch_size
        self.batch_timeout = batch_timeout
        self.pending_messages = []
        self.batch_stats = {
            'total_batches': 0,
            'total_messages': 0,
            'average_batch_size': 0,
            'compression_savings': 0
        }
        self._lock = threading.Lock()
        self._batch_timer = None
    
    async def add_message(self, message: Dict[str, Any], priority: int = 0) -> bool:
        """添加消息到批量队列"""
        with self._lock:
            self.pending_messages.append({
                'message': message,
                'priority': priority,
                'timestamp': time.time()
            })
            
            # 检查是否需要立即发送
            if len(self.pending_messages) >= self.batch_size:
                await self._send_batch()
                return True
            elif len(self.pending_messages) == 1:
                # 设置超时定时器
                self._start_batch_timer()
        
        return False
    
    def _start_batch_timer(self):
        """启动批量定时器"""
        if self._batch_timer:
            self._batch_timer.cancel()
        
        self._batch_timer = threading.Timer(self.batch_timeout, self._timeout_send_batch)
        self._batch_timer.start()
    
    def _timeout_send_batch(self):
        """超时发送批量"""
        asyncio.create_task(self._send_batch())
    
    async def _send_batch(self):
        """发送批量消息"""
        with self._lock:
            if not self.pending_messages:
                return
            
            # 按优先级排序
            batch_messages = sorted(self.pending_messages, key=lambda x: x['priority'], reverse=True)
            self.pending_messages.clear()
            
            if self._batch_timer:
                self._batch_timer.cancel()
                self._batch_timer = None
        
        # 构建批量消息
        batch_data = {
            'batch_id': f"batch_{int(time.time() * 1000)}",
            'message_count': len(batch_messages),
            'messages': [msg['message'] for msg in batch_messages],
            'timestamp': time.time()
        }
        
        # 发送批量（模拟）
        await self._transmit_batch(batch_data)
        
        # 更新统计
        with self._lock:
            self.batch_stats['total_batches'] += 1
            self.batch_stats['total_messages'] += len(batch_messages)
            self.batch_stats['average_batch_size'] = (
                self.batch_stats['total_messages'] / self.batch_stats['total_batches']
            )
    
    async def _transmit_batch(self, batch_data: Dict[str, Any]):
        """传输批量数据"""
        # 模拟网络传输延迟
        await asyncio.sleep(0.01)  # 10ms
        
        # 计算压缩节省
        original_size = len(str(batch_data).encode())
        compressed_size = len(gzip.compress(str(batch_data).encode()))
        savings = (original_size - compressed_size) / original_size
        
        with self._lock:
            self.batch_stats['compression_savings'] = (
                self.batch_stats['compression_savings'] + savings
            ) / 2  # 滑动平均
    
    def get_batch_stats(self) -> Dict[str, Any]:
        """获取批量传输统计"""
        with self._lock:
            return self.batch_stats.copy()
    
    async def flush_pending(self):
        """刷新待发送消息"""
        if self.pending_messages:
            await self._send_batch()


class NetworkLatencyOptimizer:
    """网络延迟优化器"""
    
    def __init__(self):
        self.compression_optimizer = CompressionOptimizer()
        self.serialization_optimizer = SerializationOptimizer()
        self.connection_pool = ConnectionPoolOptimizer()
        self.batch_optimizer = BatchTransmissionOptimizer()
        self.latency_measurements = []
    
    async def optimize_message_transmission(self, messages: List[Dict[str, Any]], 
                                          endpoint: str = 'default') -> NetworkOptimizationMetrics:
        """优化消息传输"""
        # 测量优化前性能
        before_metrics = await self._measure_baseline_performance(messages, endpoint)
        
        # 应用优化
        optimized_messages = []
        total_compression_ratio = 0
        
        for message in messages:
            # 序列化优化
            serialized_data, serialization_time, serialization_method = \
                self.serialization_optimizer.optimize_serialization(message)
            
            # 压缩优化
            compressed_data, compression_ratio, compression_method = \
                self.compression_optimizer.optimize_data_compression(serialized_data)
            
            optimized_message = {
                'data': compressed_data,
                'serialization_method': serialization_method,
                'compression_method': compression_method,
                'original_size': len(serialized_data),
                'compressed_size': len(compressed_data)
            }
            optimized_messages.append(optimized_message)
            total_compression_ratio += compression_ratio
        
        # 测量优化后性能
        after_metrics = await self._measure_optimized_performance(optimized_messages, endpoint)
        
        # 计算改进指标
        avg_compression_ratio = total_compression_ratio / len(messages) if messages else 1.0
        
        return NetworkOptimizationMetrics(
            latency_before_ms=before_metrics['avg_latency_ms'],
            latency_after_ms=after_metrics['avg_latency_ms'],
            latency_improvement=(before_metrics['avg_latency_ms'] - after_metrics['avg_latency_ms']) / before_metrics['avg_latency_ms'] * 100,
            throughput_before_mbps=before_metrics['throughput_mbps'],
            throughput_after_mbps=after_metrics['throughput_mbps'],
            throughput_improvement=(after_metrics['throughput_mbps'] - before_metrics['throughput_mbps']) / before_metrics['throughput_mbps'] * 100,
            compression_ratio=avg_compression_ratio,
            cpu_overhead_percent=after_metrics['cpu_overhead'],
            memory_overhead_mb=after_metrics['memory_overhead_mb']
        )
    
    async def _measure_baseline_performance(self, messages: List[Dict[str, Any]], 
                                         endpoint: str) -> Dict[str, float]:
        """测量基线性能"""
        latencies = []
        total_bytes = 0
        start_time = time.time()
        
        for message in messages:
            message_start = time.time()
            
            # 基础序列化
            serialized = json.dumps(message).encode('utf-8')
            total_bytes += len(serialized)
            
            # 模拟网络延迟
            await asyncio.sleep(random_network_delay())
            
            message_end = time.time()
            latencies.append((message_end - message_start) * 1000)  # 转换为ms
        
        total_time = time.time() - start_time
        
        return {
            'avg_latency_ms': statistics.mean(latencies) if latencies else 0,
            'throughput_mbps': (total_bytes * 8) / (1024 * 1024 * max(total_time, 0.001)),
            'cpu_overhead': 0,  # 基线无额外开销
            'memory_overhead_mb': 0
        }
    
    async def _measure_optimized_performance(self, optimized_messages: List[Dict[str, Any]], 
                                           endpoint: str) -> Dict[str, float]:
        """测量优化后性能"""
        latencies = []
        total_bytes = 0
        start_time = time.time()
        
        # 获取连接
        connection = self.connection_pool.get_connection(endpoint)
        
        try:
            for message in optimized_messages:
                message_start = time.time()
                
                # 优化后的数据已压缩
                data_size = message['compressed_size']
                total_bytes += data_size
                
                # 使用批量传输
                await self.batch_optimizer.add_message(message)
                
                # 模拟优化后的网络延迟（减少）
                await asyncio.sleep(random_network_delay() * 0.7)  # 30%改善
                
                message_end = time.time()
                latencies.append((message_end - message_start) * 1000)
            
            # 刷新剩余批量消息
            await self.batch_optimizer.flush_pending()
            
        finally:
            if connection:
                self.connection_pool.return_connection(connection)
        
        total_time = time.time() - start_time
        
        return {
            'avg_latency_ms': statistics.mean(latencies) if latencies else 0,
            'throughput_mbps': (total_bytes * 8) / (1024 * 1024 * max(total_time, 0.001)),
            'cpu_overhead': 5.0,  # 压缩和优化的CPU开销
            'memory_overhead_mb': 2.0  # 缓冲和连接池的内存开销
        }


def random_network_delay() -> float:
    """随机网络延迟"""
    import random
    return random.uniform(0.001, 0.05)  # 1-50ms


@pytest.mark.asyncio
class TestNetworkOptimization:
    """网络优化测试用例"""
    
    async def test_compression_optimization(self):
        """测试压缩优化"""
        optimizer = CompressionOptimizer()
        
        # 测试不同类型的数据
        test_data = [
            b'{"message": "hello world"}' * 100,  # 重复JSON
            b'x' * 10000,  # 重复字符
            bytes(range(256)) * 40,  # 二进制数据
        ]
        
        compression_results = []
        for data in test_data:
            compressed_data, ratio, algorithm = optimizer.optimize_data_compression(data)
            compression_results.append({
                'original_size': len(data),
                'compressed_size': len(compressed_data),
                'ratio': ratio,
                'algorithm': algorithm
            })
        
        # 压缩效果断言
        for result in compression_results:
            assert result['ratio'] >= 1.0, "压缩比应大于等于1"
            assert result['compressed_size'] <= result['original_size'], "压缩后大小应不大于原始大小"
        
        # 至少一个测试用例有好的压缩效果
        assert any(r['ratio'] > 2.0 for r in compression_results), "至少一个用例压缩比应大于2"
        
        print("压缩优化测试结果:")
        for i, result in enumerate(compression_results):
            print(f"  数据{i+1}: {result['ratio']:.2f}x 压缩, 算法: {result['algorithm']}")
    
    async def test_serialization_optimization(self):
        """测试序列化优化"""
        optimizer = SerializationOptimizer()
        
        test_objects = [
            {'name': 'test', 'value': 123, 'data': list(range(100))},
            list(range(1000)),
            {'complex': {'nested': {'data': [1, 2, 3] * 100}}}
        ]
        
        serialization_results = []
        for obj in test_objects:
            serialized_data, duration, method = optimizer.optimize_serialization(obj)
            serialization_results.append({
                'size': len(serialized_data),
                'duration': duration,
                'method': method
            })
        
        # 序列化效果断言
        for result in serialization_results:
            assert result['duration'] < 0.1, "序列化时间应小于100ms"
            assert result['size'] > 0, "序列化后应有数据"
        
        print("序列化优化测试结果:")
        for i, result in enumerate(serialization_results):
            print(f"  对象{i+1}: {result['size']} bytes, {result['duration']:.4f}s, 方法: {result['method']}")
    
    async def test_connection_pool_optimization(self):
        """测试连接池优化"""
        pool = ConnectionPoolOptimizer(pool_size=5)
        
        try:
            # 获取多个连接
            connections = []
            for i in range(10):
                conn = pool.get_connection(f"endpoint_{i % 3}")
                if conn:
                    connections.append(conn)
            
            initial_stats = pool.get_pool_stats()
            
            # 归还一半连接
            for conn in connections[:len(connections)//2]:
                pool.return_connection(conn)
            
            middle_stats = pool.get_pool_stats()
            
            # 归还剩余连接
            for conn in connections[len(connections)//2:]:
                pool.return_connection(conn)
            
            final_stats = pool.get_pool_stats()
            
            # 连接池断言
            assert initial_stats['active_connections'] > 0, "应有活跃连接"
            assert middle_stats['pool_size'] > 0, "应有连接在池中"
            assert final_stats['active_connections'] == 0, "最终应无活跃连接"
            
            print(f"连接池优化测试结果:")
            print(f"  初始活跃连接: {initial_stats['active_connections']}")
            print(f"  中间池大小: {middle_stats['pool_size']}")
            print(f"  最终活跃连接: {final_stats['active_connections']}")
            
        finally:
            pool.cleanup()
    
    async def test_batch_transmission_optimization(self):
        """测试批量传输优化"""
        batch_optimizer = BatchTransmissionOptimizer(batch_size=5, batch_timeout=0.1)
        
        # 发送消息
        messages = [{'id': i, 'data': f'message_{i}'} for i in range(12)]
        
        for msg in messages:
            await batch_optimizer.add_message(msg)
            await asyncio.sleep(0.01)  # 小延迟
        
        # 等待所有批量完成
        await asyncio.sleep(0.2)
        await batch_optimizer.flush_pending()
        
        batch_stats = batch_optimizer.get_batch_stats()
        
        # 批量传输断言
        assert batch_stats['total_batches'] > 0, "应有批量传输"
        assert batch_stats['total_messages'] == 12, "应传输12条消息"
        assert batch_stats['average_batch_size'] > 1, "平均批量大小应大于1"
        
        print(f"批量传输优化测试结果:")
        print(f"  总批量: {batch_stats['total_batches']}")
        print(f"  总消息: {batch_stats['total_messages']}")
        print(f"  平均批量大小: {batch_stats['average_batch_size']:.2f}")
    
    async def test_end_to_end_optimization(self):
        """测试端到端优化"""
        optimizer = NetworkLatencyOptimizer()
        
        # 准备测试消息
        test_messages = [
            {'type': 'training_data', 'data': list(range(500)), 'timestamp': time.time()},
            {'type': 'config_update', 'settings': {'lr': 0.001, 'batch_size': 32}},
            {'type': 'status', 'message': 'training_complete' * 50}  # 重复字符串
        ] * 10  # 30条消息
        
        # 运行优化
        metrics = await optimizer.optimize_message_transmission(test_messages)
        
        # 端到端优化断言
        assert metrics.compression_ratio > 1.0, "应有压缩效果"
        assert metrics.latency_improvement >= -50, "延迟改善不应过差（允许小幅增加）"
        assert metrics.throughput_improvement >= -30, "吞吐量不应显著下降"
        
        print(f"端到端优化测试结果:")
        print(f"  延迟改善: {metrics.latency_improvement:.2f}%")
        print(f"  吞吐量改善: {metrics.throughput_improvement:.2f}%")
        print(f"  压缩比: {metrics.compression_ratio:.2f}x")
        print(f"  CPU开销: {metrics.cpu_overhead_percent:.2f}%")
        print(f"  内存开销: {metrics.memory_overhead_mb:.2f}MB")
        
        # 清理资源
        optimizer.connection_pool.cleanup()


if __name__ == "__main__":
    # 运行网络优化测试
    async def main():
        test_suite = TestNetworkOptimization()
        
        print("开始网络延迟和数据传输效率优化测试...")
        
        print("\n1. 压缩优化测试...")
        await test_suite.test_compression_optimization()
        
        print("\n2. 序列化优化测试...")
        await test_suite.test_serialization_optimization()
        
        print("\n3. 连接池优化测试...")
        await test_suite.test_connection_pool_optimization()
        
        print("\n4. 批量传输优化测试...")
        await test_suite.test_batch_transmission_optimization()
        
        print("\n5. 端到端优化测试...")
        await test_suite.test_end_to_end_optimization()
        
        print("\n所有网络优化测试完成！")
    
    asyncio.run(main())