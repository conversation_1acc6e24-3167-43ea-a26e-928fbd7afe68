import pytest
import asyncio
import time
from zenoh_services.core.data_models import (
    TrainingConfig, TrainingMetrics, TrainingStatus, TrainingState,
    RobotCommand, RobotState, PlayConfig, PlayStatus, PlayState,
    TerrainConfig, PhysicsConfig, RobotConfig, SimulationConfig, SimulationMetrics,
    ModelInfo, DeploymentConfig, DeploymentStatus, DeploymentState,
    SystemStatus, ErrorInfo, CommandMessage, ResponseMessage,
    ConfigurationRequest, ConfigurationResponse
)

class TestEnhancedDataModels:
    """Test enhanced data models with validation and serialization"""
    
    def test_training_config_with_validation(self):
        """Test TrainingConfig validation"""
        # Valid configuration
        valid_config = TrainingConfig(
            task_name="test_task",
            num_envs=1024,
            max_iterations=1000,
            learning_rate=1e-4,
            experiment_name="test_experiment"
        )
        assert valid_config.validate()
        
        # Invalid configuration
        invalid_config = TrainingConfig(
            task_name="",  # Empty task name
            num_envs=0,    # Zero environments
            max_iterations=-1,  # Negative iterations
            learning_rate=0,    # Zero learning rate
            experiment_name=""  # Empty experiment name
        )
        assert not invalid_config.validate()
    
    def test_training_status_with_state(self):
        """Test TrainingStatus with state enum"""
        status = TrainingStatus(
            state=TrainingState.TRAINING,
            current_iteration=500,
            total_iterations=1000,
            start_time=time.time() - 3600,  # Started 1 hour ago
            last_update=time.time(),
            status_message="Training in progress",
            progress_percentage=50.0
        )
        
        assert status.is_active
        assert not status.is_paused
        assert status.elapsed_time > 3500  # About 1 hour
        
        # Test paused state
        paused_status = TrainingStatus(
            state=TrainingState.PAUSED,
            current_iteration=300,
            total_iterations=1000,
            start_time=time.time(),
            last_update=time.time(),
            status_message="Training paused"
        )
        
        assert not paused_status.is_active
        assert paused_status.is_paused
    
    def test_robot_command_validation(self):
        """Test RobotCommand validation"""
        # Valid command
        valid_command = RobotCommand(
            linear_velocity=(1.0, 0.5, 0.0),
            angular_velocity=(0.0, 0.0, 0.5),
            timestamp=time.time(),
            command_id="cmd_001"
        )
        assert valid_command.validate()
        
        # Invalid command (wrong tuple length)
        invalid_command = RobotCommand(
            linear_velocity=(1.0, 0.5),  # Only 2 elements instead of 3
            angular_velocity=(0.0, 0.0, 0.5),
            timestamp=time.time()
        )
        assert not invalid_command.validate()
    
    def test_robot_state_comprehensive(self):
        """Test comprehensive RobotState functionality"""
        state = RobotState(
            joint_positions=[0.1 * i for i in range(12)],
            joint_velocities=[0.2 * i for i in range(12)],
            joint_torques=[0.3 * i for i in range(12)],
            base_position=(1.0, 2.0, 3.0),
            base_orientation=(0.0, 0.0, 0.0, 1.0),  # Unit quaternion
            base_linear_velocity=(1.5, 2.5, 0.0),
            base_angular_velocity=(0.1, 0.2, 0.3),
            contact_forces=[100.0, 95.0, 80.0, 75.0],
            timestamp=time.time(),
            robot_id="robot_test_001"
        )
        
        assert state.validate()
        assert len(state.joint_positions) == 12
        assert len(state.joint_velocities) == 12
        assert len(state.joint_torques) == 12
        assert state.robot_id == "robot_test_001"
        
        # Test serialization round-trip
        json_str = state.to_json()
        deserialized = RobotState.from_json(json_str)
        assert deserialized.validate()
        assert deserialized.robot_id == "robot_test_001"
    
    def test_play_config_and_status(self):
        """Test PlayConfig and PlayStatus"""
        config = PlayConfig(
            task_name="test_play",
            model_path="/path/to/model.pt",
            num_envs=1,
            record_video=True,
            video_path="/path/to/video.mp4",
            max_episode_length=1000
        )
        assert config.validate()
        
        status = PlayStatus(
            state=PlayState.RUNNING,
            current_episode=5,
            current_step=500,
            start_time=time.time() - 60,
            last_update=time.time(),
            status_message="Running simulation",
            is_recording=True
        )
        assert status.is_active
    
    def test_simulation_config_nested_validation(self):
        """Test nested SimulationConfig validation"""
        # Create individual configs
        terrain = TerrainConfig(
            mesh_type="trimesh",
            curriculum=True,
            static_friction=0.8,
            dynamic_friction=0.6,
            num_rows=10,
            num_cols=10
        )
        
        physics = PhysicsConfig(
            dt=0.002,
            substeps=2,
            num_threads=8,
            solver_type=1
        )
        
        robot = RobotConfig(
            control_type="PD",
            action_scale=0.5,
            decimation=10
        )
        
        # Test individual validations
        assert terrain.validate()
        assert physics.validate()
        assert robot.validate()
        
        # Create complete simulation config
        sim_config = SimulationConfig(
            terrain=terrain,
            physics=physics,
            robot=robot
        )
        assert sim_config.validate()
        
        # Test with invalid nested config
        invalid_physics = PhysicsConfig(dt=-0.001)  # Negative dt
        invalid_sim_config = SimulationConfig(
            terrain=terrain,
            physics=invalid_physics,
            robot=robot
        )
        assert not invalid_sim_config.validate()
    
    def test_simulation_metrics(self):
        """Test SimulationMetrics"""
        metrics = SimulationMetrics(
            fps=60.0,
            step_time=0.016,
            physics_time=0.012,
            render_time=0.004,
            memory_usage=1024.5,
            timestamp=time.time(),
            additional_metrics={
                "collision_count": 5,
                "constraint_violations": 2
            }
        )
        
        # Test serialization
        json_str = metrics.to_json()
        deserialized = SimulationMetrics.from_json(json_str)
        
        assert deserialized.fps == 60.0
        assert deserialized.additional_metrics["collision_count"] == 5
    
    def test_model_info_with_validation(self):
        """Test ModelInfo with validation"""
        training_config = TrainingConfig(experiment_name="test_model")
        
        valid_model = ModelInfo(
            model_path="/models/trained_model.pt",
            model_name="test_model_v1",
            training_config=training_config,
            performance_metrics={"mean_reward": 15.5, "success_rate": 0.95},
            creation_time=time.time(),
            file_size=1024000,
            model_format="pt"
        )
        assert valid_model.validate()
        
        # Invalid model (empty path)
        invalid_model = ModelInfo(
            model_path="",  # Empty path
            model_name="test_model",
            training_config=training_config,
            file_size=-1  # Negative file size
        )
        assert not invalid_model.validate()
    
    def test_deployment_config_and_status(self):
        """Test DeploymentConfig and DeploymentStatus"""
        config = DeploymentConfig(
            model_path="/models/model.pt",
            export_format="onnx",
            export_path="/exports/",
            target_platform="gpu",
            optimization_level="fast"
        )
        assert config.validate()
        
        status = DeploymentStatus(
            state=DeploymentState.EXPORTING,
            progress=0.3,
            status_message="Exporting model to ONNX",
            last_update=time.time(),
            start_time=time.time() - 30
        )
        assert status.is_active
    
    def test_system_status_comprehensive(self):
        """Test comprehensive SystemStatus"""
        status = SystemStatus(
            is_healthy=True,
            active_services=["training", "simulation", "deployment"],
            system_load={"cpu": 45.2, "memory": 67.8, "gpu": 23.1},
            error_count=2,
            last_update=time.time(),
            uptime=3600.0  # 1 hour
        )
        
        # Test serialization
        msgpack_bytes = status.to_msgpack()
        deserialized = SystemStatus.from_msgpack(msgpack_bytes)
        
        assert deserialized.is_healthy
        assert len(deserialized.active_services) == 3
        assert deserialized.system_load["cpu"] == 45.2
        assert deserialized.uptime == 3600.0
    
    def test_error_info_with_validation(self):
        """Test ErrorInfo with validation"""
        valid_error = ErrorInfo(
            error_type="ValidationError",
            error_message="Configuration validation failed",
            context={"field": "num_envs", "value": -1},
            timestamp=time.time(),
            severity="error",
            resolved=False
        )
        assert valid_error.validate()
        
        # Invalid error (empty type)
        invalid_error = ErrorInfo(
            error_type="",  # Empty error type
            error_message="Some error",
            severity="invalid_severity"  # Invalid severity
        )
        assert not invalid_error.validate()
    
    def test_command_and_response_messages(self):
        """Test CommandMessage and ResponseMessage"""
        command = CommandMessage(
            command="start_training",
            parameters={"num_envs": 1024, "task": "zqsa01"},
            command_id="cmd_12345"
        )
        assert command.validate()
        
        response = ResponseMessage(
            success=True,
            message="Training started successfully",
            data={"session_id": "session_001"},
            command_id="cmd_12345"
        )
        
        # Test serialization round-trip
        json_data = response.to_json()
        deserialized_response = ResponseMessage.from_json(json_data)
        
        assert deserialized_response.success
        assert deserialized_response.command_id == "cmd_12345"
        assert deserialized_response.data["session_id"] == "session_001"
    
    def test_configuration_request_response(self):
        """Test ConfigurationRequest and ConfigurationResponse"""
        request = ConfigurationRequest(
            config_type="training",
            action="set",
            config_data={"num_envs": 2048, "learning_rate": 1e-4},
            request_id="req_001"
        )
        
        response = ConfigurationResponse(
            success=True,
            config_type="training",
            config_data={"num_envs": 2048, "learning_rate": 1e-4},
            request_id="req_001"
        )
        
        # Test MessagePack serialization
        request_bytes = request.to_msgpack()
        deserialized_request = ConfigurationRequest.from_msgpack(request_bytes)
        
        assert deserialized_request.config_type == "training"
        assert deserialized_request.action == "set"
        assert deserialized_request.request_id == "req_001"
        assert deserialized_request.config_data["num_envs"] == 2048

class TestSerializationMixin:
    """Test SerializationMixin functionality"""
    
    def test_to_dict_conversion(self):
        """Test to_dict conversion with nested objects"""
        terrain = TerrainConfig(mesh_type="trimesh", curriculum=True)
        config = SimulationConfig(terrain=terrain)
        
        dict_result = config.to_dict()
        
        assert isinstance(dict_result, dict)
        assert "terrain" in dict_result
        assert isinstance(dict_result["terrain"], dict)
        assert dict_result["terrain"]["mesh_type"] == "trimesh"
        assert dict_result["terrain"]["curriculum"] is True
    
    def test_enum_serialization(self):
        """Test enum serialization in data models"""
        status = TrainingStatus(
            state=TrainingState.TRAINING,
            current_iteration=100,
            total_iterations=1000,
            start_time=time.time(),
            last_update=time.time(),
            status_message="Training"
        )
        
        dict_result = status.to_dict()
        # Enum should be serialized as its value
        assert dict_result["state"] == "training"
        
        # Test round-trip
        deserialized = TrainingStatus.from_dict(dict_result)
        assert deserialized.state == TrainingState.TRAINING

if __name__ == "__main__":
    pytest.main([__file__, "-v"])