"""
Comprehensive Test Suite for Task 14 Error Handling System
Tests all components of the error handling and recovery system.
"""

import pytest
import asyncio
import logging
import time
import threading
import sys
import os
from unittest.mock import Mock, AsyncMock, patch, MagicMock

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.error_handler import (
    ErrorHandler, ErrorInfo, ErrorContext, ErrorCategory, ErrorSeverity,
    RecoveryStrategy, RecoveryAction, ServiceHealth, HealthStatus,
    get_error_handler, initialize_error_handler
)
from core.fault_detection import (
    FaultDetectionSystem, ServiceMonitor, HealthCheck, ServiceMetrics,
    FailureType, RecoveryAction as FaultRecoveryAction
)
from core.notification_system import (
    NotificationManager, Notification, NotificationType, NotificationPriority,
    UserPrompt
)
from core.service_integration import (
    BaseService, ServiceIntegrationMixin, service_method_error_handler,
    integrate_error_handling
)
from tests.fault_simulation import (
    FaultSimulator, FaultScenario, FaultType as SimFaultType, SimulationMode
)


class TestErrorHandlerIntegration:
    """Comprehensive tests for error handler integration"""
    
    @pytest.fixture
    def error_handler(self):
        """Create error handler for testing"""
        return ErrorHandler(enable_recovery=True)
    
    @pytest.fixture
    def sample_error_context(self):
        """Create sample error context"""
        return ErrorContext(
            service_name="test_service",
            service_state="running",
            timestamp=time.time(),
            thread_id=threading.get_ident(),
            function_name="test_function"
        )
    
    @pytest.mark.asyncio
    async def test_complete_error_handling_flow(self, error_handler, sample_error_context):
        """Test complete error handling flow with recovery"""
        
        # Track callbacks
        error_callback_called = False
        recovery_callback_called = False
        recovery_success = None
        
        def on_error(error_info):
            nonlocal error_callback_called
            error_callback_called = True
        
        def on_recovery(service_name, recovery_action, success):
            nonlocal recovery_callback_called, recovery_success
            recovery_callback_called = True
            recovery_success = success
        
        error_handler.add_error_callback(on_error)
        error_handler.add_recovery_callback(on_recovery)
        
        # Create network error that should trigger recovery
        exception = ConnectionError("Network connection failed")
        
        # Handle error
        error_id = await error_handler.handle_error(
            exception, sample_error_context, ErrorCategory.NETWORK, ErrorSeverity.HIGH
        )
        
        # Wait for recovery to complete
        await asyncio.sleep(3.0)
        
        # Verify error was handled
        assert error_id in error_handler.errors
        assert error_callback_called
        
        # Verify recovery was attempted
        error_info = error_handler.errors[error_id]
        assert error_info.retry_count >= 1
        
        # Verify service health was updated
        health = error_handler.get_service_health("test_service")
        assert health is not None
        assert health.error_count >= 1
    
    @pytest.mark.asyncio
    async def test_manual_intervention_handling(self, error_handler, sample_error_context):
        """Test manual intervention requirement"""
        
        # Create resource exhaustion error that requires manual intervention
        exception = MemoryError("Out of memory")
        
        error_id = await error_handler.handle_error(
            exception, sample_error_context, ErrorCategory.RESOURCE, ErrorSeverity.CRITICAL
        )
        
        # Wait for recovery attempt
        await asyncio.sleep(3.0)
        
        # Verify error was marked for manual intervention
        error_info = error_handler.errors[error_id]
        assert not error_info.resolved
        
        # Verify service health reflects critical status
        health = error_handler.get_service_health("test_service")
        assert health.status == HealthStatus.CRITICAL
    
    @pytest.mark.asyncio
    async def test_recovery_actions(self, error_handler):
        """Test specific recovery actions"""
        
        # Test reconnect action
        context = ErrorContext(
            service_name="network_service",
            service_state="connecting",
            timestamp=time.time(),
            thread_id=threading.get_ident(),
            function_name="connect",
            additional_data={"session_manager": Mock(is_connected=Mock(return_value=False))}
        )
        
        error_info = ErrorInfo(
            error_id="test_reconnect",
            timestamp=time.time(),
            category=ErrorCategory.NETWORK,
            severity=ErrorSeverity.HIGH,
            message="Connection lost",
            exception_type="ConnectionError",
            traceback="Test traceback",
            context=context
        )
        
        strategy = RecoveryStrategy(
            error_pattern=".*connection.*",
            category=ErrorCategory.NETWORK,
            severity=ErrorSeverity.HIGH,
            action=RecoveryAction.RECONNECT,
            retry_delay=0.1
        )
        
        # Test reconnect recovery
        success = await error_handler._handle_reconnect(error_info, strategy)
        assert success  # Should succeed with generic reconnect
    
    @pytest.mark.asyncio
    async def test_fallback_mode_recovery(self, error_handler):
        """Test fallback mode recovery"""
        
        context = ErrorContext(
            service_name="training_service",
            service_state="training",
            timestamp=time.time(),
            thread_id=threading.get_ident(),
            function_name="train_step"
        )
        
        error_info = ErrorInfo(
            error_id="test_fallback",
            timestamp=time.time(),
            category=ErrorCategory.TRAINING,
            severity=ErrorSeverity.HIGH,
            message="Training failure",
            exception_type="RuntimeError",
            traceback="Test traceback",
            context=context
        )
        
        strategy = RecoveryStrategy(
            error_pattern=".*training.*",
            category=ErrorCategory.TRAINING,
            severity=ErrorSeverity.HIGH,
            action=RecoveryAction.FALLBACK_MODE,
            retry_delay=0.1
        )
        
        # Test fallback mode recovery
        success = await error_handler._handle_fallback_mode(error_info, strategy)
        assert success
        
        # Verify service health reflects fallback mode
        health = error_handler.get_service_health("training_service")
        if health:
            assert health.metrics.get("fallback_mode", False)


class TestFaultDetectionIntegration:
    """Test fault detection system integration"""
    
    @pytest.fixture
    def fault_detection_system(self):
        """Create fault detection system for testing"""
        return FaultDetectionSystem()
    
    @pytest.mark.asyncio
    async def test_service_monitoring_lifecycle(self, fault_detection_system):
        """Test complete service monitoring lifecycle"""
        
        # Register service
        service_monitor = fault_detection_system.register_service("lifecycle_test_service")
        
        # Add health checks
        health_check_results = {"result": True}
        
        def dynamic_health_check():
            return health_check_results["result"]
        
        service_monitor.add_health_check(HealthCheck(
            name="dynamic_check",
            check_function=dynamic_health_check,
            interval=0.1,
            failure_threshold=2
        ))
        
        # Add recovery action
        recovery_called = False
        
        def test_recovery():
            nonlocal recovery_called
            recovery_called = True
            health_check_results["result"] = True  # Fix the issue
            return True
        
        service_monitor.add_recovery_action(
            FailureType.UNRESPONSIVE,
            FaultRecoveryAction("test_recovery", test_recovery)
        )
        
        # Start monitoring
        await service_monitor.start_monitoring()
        
        # Let it run healthy for a bit
        await asyncio.sleep(0.3)
        initial_status = service_monitor.last_health_status
        
        # Trigger failure
        health_check_results["result"] = False
        
        # Wait for failure detection and recovery
        await asyncio.sleep(1.0)
        
        # Stop monitoring
        await service_monitor.stop_monitoring()
        
        # Verify recovery was triggered
        assert recovery_called
    
    @pytest.mark.asyncio
    async def test_system_wide_monitoring(self, fault_detection_system):
        """Test system-wide monitoring"""
        
        # Register multiple services
        services = ["service_a", "service_b", "service_c"]
        monitors = []
        
        for service_name in services:
            monitor = fault_detection_system.register_service(service_name)
            monitors.append(monitor)
        
        # Start system monitoring
        await fault_detection_system.start_system_monitoring()
        
        # Let it run for a bit
        await asyncio.sleep(0.5)
        
        # Get system health
        system_health = fault_detection_system.get_system_health()
        
        assert "services" in system_health
        assert "global_checks" in system_health
        assert "system_metrics" in system_health
        
        for service_name in services:
            assert service_name in system_health["services"]
        
        # Stop system monitoring
        await fault_detection_system.stop_system_monitoring()


class TestServiceIntegration:
    """Test service integration with error handling"""
    
    class TestService(BaseService):
        """Test service implementation"""
        
        def __init__(self):
            super().__init__("test_integration_service")
            self.operation_count = 0
            self.should_fail = False
        
        async def initialize(self) -> bool:
            self.add_health_check(
                "operation_health",
                lambda: not self.should_fail,
                interval=0.1
            )
            
            self.add_recovery_action(
                FailureType.HIGH_ERROR_RATE,
                "reset_failure",
                self.reset_failure_flag
            )
            
            await self.start_monitoring()
            self.is_initialized = True
            return True
        
        async def start(self) -> bool:
            self.is_running = True
            return True
        
        async def stop(self) -> bool:
            self.is_running = False
            await self.stop_monitoring()
            return True
        
        async def cleanup(self) -> bool:
            self.operation_count = 0
            self.should_fail = False
            return True
        
        @service_method_error_handler(
            category=ErrorCategory.SERVICE,
            severity=ErrorSeverity.MEDIUM
        )
        async def perform_operation(self):
            """Test operation that may fail"""
            self.operation_count += 1
            
            if self.should_fail and self.operation_count % 3 == 0:
                raise RuntimeError(f"Operation failed at count {self.operation_count}")
            
            return self.operation_count
        
        def reset_failure_flag(self) -> bool:
            """Recovery action to reset failure flag"""
            self.should_fail = False
            return True
    
    @pytest.mark.asyncio
    async def test_service_with_error_handling(self):
        """Test service with integrated error handling"""
        
        service = self.TestService()
        
        # Initialize and start
        assert await service.initialize()
        assert await service.start()
        
        # Normal operations
        for i in range(5):
            result = await service.perform_operation()
            assert result == i + 1
        
        # Trigger failures
        service.should_fail = True
        
        error_count = 0
        for i in range(10):
            try:
                await service.perform_operation()
            except RuntimeError:
                error_count += 1
        
        # Should have caught some errors
        assert error_count > 0
        
        # Stop and cleanup
        assert await service.stop()
        assert await service.cleanup()
    
    def test_service_integration_helper(self):
        """Test service integration helper function"""
        
        class SimpleService:
            def __init__(self):
                self.state = "idle"
                self.value = 0
        
        service = SimpleService()
        
        # Integrate error handling
        integrate_error_handling(
            service,
            "simple_integrated_service",
            health_checks=[
                {
                    "name": "state_check",
                    "check_function": lambda: service.state == "idle",
                    "interval": 1.0
                }
            ]
        )
        
        # Verify integration
        assert hasattr(service, 'service_name')
        assert hasattr(service, 'error_handler')
        assert hasattr(service, 'fault_detection')
        assert hasattr(service, 'service_monitor')
        assert hasattr(service, 'create_error_context')
        
        # Test error context creation
        context = service.create_error_context("test_function")
        assert context.service_name == "simple_integrated_service"
        assert context.function_name == "test_function"


class TestFaultSimulation:
    """Test fault simulation system"""
    
    @pytest.fixture
    def fault_simulator(self):
        """Create fault simulator for testing"""
        return FaultSimulator()
    
    @pytest.mark.asyncio
    async def test_immediate_fault_simulation(self, fault_simulator):
        """Test immediate fault simulation"""
        
        scenario = FaultScenario(
            name="test_immediate_network_fault",
            fault_type=SimFaultType.NETWORK_FAILURE,
            simulation_mode=SimulationMode.IMMEDIATE,
            target_service="test_network_service",
            duration=2.0,
            intensity=0.7
        )
        
        result = await fault_simulator.run_scenario(scenario)
        
        assert result["success"]
        assert result["final_status"] == "completed"
        assert len(result["errors_generated"]) > 0
        assert result["scenario_name"] == "test_immediate_network_fault"
    
    @pytest.mark.asyncio
    async def test_gradual_fault_simulation(self, fault_simulator):
        """Test gradual fault simulation"""
        
        scenario = FaultScenario(
            name="test_gradual_memory_fault",
            fault_type=SimFaultType.MEMORY_LEAK,
            simulation_mode=SimulationMode.GRADUAL,
            target_service="test_memory_service",
            duration=3.0,
            intensity=0.5
        )
        
        result = await fault_simulator.run_scenario(scenario)
        
        assert result["success"]
        assert result["final_status"] == "completed"
        assert len(result["errors_generated"]) > 1  # Multiple steps
    
    @pytest.mark.asyncio 
    async def test_cascading_fault_simulation(self, fault_simulator):
        """Test cascading fault simulation"""
        
        scenario = FaultScenario(
            name="test_cascading_fault",
            fault_type=SimFaultType.SERVICE_CRASH,
            simulation_mode=SimulationMode.CASCADING,
            target_service="primary_service",
            duration=2.0,
            intensity=1.0,
            cascading_services=["secondary_service", "tertiary_service"]
        )
        
        result = await fault_simulator.run_scenario(scenario)
        
        assert result["success"] 
        assert result["final_status"] == "completed"
        assert len(result["errors_generated"]) >= 3  # Primary + cascading
    
    @pytest.mark.asyncio
    async def test_stress_test(self, fault_simulator):
        """Test stress test with multiple scenarios"""
        
        scenarios = [
            FaultScenario(
                name="stress_network_1",
                fault_type=SimFaultType.NETWORK_FAILURE,
                simulation_mode=SimulationMode.IMMEDIATE,
                target_service="stress_service_1",
                duration=1.0
            ),
            FaultScenario(
                name="stress_memory_2",
                fault_type=SimFaultType.MEMORY_LEAK,
                simulation_mode=SimulationMode.IMMEDIATE,
                target_service="stress_service_2", 
                duration=1.0
            ),
            FaultScenario(
                name="stress_crash_3",
                fault_type=SimFaultType.SERVICE_CRASH,
                simulation_mode=SimulationMode.IMMEDIATE,
                target_service="stress_service_3",
                duration=1.0
            )
        ]
        
        # Test sequential execution
        results = await fault_simulator.run_stress_test(scenarios, concurrent=False)
        
        assert len(results) == 3
        for result in results:
            assert result["success"]
    
    @pytest.mark.asyncio
    async def test_simulator_cleanup(self, fault_simulator):
        """Test simulator cleanup functionality"""
        
        # Run a memory leak simulation
        scenario = FaultScenario(
            name="cleanup_test",
            fault_type=SimFaultType.MEMORY_LEAK,
            simulation_mode=SimulationMode.IMMEDIATE,
            target_service="cleanup_service",
            duration=1.0,
            intensity=0.3
        )
        
        await fault_simulator.run_scenario(scenario)
        
        # Verify cleanup
        await fault_simulator.cleanup()
        
        # Memory hogs should be cleared
        assert len(fault_simulator.memory_hogs) == 0
        assert len(fault_simulator.cpu_burners) == 0


class TestEndToEndIntegration:
    """End-to-end integration tests"""
    
    @pytest.mark.asyncio
    async def test_complete_system_integration(self):
        """Test complete system working together"""
        
        # Initialize all components
        error_handler = ErrorHandler(enable_recovery=True)
        fault_detection = FaultDetectionSystem(error_handler)
        fault_simulator = FaultSimulator(error_handler, fault_detection)
        
        # Register a service
        service_monitor = fault_detection.register_service("integration_test_service")
        
        # Add health check that can be controlled
        health_status = {"healthy": True}
        
        service_monitor.add_health_check(HealthCheck(
            name="controlled_health",
            check_function=lambda: health_status["healthy"],
            interval=0.1,
            failure_threshold=1
        ))
        
        # Add recovery action
        recovery_triggered = False
        
        def recovery_action():
            nonlocal recovery_triggered
            recovery_triggered = True
            health_status["healthy"] = True
            return True
        
        service_monitor.add_recovery_action(
            FailureType.UNRESPONSIVE,
            FaultRecoveryAction("integration_recovery", recovery_action)
        )
        
        # Start monitoring
        await service_monitor.start_monitoring()
        
        # Wait for initial health check
        await asyncio.sleep(0.2)
        
        # Simulate fault that affects health
        health_status["healthy"] = False
        
        # Wait for detection and recovery
        await asyncio.sleep(0.5)
        
        # Stop monitoring
        await service_monitor.stop_monitoring()
        
        # Verify recovery was triggered
        assert recovery_triggered
        
        # Clean up
        await fault_simulator.cleanup()
        await fault_detection.shutdown()


if __name__ == "__main__":
    # Configure logging for tests
    logging.basicConfig(level=logging.DEBUG)
    
    # Run tests
    pytest.main([__file__, "-v", "-s"])