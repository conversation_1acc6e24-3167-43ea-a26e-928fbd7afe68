import pytest
from zenoh_services.core import topics

class TestTopics:
    """Test cases for topic definitions"""
    
    def test_system_topics(self):
        """Test system-related topic definitions"""
        assert topics.SYSTEM_STATUS == "legged_gym/system/status"
        assert topics.SYSTEM_HEALTH == "legged_gym/system/health"
        assert topics.SYSTEM_ERROR == "legged_gym/system/error"
    
    def test_training_topics(self):
        """Test training-related topic definitions"""
        assert topics.TRAIN_COMMAND == "legged_gym/train/command"
        assert topics.TRAIN_STATUS == "legged_gym/train/status"
        assert topics.TRAIN_METRICS == "legged_gym/train/metrics"
        assert topics.TRAIN_PROGRESS == "legged_gym/train/progress"
        assert topics.TRAIN_CHECKPOINT == "legged_gym/train/checkpoint"
    
    def test_play_topics(self):
        """Test play/testing-related topic definitions"""
        assert topics.PLAY_COMMAND == "legged_gym/play/command"
        assert topics.PLAY_STATUS == "legged_gym/play/status"
        assert topics.ROBOT_STATE == "legged_gym/robot/state"
        assert topics.ROBOT_CONTROL == "legged_gym/robot/control"
    
    def test_simulation_topics(self):
        """Test simulation-related topic definitions"""
        assert topics.SIM_CONFIG == "legged_gym/simulation/config"
        assert topics.SIM_STATUS == "legged_gym/simulation/status"
        assert topics.SIM_METRICS == "legged_gym/simulation/metrics"
    
    def test_deployment_topics(self):
        """Test deployment-related topic definitions"""
        assert topics.DEPLOY_COMMAND == "legged_gym/deploy/command"
        assert topics.DEPLOY_STATUS == "legged_gym/deploy/status"
        assert topics.MODEL_INFO == "legged_gym/deployment/model/info"
    
    def test_configuration_topics(self):
        """Test configuration-related topic definitions"""
        assert topics.CONFIG_UPDATE == "legged_gym/config/update"
        assert topics.CONFIG_REQUEST == "legged_gym/config/request"
    
    def test_environment_topics(self):
        """Test environment-related topic definitions"""
        assert topics.ENV_STATUS == "legged_gym/environment/status"
        assert topics.ENV_COMMAND == "legged_gym/environment/command"
    
    def test_topic_naming_convention(self):
        """Test that all topics follow the naming convention"""
        topic_vars = [attr for attr in dir(topics) if not attr.startswith('_')]
        
        for topic_var in topic_vars:
            topic_value = getattr(topics, topic_var)
            if isinstance(topic_value, str):
                # All topics should start with "legged_gym/"
                assert topic_value.startswith("legged_gym/"), f"Topic {topic_var} doesn't follow naming convention"
                # Topics should not end with "/"
                assert not topic_value.endswith("/"), f"Topic {topic_var} ends with '/'"
                # Topics should use lowercase and underscores
                parts = topic_value.split("/")
                for part in parts[1:]:  # Skip "legged_gym" part
                    assert part.islower() or "_" in part, f"Topic part '{part}' should be lowercase or contain underscores"

if __name__ == "__main__":
    pytest.main([__file__, "-v"])