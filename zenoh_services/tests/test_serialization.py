import pytest
import json
import msgpack
import time
from zenoh_services.core.serialization import (
    Serializer, SerializationFormat, SerializationError,
    MessageValidator, serialize_to_json, serialize_to_msgpack,
    deserialize_from_json, deserialize_from_msgpack
)
from zenoh_services.core.data_models import (
    TrainingConfig, TrainingMetrics, TrainingState,
    RobotCommand, RobotState, TerrainConfig, SystemStatus
)

class TestSerialization:
    """Test serialization functionality"""
    
    def test_training_config_json_serialization(self):
        """Test TrainingConfig JSON serialization"""
        config = TrainingConfig(
            task_name="test_task",
            num_envs=1024,
            max_iterations=500,
            learning_rate=1e-4,
            experiment_name="test_experiment"
        )
        
        # Test direct serialization
        json_str = config.to_json()
        assert isinstance(json_str, str)
        
        # Test deserialization
        deserialized = TrainingConfig.from_json(json_str)
        assert deserialized.task_name == "test_task"
        assert deserialized.num_envs == 1024
        assert deserialized.max_iterations == 500
        assert deserialized.learning_rate == 1e-4
        assert deserialized.experiment_name == "test_experiment"
    
    def test_training_config_msgpack_serialization(self):
        """Test TrainingConfig MessagePack serialization"""
        config = TrainingConfig(
            task_name="test_task",
            num_envs=1024,
            max_iterations=500
        )
        
        # Test direct serialization
        msgpack_bytes = config.to_msgpack()
        assert isinstance(msgpack_bytes, bytes)
        
        # Test deserialization
        deserialized = TrainingConfig.from_msgpack(msgpack_bytes)
        assert deserialized.task_name == "test_task"
        assert deserialized.num_envs == 1024
        assert deserialized.max_iterations == 500
    
    def test_serializer_with_training_config(self):
        """Test Serializer class with TrainingConfig"""
        config = TrainingConfig(task_name="serializer_test", num_envs=512)
        
        # Test JSON serialization
        json_result = Serializer.serialize(config, SerializationFormat.JSON)
        assert isinstance(json_result, str)
        
        # Test MessagePack serialization
        msgpack_result = Serializer.serialize(config, SerializationFormat.MSGPACK)
        assert isinstance(msgpack_result, bytes)
        
        # Test JSON deserialization
        json_deserialized = Serializer.deserialize(json_result, TrainingConfig, SerializationFormat.JSON)
        assert json_deserialized.task_name == "serializer_test"
        assert json_deserialized.num_envs == 512
        
        # Test MessagePack deserialization
        msgpack_deserialized = Serializer.deserialize(msgpack_result, TrainingConfig, SerializationFormat.MSGPACK)
        assert msgpack_deserialized.task_name == "serializer_test"
        assert msgpack_deserialized.num_envs == 512
    
    def test_robot_command_serialization(self):
        """Test RobotCommand serialization"""
        command = RobotCommand(
            linear_velocity=(1.0, 0.5, 0.0),
            angular_velocity=(0.0, 0.0, 0.5),
            timestamp=time.time(),
            command_id="test_cmd_001"
        )
        
        # Test JSON round-trip (note: tuples become lists in JSON)
        json_str = serialize_to_json(command)
        json_deserialized = deserialize_from_json(json_str, RobotCommand)
        
        assert list(json_deserialized.linear_velocity) == [1.0, 0.5, 0.0]
        assert list(json_deserialized.angular_velocity) == [0.0, 0.0, 0.5]
        assert json_deserialized.command_id == "test_cmd_001"
        
        # Test MessagePack round-trip (should preserve tuples better)
        msgpack_bytes = serialize_to_msgpack(command)
        msgpack_deserialized = deserialize_from_msgpack(msgpack_bytes, RobotCommand)
        
        assert list(msgpack_deserialized.linear_velocity) == [1.0, 0.5, 0.0]
        assert list(msgpack_deserialized.angular_velocity) == [0.0, 0.0, 0.5]
        assert msgpack_deserialized.command_id == "test_cmd_001"
    
    def test_robot_state_with_validation(self):
        """Test RobotState serialization and validation"""
        state = RobotState(
            joint_positions=[0.0] * 12,
            joint_velocities=[0.1] * 12,
            joint_torques=[0.2] * 12,
            base_position=(0.0, 0.0, 1.0),
            base_orientation=(0.0, 0.0, 0.0, 1.0),
            base_linear_velocity=(1.0, 0.0, 0.0),
            base_angular_velocity=(0.0, 0.0, 0.1),
            contact_forces=[100.0, 100.0, 50.0, 50.0],
            timestamp=time.time()
        )
        
        # Test validation
        assert state.validate()
        
        # Test serialization (tuples become lists in serialization)
        msgpack_bytes = state.to_msgpack()
        deserialized = RobotState.from_msgpack(msgpack_bytes)
        
        assert deserialized.validate()
        assert len(deserialized.joint_positions) == 12
        assert len(deserialized.joint_velocities) == 12
        assert len(deserialized.joint_torques) == 12
        assert list(deserialized.base_position) == [0.0, 0.0, 1.0]
    
    def test_nested_serialization(self):
        """Test serialization of objects with nested SerializationMixin objects"""
        terrain = TerrainConfig(
            mesh_type="trimesh",
            curriculum=True,
            static_friction=0.8
        )
        
        # Test nested serialization
        json_str = terrain.to_json()
        deserialized = TerrainConfig.from_json(json_str)
        
        assert deserialized.mesh_type == "trimesh"
        assert deserialized.curriculum is True
        assert deserialized.static_friction == 0.8
        assert deserialized.validate()
    
    def test_format_auto_detection(self):
        """Test automatic format detection"""
        config = TrainingConfig(task_name="auto_detect_test")
        
        # Serialize to both formats
        json_data = serialize_to_json(config)
        msgpack_data = serialize_to_msgpack(config)
        
        # Test auto-detection during deserialization
        json_result = Serializer.deserialize(json_data, TrainingConfig)  # No format specified
        msgpack_result = Serializer.deserialize(msgpack_data, TrainingConfig)  # No format specified
        
        assert json_result.task_name == "auto_detect_test"
        assert msgpack_result.task_name == "auto_detect_test"
    
    def test_serialization_error_handling(self):
        """Test error handling in serialization"""
        # Test invalid target class
        with pytest.raises(SerializationError):
            Serializer.deserialize(b"invalid data", TrainingConfig)
        
        # Test invalid data
        with pytest.raises(SerializationError):
            deserialize_from_json("invalid json", TrainingConfig)

class TestMessageValidator:
    """Test message validation functionality"""
    
    def test_json_validation(self):
        """Test JSON validation"""
        valid_json = '{"key": "value", "number": 123}'
        invalid_json = '{"key": "value", "number":}'
        
        assert MessageValidator.validate_json(valid_json)
        assert not MessageValidator.validate_json(invalid_json)
    
    def test_msgpack_validation(self):
        """Test MessagePack validation"""
        valid_data = msgpack.packb({"key": "value", "number": 123})
        invalid_data = b"not msgpack data"
        
        assert MessageValidator.validate_msgpack(valid_data)
        assert not MessageValidator.validate_msgpack(invalid_data)
    
    def test_data_model_validation(self):
        """Test data model validation"""
        # Valid config
        valid_config = TrainingConfig(
            task_name="valid_task",
            num_envs=1024,
            max_iterations=1000,
            learning_rate=1e-4
        )
        assert MessageValidator.validate_data_model(valid_config)
        
        # Invalid config (negative values)
        invalid_config = TrainingConfig(
            task_name="",  # Empty task name
            num_envs=-1,   # Negative envs
            max_iterations=0,  # Zero iterations
            learning_rate=-1   # Negative learning rate
        )
        assert not MessageValidator.validate_data_model(invalid_config)
    
    def test_round_trip_validation(self):
        """Test serialization round-trip validation"""
        config = TrainingConfig(task_name="round_trip_test", num_envs=512)
        
        # Test JSON round-trip
        assert MessageValidator.validate_round_trip(config, SerializationFormat.JSON)
        
        # Test MessagePack round-trip
        assert MessageValidator.validate_round_trip(config, SerializationFormat.MSGPACK)

class TestPerformance:
    """Test serialization performance"""
    
    def test_large_data_serialization(self):
        """Test serialization of large data structures"""
        # Create large robot state
        large_state = RobotState(
            joint_positions=[0.1 * i for i in range(12)],
            joint_velocities=[0.2 * i for i in range(12)],
            joint_torques=[0.3 * i for i in range(12)],
            base_position=(1.0, 2.0, 3.0),
            base_orientation=(0.0, 0.0, 0.0, 1.0),
            base_linear_velocity=(1.5, 2.5, 3.5),
            base_angular_velocity=(0.1, 0.2, 0.3),
            contact_forces=[50.0 + i for i in range(100)],  # Large contact force array
            timestamp=time.time()
        )
        
        # Test MessagePack (should be faster for large data)
        start_time = time.time()
        msgpack_data = large_state.to_msgpack()
        msgpack_serialize_time = time.time() - start_time
        
        start_time = time.time()
        msgpack_deserialized = RobotState.from_msgpack(msgpack_data)
        msgpack_deserialize_time = time.time() - start_time
        
        # Test JSON
        start_time = time.time()
        json_data = large_state.to_json()
        json_serialize_time = time.time() - start_time
        
        start_time = time.time()
        json_deserialized = RobotState.from_json(json_data)
        json_deserialize_time = time.time() - start_time
        
        # Verify correctness
        assert msgpack_deserialized.validate()
        assert json_deserialized.validate()
        assert len(msgpack_deserialized.contact_forces) == 100
        assert len(json_deserialized.contact_forces) == 100
        
        # MessagePack should typically be more compact for large data
        # Note: This may not always be true for small datasets, so we just verify both work
        assert len(msgpack_data) > 0
        assert len(json_data.encode('utf-8')) > 0
        
        print(f"MessagePack serialize: {msgpack_serialize_time:.4f}s")
        print(f"MessagePack deserialize: {msgpack_deserialize_time:.4f}s")
        print(f"JSON serialize: {json_serialize_time:.4f}s")
        print(f"JSON deserialize: {json_deserialize_time:.4f}s")
        print(f"MessagePack size: {len(msgpack_data)} bytes")
        print(f"JSON size: {len(json_data.encode('utf-8'))} bytes")

if __name__ == "__main__":
    pytest.main([__file__, "-v"])