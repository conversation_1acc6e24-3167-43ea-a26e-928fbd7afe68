import pytest
import asyncio
import time
from unittest.mock import <PERSON><PERSON>, As<PERSON><PERSON><PERSON>, patch
from zenoh_services.core import ZenohSessionManager, ZenohConfig, ZenohMessage

class TestZenohSessionManager:
    """Test cases for ZenohSessionManager"""
    
    @pytest.fixture
    def session_manager(self):
        """Create a test session manager"""
        config = ZenohConfig(router_endpoint="tcp/127.0.0.1:7447")
        manager = ZenohSessionManager(config)
        return manager
    
    @pytest.fixture
    def mock_zenoh_session(self):
        """Create a mock Zenoh session"""
        session = Mock()
        session.declare_publisher = Mock()
        session.declare_subscriber = Mock()
        session.close = Mock()
        return session
    
    @pytest.mark.asyncio
    async def test_initialization(self, session_manager):
        """Test session initialization"""
        with patch('zenoh.open') as mock_open:
            mock_session = Mock()
            mock_open.return_value = mock_session
            
            await session_manager.initialize()
            
            assert session_manager.is_initialized
            assert session_manager.session == mock_session
            mock_open.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_create_publisher(self, session_manager, mock_zenoh_session):
        """Test publisher creation"""
        session_manager.session = mock_zenoh_session
        session_manager.is_initialized = True
        
        mock_publisher = Mock()
        mock_zenoh_session.declare_publisher.return_value = mock_publisher
        
        topic = "test/topic"
        publisher = await session_manager.create_publisher(topic)
        
        assert topic in session_manager.publishers
        assert session_manager.publishers[topic] == mock_publisher
        mock_zenoh_session.declare_publisher.assert_called_once_with(topic)
    
    @pytest.mark.asyncio
    async def test_create_subscriber(self, session_manager, mock_zenoh_session):
        """Test subscriber creation"""
        session_manager.session = mock_zenoh_session
        session_manager.is_initialized = True
        
        mock_subscriber = Mock()
        mock_zenoh_session.declare_subscriber.return_value = mock_subscriber
        
        topic = "test/topic"
        callback = Mock()
        
        subscriber = await session_manager.create_subscriber(topic, callback)
        
        assert topic in session_manager.subscribers
        assert session_manager.subscribers[topic] == mock_subscriber
        mock_zenoh_session.declare_subscriber.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_publish_message(self, session_manager, mock_zenoh_session):
        """Test message publishing"""
        session_manager.session = mock_zenoh_session
        session_manager.is_initialized = True
        
        mock_publisher = Mock()
        mock_zenoh_session.declare_publisher.return_value = mock_publisher
        
        topic = "test/topic"
        data = {"key": "value"}
        
        await session_manager.publish(topic, data, "test_type")
        
        assert topic in session_manager.publishers
        mock_publisher.put.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_publish_without_initialization(self, session_manager):
        """Test publishing without initialization should raise error"""
        with pytest.raises(RuntimeError, match="Session not initialized"):
            await session_manager.publish("test/topic", {})
    
    @pytest.mark.asyncio
    async def test_cleanup(self, session_manager, mock_zenoh_session):
        """Test session cleanup"""
        session_manager.session = mock_zenoh_session
        session_manager.is_initialized = True
        
        mock_publisher = Mock()
        mock_subscriber = Mock()
        session_manager.publishers["topic1"] = mock_publisher
        session_manager.subscribers["topic2"] = mock_subscriber
        
        await session_manager.cleanup()
        
        assert not session_manager.is_initialized
        assert len(session_manager.publishers) == 0
        assert len(session_manager.subscribers) == 0
        mock_publisher.undeclare.assert_called_once()
        mock_subscriber.undeclare.assert_called_once()
        mock_zenoh_session.close.assert_called_once()

class TestZenohMessage:
    """Test cases for ZenohMessage"""
    
    def test_message_creation(self):
        """Test ZenohMessage creation"""
        timestamp = time.time()
        message = ZenohMessage(
            timestamp=timestamp,
            message_id="test_id",
            message_type="test_type",
            payload={"data": "test"}
        )
        
        assert message.timestamp == timestamp
        assert message.message_id == "test_id"
        assert message.message_type == "test_type"
        assert message.payload == {"data": "test"}
        assert message.metadata == {}
    
    def test_message_serialization(self):
        """Test message serialization and deserialization"""
        original_message = ZenohMessage(
            timestamp=time.time(),
            message_id="test_id",
            message_type="test_type",
            payload={"data": "test"},
            metadata={"source": "test"}
        )
        
        # Serialize
        serialized = original_message.to_msgpack()
        assert isinstance(serialized, bytes)
        
        # Deserialize
        deserialized = ZenohMessage.from_msgpack(serialized)
        
        assert deserialized.timestamp == original_message.timestamp
        assert deserialized.message_id == original_message.message_id
        assert deserialized.message_type == original_message.message_type
        assert deserialized.payload == original_message.payload
        assert deserialized.metadata == original_message.metadata

class TestZenohConfig:
    """Test cases for ZenohConfig"""
    
    def test_default_config(self):
        """Test default configuration values"""
        config = ZenohConfig()
        
        assert config.router_endpoint == "tcp/127.0.0.1:7447"
        assert config.session_timeout == 30
        assert config.qos_reliability == "reliable"
        assert config.qos_durability == "volatile"
    
    def test_custom_config(self):
        """Test custom configuration values"""
        config = ZenohConfig(
            router_endpoint="tcp/192.168.1.100:7447",
            session_timeout=60,
            qos_reliability="best_effort",
            qos_durability="persistent"
        )
        
        assert config.router_endpoint == "tcp/192.168.1.100:7447"
        assert config.session_timeout == 60
        assert config.qos_reliability == "best_effort"
        assert config.qos_durability == "persistent"

class TestIntegration:
    """Integration tests for Zenoh components"""
    
    @pytest.mark.asyncio
    async def test_publisher_subscriber_integration(self):
        """Test publisher-subscriber integration with mock session"""
        config = ZenohConfig()
        manager = ZenohSessionManager(config)
        
        with patch('zenoh.open') as mock_open:
            mock_session = Mock()
            mock_publisher = Mock()
            mock_subscriber = Mock()
            
            mock_session.declare_publisher.return_value = mock_publisher
            mock_session.declare_subscriber.return_value = mock_subscriber
            mock_open.return_value = mock_session
            
            # Initialize session
            await manager.initialize()
            
            # Create publisher and subscriber
            topic = "test/integration"
            callback = Mock()
            
            await manager.create_publisher(topic)
            await manager.create_subscriber(topic, callback)
            
            # Publish a message
            test_data = {"test": "integration"}
            await manager.publish(topic, test_data, "integration_test")
            
            # Verify publisher was called
            mock_publisher.put.assert_called_once()
            
            # Cleanup
            await manager.cleanup()

if __name__ == "__main__":
    pytest.main([__file__, "-v"])