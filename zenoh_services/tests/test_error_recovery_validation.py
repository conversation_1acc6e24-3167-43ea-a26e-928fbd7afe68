"""
Task 15.1.4: Error Recovery and Fault Transfer Validation Tests
Comprehensive validation of the error handling and recovery system from Task 14
"""

import asyncio
import pytest
import logging
import time
import os
import threading
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

# Import error handling components from Task 14
from zenoh_services.core.error_handler import (
    get_error_handler, initialize_error_handler, ErrorContext, 
    ErrorCategory, ErrorSeverity, RecoveryAction, RecoveryStrategy
)
from zenoh_services.core.fault_detection import (
    get_fault_detection_system, HealthCheck, FailureType
)
from zenoh_services.core.notification_system import get_notification_system
from zenoh_services.core.service_integration import (
    BaseService, ServiceIntegrationMixin, integrate_error_handling
)

# Import fault simulation from Task 14
from zenoh_services.tests.fault_simulation import (
    FaultSimulator, FaultScenario, FaultType, SimulationMode
)

# Import services for integration testing
from zenoh_services.core.enhanced_session_manager import EnhancedZenohSessionManager
from zenoh_services.services.training_service import TrainingService
from zenoh_services.services.play_service import PlayService
from zenoh_services.services.simulation_service import SimulationService

# Set up logging for tests
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MockFailureService:
    """Mock service that can simulate various failure modes"""
    
    def __init__(self, name: str):
        self.name = name
        self.is_initialized = False
        self.is_running = False
        self.failure_mode = None
        self.call_count = 0
        self.recovery_attempts = []
    
    async def initialize(self) -> bool:
        self.call_count += 1
        if self.failure_mode == "init_failure":
            raise Exception(f"{self.name} initialization failed")
        self.is_initialized = True
        return True
    
    async def start(self) -> bool:
        if not self.is_initialized:
            raise Exception(f"{self.name} not initialized")
        if self.failure_mode == "start_failure":
            raise Exception(f"{self.name} start failed")
        self.is_running = True
        return True
    
    async def stop(self) -> bool:
        if self.failure_mode == "stop_failure":
            raise Exception(f"{self.name} stop failed")
        self.is_running = False
        return True
    
    async def cleanup(self) -> bool:
        if self.failure_mode == "cleanup_failure":
            raise Exception(f"{self.name} cleanup failed")
        self.is_initialized = False
        return True
    
    async def process_data(self, data: Any) -> Any:
        self.call_count += 1
        if self.failure_mode == "processing_failure":
            raise Exception(f"{self.name} processing failed")
        return f"processed_{data}"
    
    def set_failure_mode(self, mode: str):
        """Set failure mode for testing"""
        self.failure_mode = mode
    
    def clear_failure_mode(self):
        """Clear failure mode"""
        self.failure_mode = None
    
    def is_healthy(self) -> bool:
        """Health check method"""
        return self.is_running and self.failure_mode is None
    
    def recover_from_failure(self) -> bool:
        """Recovery action"""
        try:
            self.recovery_attempts.append(time.time())
            self.clear_failure_mode()
            self.is_running = True
            return True
        except:
            return False


@pytest.fixture
async def error_handler_system():
    """Set up error handling system for testing"""
    # Initialize error handler
    error_handler = initialize_error_handler(
        log_file="test_error_handler.log",
        enable_recovery=True
    )
    
    # Initialize fault detection
    fault_detection = get_fault_detection_system()
    
    # Initialize notification system
    notification_system = get_notification_system()
    
    yield {
        "error_handler": error_handler,
        "fault_detection": fault_detection,
        "notification_system": notification_system
    }
    
    # Cleanup
    await fault_detection.stop_system_monitoring()
    if os.path.exists("test_error_handler.log"):
        os.remove("test_error_handler.log")


@pytest.fixture
def mock_failure_services():
    """Create mock services that can simulate failures"""
    services = {
        "service_a": MockFailureService("service_a"),
        "service_b": MockFailureService("service_b"),
        "service_c": MockFailureService("service_c")
    }
    return services


class TestErrorRecoveryValidation:
    """Test error recovery mechanisms"""
    
    @pytest.mark.asyncio
    async def test_basic_error_recovery(self, error_handler_system, mock_failure_services):
        """Test basic error recovery functionality"""
        
        logger.info("Testing basic error recovery...")
        
        error_handler = error_handler_system["error_handler"]
        service = mock_failure_services["service_a"]
        
        # Register recovery strategy
        recovery_strategy = RecoveryStrategy(
            error_pattern=r".*processing failed.*",
            category=ErrorCategory.SERVICE,
            severity=ErrorSeverity.MEDIUM,
            action=RecoveryAction.RETRY,
            max_retry_attempts=3,
            retry_delay=0.1
        )
        error_handler.register_strategy(recovery_strategy)
        
        # Set service to fail initially
        service.set_failure_mode("processing_failure")
        
        # Attempt operation that will fail
        try:
            await service.process_data("test_data")
            assert False, "Expected service to fail"
        except Exception as e:
            # Handle error through error handler
            context = ErrorContext(
                service_name="service_a",
                service_state="processing",
                timestamp=time.time(),
                thread_id=threading.get_ident(),
                function_name="process_data"
            )
            
            # This should trigger recovery
            recovery_result = await error_handler.handle_error(
                e, context, ErrorCategory.SERVICE, ErrorSeverity.MEDIUM
            )
            
            assert recovery_result is not None
            assert recovery_result.recovery_attempted is True
        
        # Clear failure mode and try again - should succeed
        service.clear_failure_mode()
        result = await service.process_data("test_data")
        assert result == "processed_test_data"
        
        # Check error statistics
        stats = error_handler.get_error_statistics()
        assert stats["total_errors"] >= 1
        assert stats["recovery_attempts"] >= 1
        
        logger.info("Basic error recovery test passed!")
    
    @pytest.mark.asyncio
    async def test_service_restart_recovery(self, error_handler_system, mock_failure_services):
        """Test service restart recovery"""
        
        logger.info("Testing service restart recovery...")
        
        error_handler = error_handler_system["error_handler"]
        service = mock_failure_services["service_b"]
        
        # Initialize service
        await service.initialize()
        await service.start()
        assert service.is_running is True
        
        # Register restart recovery strategy
        recovery_strategy = RecoveryStrategy(
            error_pattern=r".*start failed.*",
            category=ErrorCategory.SERVICE,
            severity=ErrorSeverity.HIGH,
            action=RecoveryAction.RESTART_SERVICE,
            max_retry_attempts=2,
            retry_delay=0.1
        )
        error_handler.register_strategy(recovery_strategy)
        
        # Simulate service failure
        service.set_failure_mode("start_failure")
        service.is_running = False
        
        # Trigger restart through error handler
        try:
            await service.start()
            assert False, "Expected service start to fail"
        except Exception as e:
            context = ErrorContext(
                service_name="service_b",
                service_state="stopped",
                timestamp=time.time(),
                thread_id=threading.get_ident(),
                function_name="start"
            )
            
            # Handle error with restart recovery
            recovery_result = await error_handler.handle_error(
                e, context, ErrorCategory.SERVICE, ErrorSeverity.HIGH
            )
            
            assert recovery_result is not None
            assert recovery_result.recovery_attempted is True
        
        logger.info("Service restart recovery test passed!")
    
    @pytest.mark.asyncio
    async def test_health_check_failure_detection(self, error_handler_system, mock_failure_services):
        """Test health check-based failure detection"""
        
        logger.info("Testing health check failure detection...")
        
        fault_detection = error_handler_system["fault_detection"]
        service = mock_failure_services["service_c"]
        
        # Initialize service
        await service.initialize()
        await service.start()
        
        # Register service with health check
        monitor = fault_detection.register_service("service_c")
        
        health_check = HealthCheck(
            name="service_c_health",
            check_function=service.is_healthy,
            interval=0.1,
            timeout=0.05,
            failure_threshold=2
        )
        monitor.add_health_check(health_check)
        
        # Add recovery action
        monitor.add_recovery_action(
            FailureType.UNRESPONSIVE,
            "recover_service",
            service.recover_from_failure
        )
        
        # Start monitoring
        await monitor.start_monitoring()
        
        # Let it run healthy for a bit
        await asyncio.sleep(0.3)
        
        # Verify service is healthy
        health_status = monitor.get_health_status()
        assert health_status["overall_status"] == "healthy"
        
        # Simulate service failure
        service.set_failure_mode("processing_failure")
        
        # Wait for health checks to detect failure
        await asyncio.sleep(0.5)
        
        # Verify failure was detected and recovery attempted
        health_status = monitor.get_health_status()
        assert len(service.recovery_attempts) > 0
        
        await monitor.stop_monitoring()
        
        logger.info("Health check failure detection test passed!")
    
    @pytest.mark.asyncio
    async def test_cascading_failure_handling(self, error_handler_system, mock_failure_services):
        """Test handling of cascading failures"""
        
        logger.info("Testing cascading failure handling...")
        
        error_handler = error_handler_system["error_handler"]
        fault_detection = error_handler_system["fault_detection"]
        
        services = mock_failure_services
        
        # Initialize all services
        for service in services.values():
            await service.initialize()
            await service.start()
        
        # Register services for monitoring
        monitors = {}
        for name, service in services.items():
            monitor = fault_detection.register_service(name)
            monitors[name] = monitor
            
            # Add health checks
            health_check = HealthCheck(
                name=f"{name}_health",
                check_function=service.is_healthy,
                interval=0.1,
                timeout=0.05,
                failure_threshold=1
            )
            monitor.add_health_check(health_check)
            
            # Add recovery actions
            monitor.add_recovery_action(
                FailureType.UNRESPONSIVE,
                f"recover_{name}",
                service.recover_from_failure
            )
            
            await monitor.start_monitoring()
        
        # Simulate cascading failure
        # Service A fails, which should be detected and recovered
        services["service_a"].set_failure_mode("processing_failure")
        
        # Wait for detection and recovery
        await asyncio.sleep(0.3)
        
        # Service B fails due to Service A dependency (simulated)
        services["service_b"].set_failure_mode("processing_failure")
        
        # Wait for cascading failure handling
        await asyncio.sleep(0.5)
        
        # Verify recovery attempts were made
        for name, service in services.items():
            if service.failure_mode:
                assert len(service.recovery_attempts) > 0, f"No recovery attempts for {name}"
        
        # Cleanup
        for monitor in monitors.values():
            await monitor.stop_monitoring()
        
        logger.info("Cascading failure handling test passed!")
    
    @pytest.mark.asyncio
    async def test_fault_tolerance_under_load(self, error_handler_system):
        """Test fault tolerance under high load conditions"""
        
        logger.info("Testing fault tolerance under load...")
        
        error_handler = error_handler_system["error_handler"]
        
        # Create multiple failure scenarios
        failure_count = 0
        recovery_count = 0
        
        async def simulate_high_load_operation(operation_id: int):
            nonlocal failure_count, recovery_count
            
            try:
                # Simulate random failures
                if operation_id % 3 == 0:  # Fail every 3rd operation
                    failure_count += 1
                    raise Exception(f"Operation {operation_id} failed")
                
                # Simulate successful operation
                await asyncio.sleep(0.01)
                return f"result_{operation_id}"
                
            except Exception as e:
                # Handle through error handler
                context = ErrorContext(
                    service_name="load_test_service",
                    service_state="processing",
                    timestamp=time.time(),
                    thread_id=threading.get_ident(),
                    function_name="high_load_operation"
                )
                
                recovery_result = await error_handler.handle_error(
                    e, context, ErrorCategory.SERVICE, ErrorSeverity.MEDIUM
                )
                
                if recovery_result and recovery_result.recovery_attempted:
                    recovery_count += 1
                
                # Return error result
                return f"error_{operation_id}"
        
        # Run many concurrent operations
        tasks = []
        for i in range(50):
            task = asyncio.create_task(simulate_high_load_operation(i))
            tasks.append(task)
        
        # Wait for all operations
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Verify system handled load appropriately
        assert len(results) == 50
        assert failure_count > 0, "Expected some failures in load test"
        assert recovery_count > 0, "Expected recovery attempts under load"
        
        # Check error handler statistics
        stats = error_handler.get_error_statistics()
        assert stats["total_errors"] >= failure_count
        
        logger.info(f"Load test: {failure_count} failures, {recovery_count} recoveries")
        logger.info("Fault tolerance under load test passed!")
    
    @pytest.mark.asyncio
    async def test_recovery_strategy_effectiveness(self, error_handler_system):
        """Test effectiveness of different recovery strategies"""
        
        logger.info("Testing recovery strategy effectiveness...")
        
        error_handler = error_handler_system["error_handler"]
        
        # Test different recovery strategies
        strategies = [
            (RecoveryAction.RETRY, "retry_test"),
            (RecoveryAction.RECONNECT, "reconnect_test"),
            (RecoveryAction.RESTART_SERVICE, "restart_test"),
            (RecoveryAction.RESET_STATE, "reset_test")
        ]
        
        strategy_results = {}
        
        for action, test_name in strategies:
            # Register strategy
            strategy = RecoveryStrategy(
                error_pattern=f".*{test_name}.*",
                category=ErrorCategory.SERVICE,
                severity=ErrorSeverity.MEDIUM,
                action=action,
                max_retry_attempts=2,
                retry_delay=0.05
            )
            error_handler.register_strategy(strategy)
            
            # Simulate error for this strategy
            try:
                raise Exception(f"Test error for {test_name}")
            except Exception as e:
                context = ErrorContext(
                    service_name=test_name,
                    service_state="testing",
                    timestamp=time.time(),
                    thread_id=threading.get_ident(),
                    function_name="test_recovery"
                )
                
                recovery_result = await error_handler.handle_error(
                    e, context, ErrorCategory.SERVICE, ErrorSeverity.MEDIUM
                )
                
                strategy_results[action] = {
                    "attempted": recovery_result.recovery_attempted if recovery_result else False,
                    "action_taken": recovery_result.action_taken if recovery_result else None,
                    "success": recovery_result.success if recovery_result else False
                }
        
        # Verify all strategies were attempted
        for action, test_name in strategies:
            assert action in strategy_results, f"Strategy {action} was not tested"
            result = strategy_results[action]
            assert result["attempted"] is True, f"Recovery not attempted for {action}"
        
        logger.info("Recovery strategy effectiveness test passed!")
    
    @pytest.mark.asyncio 
    async def test_error_notification_integration(self, error_handler_system):
        """Test integration with notification system"""
        
        logger.info("Testing error notification integration...")
        
        error_handler = error_handler_system["error_handler"]
        notification_system = error_handler_system["notification_system"]
        
        # Track notifications
        notifications_received = []
        
        # Mock notification delivery
        original_send_notification = notification_system.send_notification
        async def mock_send_notification(notification):
            notifications_received.append(notification)
            return await original_send_notification(notification)
        
        notification_system.send_notification = mock_send_notification
        
        # Generate errors that should trigger notifications
        error_scenarios = [
            (ErrorSeverity.LOW, "Low priority error"),
            (ErrorSeverity.MEDIUM, "Medium priority error"),
            (ErrorSeverity.HIGH, "High priority error"),
            (ErrorSeverity.CRITICAL, "Critical priority error")
        ]
        
        for severity, message in error_scenarios:
            try:
                raise Exception(message)
            except Exception as e:
                context = ErrorContext(
                    service_name="notification_test",
                    service_state="testing",
                    timestamp=time.time(),
                    thread_id=threading.get_ident(),
                    function_name="test_notifications"
                )
                
                await error_handler.handle_error(
                    e, context, ErrorCategory.SERVICE, severity
                )
        
        # Verify notifications were sent
        assert len(notifications_received) >= len(error_scenarios)
        
        # Verify notification content
        for notification in notifications_received:
            assert "type" in notification.data
            assert "severity" in notification.data
            assert "message" in notification.data
        
        logger.info("Error notification integration test passed!")


class TestFaultTransferValidation:
    """Test fault transfer and isolation mechanisms"""
    
    @pytest.mark.asyncio
    async def test_fault_isolation_boundaries(self, error_handler_system, mock_failure_services):
        """Test that faults are properly isolated between services"""
        
        logger.info("Testing fault isolation boundaries...")
        
        services = mock_failure_services
        fault_detection = error_handler_system["fault_detection"]
        
        # Initialize all services
        for service in services.values():
            await service.initialize()
            await service.start()
        
        # Register services for monitoring
        for name, service in services.items():
            monitor = fault_detection.register_service(name)
            
            health_check = HealthCheck(
                name=f"{name}_isolation_health",
                check_function=service.is_healthy,
                interval=0.1,
                timeout=0.05,
                failure_threshold=1
            )
            monitor.add_health_check(health_check)
            await monitor.start_monitoring()
        
        # Cause failure in one service
        services["service_a"].set_failure_mode("processing_failure")
        
        # Wait for failure detection
        await asyncio.sleep(0.3)
        
        # Verify other services remain healthy
        system_health = fault_detection.get_system_health()
        
        service_a_health = system_health["services"]["service_a"]
        service_b_health = system_health["services"]["service_b"]
        service_c_health = system_health["services"]["service_c"]
        
        # Service A should be unhealthy
        assert service_a_health["status"] in ["degraded", "failed"]
        
        # Other services should remain healthy
        assert service_b_health["status"] == "healthy"
        assert service_c_health["status"] == "healthy"
        
        # Cleanup
        for service_name in services.keys():
            monitor = fault_detection.service_monitors[service_name]
            await monitor.stop_monitoring()
        
        logger.info("Fault isolation boundaries test passed!")
    
    @pytest.mark.asyncio
    async def test_graceful_degradation(self, error_handler_system):
        """Test graceful degradation when services fail"""
        
        logger.info("Testing graceful degradation...")
        
        error_handler = error_handler_system["error_handler"]
        
        # Mock system with primary and fallback services
        class DegradableSystem:
            def __init__(self):
                self.primary_available = True
                self.fallback_mode = False
                self.operations_completed = 0
            
            async def process_request(self, request):
                self.operations_completed += 1
                
                if self.primary_available and not self.fallback_mode:
                    # Normal operation
                    return f"primary_result_{request}"
                elif self.fallback_mode:
                    # Degraded operation
                    return f"fallback_result_{request}"
                else:
                    # Service unavailable
                    raise Exception("Service completely unavailable")
            
            def trigger_primary_failure(self):
                self.primary_available = False
            
            def enable_fallback_mode(self):
                self.fallback_mode = True
            
            def restore_primary(self):
                self.primary_available = True
                self.fallback_mode = False
        
        system = DegradableSystem()
        
        # Register degradation recovery strategy
        recovery_strategy = RecoveryStrategy(
            error_pattern=r".*unavailable.*",
            category=ErrorCategory.SERVICE,
            severity=ErrorSeverity.HIGH,
            action=RecoveryAction.FALLBACK_MODE,
            max_retry_attempts=1
        )
        error_handler.register_strategy(recovery_strategy)
        
        # Test normal operation
        result = await system.process_request("test1")
        assert "primary_result" in result
        
        # Trigger primary failure
        system.trigger_primary_failure()
        
        # Handle failure through error handler
        try:
            await system.process_request("test2")
        except Exception as e:
            context = ErrorContext(
                service_name="degradable_system",
                service_state="processing",
                timestamp=time.time(),
                thread_id=threading.get_ident(),
                function_name="process_request"
            )
            
            recovery_result = await error_handler.handle_error(
                e, context, ErrorCategory.SERVICE, ErrorSeverity.HIGH
            )
            
            # Enable fallback mode as recovery action
            if recovery_result and recovery_result.recovery_attempted:
                system.enable_fallback_mode()
        
        # Test degraded operation
        result = await system.process_request("test3")
        assert "fallback_result" in result
        
        # Restore primary service
        system.restore_primary()
        result = await system.process_request("test4")
        assert "primary_result" in result
        
        logger.info("Graceful degradation test passed!")
    
    @pytest.mark.asyncio
    async def test_fault_simulation_integration(self, error_handler_system):
        """Test integration with fault simulation system"""
        
        logger.info("Testing fault simulation integration...")
        
        error_handler = error_handler_system["error_handler"]
        fault_detection = error_handler_system["fault_detection"]
        
        # Create fault simulator
        simulator = FaultSimulator()
        
        # Create mock service for simulation
        mock_service = MockFailureService("simulation_target")
        await mock_service.initialize()
        await mock_service.start()
        
        # Register service for monitoring
        monitor = fault_detection.register_service("simulation_target")
        
        health_check = HealthCheck(
            name="simulation_target_health",
            check_function=mock_service.is_healthy,
            interval=0.1,
            timeout=0.05,
            failure_threshold=1
        )
        monitor.add_health_check(health_check)
        
        # Add recovery action
        monitor.add_recovery_action(
            FailureType.SERVICE_CRASH,
            "recover_simulation_target",
            mock_service.recover_from_failure
        )
        
        await monitor.start_monitoring()
        
        # Define fault scenario
        scenario = FaultScenario(
            name="integration_test",
            fault_type=FaultType.SERVICE_CRASH,
            simulation_mode=SimulationMode.IMMEDIATE,
            target_service="simulation_target",
            duration=1.0,
            intensity=0.8
        )
        
        # Run fault simulation
        simulation_result = await simulator.run_scenario(scenario)
        
        # Wait for recovery
        await asyncio.sleep(0.5)
        
        # Verify simulation results
        assert simulation_result["scenario_executed"] is True
        assert len(simulation_result["errors_generated"]) > 0
        assert simulation_result["final_status"] in ["completed", "recovered"]
        
        # Verify recovery was attempted
        assert len(mock_service.recovery_attempts) > 0
        
        # Cleanup
        await monitor.stop_monitoring()
        await simulator.cleanup()
        
        logger.info("Fault simulation integration test passed!")
    
    @pytest.mark.asyncio
    async def test_service_integration_mixin(self, error_handler_system):
        """Test service integration using ServiceIntegrationMixin"""
        
        logger.info("Testing service integration mixin...")
        
        # Create service using integration mixin
        class TestIntegratedService(ServiceIntegrationMixin):
            def __init__(self):
                super().__init__("test_integrated_service")
                self.processing_count = 0
                self.failure_mode = None
            
            async def initialize(self) -> bool:
                await self.setup_error_handling()
                self.is_initialized = True
                return True
            
            async def start(self) -> bool:
                await self.start_monitoring()
                self.is_running = True
                return True
            
            async def stop(self) -> bool:
                await self.stop_monitoring()
                self.is_running = False
                return True
            
            async def cleanup(self) -> bool:
                self.is_initialized = False
                return True
            
            async def process_data_with_errors(self, data):
                """Method that may fail and uses error handling"""
                self.processing_count += 1
                
                if self.failure_mode == "processing_error":
                    raise Exception(f"Processing failed for data: {data}")
                
                return f"processed_{data}"
            
            def health_check(self) -> bool:
                return self.is_running and self.failure_mode is None
            
            def recovery_action(self) -> bool:
                try:
                    self.failure_mode = None
                    return True
                except:
                    return False
        
        # Create and initialize integrated service
        service = TestIntegratedService()
        
        # Add health check and recovery action
        service.add_health_check(
            "integrated_health",
            service.health_check,
            interval=0.1
        )
        
        service.add_recovery_action(
            FailureType.UNRESPONSIVE,
            "integrated_recovery",
            service.recovery_action
        )
        
        # Initialize and start service
        await service.initialize()
        await service.start()
        
        # Test normal operation
        result = await service.process_data_with_errors("test_data")
        assert result == "processed_test_data"
        
        # Introduce failure
        service.failure_mode = "processing_error"
        
        # Wait for health check to detect failure and recover
        await asyncio.sleep(0.5)
        
        # Verify recovery was attempted
        assert service.failure_mode is None  # Should be cleared by recovery
        
        # Test operation after recovery
        result = await service.process_data_with_errors("recovery_test")
        assert result == "processed_recovery_test"
        
        # Cleanup
        await service.stop()
        await service.cleanup()
        
        logger.info("Service integration mixin test passed!")


class TestComprehensiveRecoveryScenarios:
    """Test comprehensive recovery scenarios"""
    
    @pytest.mark.asyncio
    async def test_multi_level_recovery_strategy(self, error_handler_system):
        """Test multi-level recovery strategies"""
        
        logger.info("Testing multi-level recovery strategies...")
        
        error_handler = error_handler_system["error_handler"]
        
        # Register multiple recovery strategies with different priorities
        strategies = [
            RecoveryStrategy(
                error_pattern=r".*connection.*timeout.*",
                category=ErrorCategory.NETWORK,
                severity=ErrorSeverity.MEDIUM,
                action=RecoveryAction.RETRY,
                max_retry_attempts=2,
                retry_delay=0.1,
                priority=1  # First level
            ),
            RecoveryStrategy(
                error_pattern=r".*connection.*timeout.*",
                category=ErrorCategory.NETWORK,
                severity=ErrorSeverity.HIGH,
                action=RecoveryAction.RECONNECT,
                max_retry_attempts=1,
                retry_delay=0.2,
                priority=2  # Second level
            ),
            RecoveryStrategy(
                error_pattern=r".*connection.*timeout.*",
                category=ErrorCategory.NETWORK,
                severity=ErrorSeverity.CRITICAL,
                action=RecoveryAction.RESTART_SERVICE,
                max_retry_attempts=1,
                retry_delay=0.3,
                priority=3  # Third level
            )
        ]
        
        for strategy in strategies:
            error_handler.register_strategy(strategy)
        
        # Simulate escalating failures
        failure_count = 0
        recovery_attempts = []
        
        for severity in [ErrorSeverity.MEDIUM, ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]:
            try:
                failure_count += 1
                raise Exception(f"Connection timeout error #{failure_count}")
            except Exception as e:
                context = ErrorContext(
                    service_name="network_service",
                    service_state="connecting",
                    timestamp=time.time(),
                    thread_id=threading.get_ident(),
                    function_name="connect"
                )
                
                recovery_result = await error_handler.handle_error(
                    e, context, ErrorCategory.NETWORK, severity
                )
                
                if recovery_result:
                    recovery_attempts.append({
                        "severity": severity,
                        "action": recovery_result.action_taken,
                        "success": recovery_result.success
                    })
        
        # Verify different recovery actions were taken for different severities
        assert len(recovery_attempts) == 3
        
        actions_taken = [attempt["action"] for attempt in recovery_attempts]
        assert RecoveryAction.RETRY in actions_taken
        assert RecoveryAction.RECONNECT in actions_taken
        assert RecoveryAction.RESTART_SERVICE in actions_taken
        
        logger.info("Multi-level recovery strategy test passed!")
    
    @pytest.mark.asyncio
    async def test_recovery_timeout_handling(self, error_handler_system):
        """Test recovery timeout handling"""
        
        logger.info("Testing recovery timeout handling...")
        
        error_handler = error_handler_system["error_handler"]
        
        # Track recovery attempts
        recovery_start_times = []
        recovery_timeouts = []
        
        # Mock slow recovery function
        async def slow_recovery_action():
            recovery_start_times.append(time.time())
            # Simulate recovery that takes too long
            await asyncio.sleep(1.0)
            return True
        
        # Register strategy with short timeout
        recovery_strategy = RecoveryStrategy(
            error_pattern=r".*slow.*recovery.*",
            category=ErrorCategory.SERVICE,
            severity=ErrorSeverity.MEDIUM,
            action=RecoveryAction.RESTART_SERVICE,
            timeout=0.2,  # Short timeout
            max_retry_attempts=1,
            custom_handler=slow_recovery_action
        )
        error_handler.register_strategy(recovery_strategy)
        
        # Trigger error that requires slow recovery
        try:
            raise Exception("Error requiring slow recovery")
        except Exception as e:
            context = ErrorContext(
                service_name="slow_service",
                service_state="failed",
                timestamp=time.time(),
                thread_id=threading.get_ident(),
                function_name="recover"
            )
            
            start_time = time.time()
            recovery_result = await error_handler.handle_error(
                e, context, ErrorCategory.SERVICE, ErrorSeverity.MEDIUM
            )
            end_time = time.time()
            
            # Verify recovery attempt was made but timed out
            assert recovery_result is not None
            assert recovery_result.recovery_attempted is True
            
            # Recovery should have timed out
            recovery_duration = end_time - start_time
            assert recovery_duration < 1.0  # Should be much less than the 1.0s recovery time
            
        logger.info("Recovery timeout handling test passed!")
    
    @pytest.mark.asyncio
    async def test_concurrent_error_handling(self, error_handler_system):
        """Test concurrent error handling"""
        
        logger.info("Testing concurrent error handling...")
        
        error_handler = error_handler_system["error_handler"]
        
        # Track concurrent operations
        concurrent_errors = []
        concurrent_recoveries = []
        
        async def generate_concurrent_error(error_id: int):
            try:
                # Simulate various error types
                error_types = [
                    "Network connection failed",
                    "Service processing error", 
                    "Memory allocation failed",
                    "Timeout occurred"
                ]
                error_message = f"{error_types[error_id % len(error_types)]} #{error_id}"
                raise Exception(error_message)
                
            except Exception as e:
                concurrent_errors.append(error_id)
                
                context = ErrorContext(
                    service_name=f"concurrent_service_{error_id}",
                    service_state="processing",
                    timestamp=time.time(),
                    thread_id=threading.get_ident(),
                    function_name="concurrent_operation"
                )
                
                recovery_result = await error_handler.handle_error(
                    e, context, ErrorCategory.SERVICE, ErrorSeverity.MEDIUM
                )
                
                if recovery_result and recovery_result.recovery_attempted:
                    concurrent_recoveries.append(error_id)
                
                return recovery_result
        
        # Generate many concurrent errors
        error_count = 20
        tasks = []
        for i in range(error_count):
            task = asyncio.create_task(generate_concurrent_error(i))
            tasks.append(task)
        
        # Wait for all concurrent error handling to complete
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Verify all errors were handled
        assert len(concurrent_errors) == error_count
        assert len(concurrent_recoveries) > 0  # Some recovery attempts should have been made
        
        # Verify error handler statistics
        stats = error_handler.get_error_statistics()
        assert stats["total_errors"] >= error_count
        
        logger.info(f"Handled {error_count} concurrent errors, {len(concurrent_recoveries)} recoveries")
        logger.info("Concurrent error handling test passed!")


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v", "-s", "--tb=short"])