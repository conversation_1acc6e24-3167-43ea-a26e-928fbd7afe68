"""
Simulation Service for Legged Robot Gymnasium
Manages simulation configuration and environment parameters with Zenoh integration
"""

import asyncio
import logging
import time
import os
import json
from typing import Optional, Dict, Any, Callable, List
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime

from zenoh_services.core.enhanced_session_manager import EnhancedZenohSessionManager
from zenoh_services.core.topic_manager import TopicManager
from zenoh_services.core.data_models import (
    SimulationConfig, TerrainConfig, PhysicsConfig, RobotConfig, 
    SerializationMixin, ConfigurationRequest, ConfigurationResponse
)
from zenoh_services.core.message_format import MessageFactory, MessageType, Priority
from zenoh_services.core import topics

logger = logging.getLogger(__name__)


class SimulationServiceState(Enum):
    """Simulation service states"""
    IDLE = "idle"
    CONFIGURING = "configuring"
    RUNNING = "running"
    UPDATING = "updating"
    ERROR = "error"


@dataclass
class SimulationServiceConfig:
    """Configuration for simulation service"""
    # Default simulation parameters
    default_terrain_type: str = "plane"
    default_terrain_size: tuple = (10, 10)
    default_physics_timestep: float = 0.005
    default_gravity: tuple = (0.0, 0.0, -9.81)
    
    # Service parameters
    config_update_interval: float = 1.0
    validation_enabled: bool = True
    backup_enabled: bool = True
    
    # File paths
    config_storage_path: str = "./simulation_configs"
    backup_path: str = "./config_backups"
    
    # Performance settings
    max_concurrent_updates: int = 5
    update_timeout: float = 30.0


@dataclass
class SimulationStatus(SerializationMixin):
    """Current simulation status"""
    state: SimulationServiceState
    active_config: Optional[str] = None
    last_update: float = field(default_factory=time.time)
    pending_updates: int = 0
    validation_errors: List[str] = field(default_factory=list)
    message: str = ""
    
    def validate(self) -> bool:
        """Validate simulation status"""
        return True


class SimulationService:
    """
    Simulation configuration management service with Zenoh integration
    Handles dynamic updates of terrain, physics, and robot parameters
    """
    
    def __init__(self, session_manager: EnhancedZenohSessionManager, config: SimulationServiceConfig = None):
        self.session_manager = session_manager
        self.topic_manager = TopicManager(session_manager)
        self.config = config or SimulationServiceConfig()
        
        # Service state
        self.state = SimulationServiceState.IDLE
        self.is_initialized = False
        
        # Configuration management
        self.current_simulation_config: Optional[SimulationConfig] = None
        self.current_terrain_config: Optional[TerrainConfig] = None
        self.current_physics_config: Optional[PhysicsConfig] = None
        self.current_robot_config: Optional[RobotConfig] = None
        
        # Configuration history and backup
        self.config_history: List[Dict[str, Any]] = []
        self.max_history_size = 50
        
        # Background tasks
        self.status_task: Optional[asyncio.Task] = None
        self.config_monitor_task: Optional[asyncio.Task] = None
        
        # Update management
        self.pending_updates: Dict[str, asyncio.Future] = {}
        self.update_lock = asyncio.Lock()
        
    async def initialize(self) -> bool:
        """Initialize the simulation service"""
        try:
            logger.info("Initializing simulation service...")
            
            # Create storage directories
            self._create_storage_directories()
            
            # Register topics
            await self._register_topics()
            
            # Setup command subscribers
            await self._setup_command_subscribers()
            
            # Load default configurations
            await self._load_default_configurations()
            
            # Start background tasks
            await self._start_background_tasks()
            
            self.is_initialized = True
            self.state = SimulationServiceState.IDLE
            logger.info("Simulation service initialized successfully")
            return True
            
        except Exception as e:
            self.state = SimulationServiceState.ERROR
            logger.error(f"Failed to initialize simulation service: {e}")
            return False
    
    def _create_storage_directories(self):
        """Create necessary storage directories"""
        directories = [
            self.config.config_storage_path,
            self.config.backup_path
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    async def _register_topics(self):
        """Register all simulation-related topics"""
        topics_to_register = [
            topics.SIMULATION_CONFIG,
            topics.SIMULATION_STATUS,
            topics.TERRAIN_CONFIG,
            topics.PHYSICS_CONFIG,
            topics.ROBOT_CONFIG
        ]
        
        for topic in topics_to_register:
            await self.topic_manager.register_topic(topic)
            await self.topic_manager.create_publisher(topic)
    
    async def _setup_command_subscribers(self):
        """Setup subscribers for configuration update commands"""
        # Subscribe to configuration update requests
        await self.topic_manager.create_subscriber(
            topics.SIMULATION_CONFIG, 
            self._handle_simulation_config_update
        )
        
        await self.topic_manager.create_subscriber(
            topics.TERRAIN_CONFIG,
            self._handle_terrain_config_update
        )
        
        await self.topic_manager.create_subscriber(
            topics.PHYSICS_CONFIG,
            self._handle_physics_config_update
        )
        
        await self.topic_manager.create_subscriber(
            topics.ROBOT_CONFIG,
            self._handle_robot_config_update
        )
    
    async def _load_default_configurations(self):
        """Load default configuration values"""
        # Default terrain configuration
        self.current_terrain_config = TerrainConfig(
            mesh_type=self.config.default_terrain_type,
            terrain_length=float(self.config.default_terrain_size[0]),
            terrain_width=float(self.config.default_terrain_size[1]),
            horizontal_scale=0.1,
            vertical_scale=0.005,
            border_size=0.0,
            slope_threshold=0.75
        )
        
        # Default physics configuration
        self.current_physics_config = PhysicsConfig(
            dt=self.config.default_physics_timestep,
            gravity=list(self.config.default_gravity),
            solver_type="PGS",
            num_position_iterations=4,
            num_velocity_iterations=1,
            contact_offset=0.02,
            rest_offset=0.0,
            bounce_threshold_velocity=0.2,
            max_depenetration_velocity=1.0
        )
        
        # Default robot configuration
        self.current_robot_config = RobotConfig(
            name="anymal_c",
            urdf_path="",
            base_mass=35.0,
            joint_stiffness=80.0,
            joint_damping=2.0,
            contact_friction=1.0,
            control_frequency=50.0
        )
        
        # Combined simulation configuration
        self.current_simulation_config = SimulationConfig(
            terrain=self.current_terrain_config,
            physics=self.current_physics_config,
            robot=self.current_robot_config,
            num_envs=4096,
            episode_length=1000,
            simulation_name="default_simulation",
            version="1.0.0"
        )
        
        logger.info("Default configurations loaded")
    
    async def _start_background_tasks(self):
        """Start background monitoring tasks"""
        self.status_task = asyncio.create_task(self._status_publishing_loop())
        self.config_monitor_task = asyncio.create_task(self._config_monitoring_loop())
        logger.debug("Background tasks started")
    
    async def _handle_simulation_config_update(self, message):
        """Handle simulation configuration update requests"""
        try:
            if hasattr(message, 'payload'):
                config_data = message.payload
            else:
                config_data = message
            
            # Create configuration request
            request = ConfigurationRequest.from_dict(config_data)
            
            if request.action == "set":
                await self._update_simulation_config(request.config_data)
            elif request.action == "get":
                await self._publish_current_config("simulation")
            elif request.action == "validate":
                await self._validate_simulation_config(request.config_data)
            elif request.action == "reset":
                await self._reset_to_defaults()
            
        except Exception as e:
            logger.error(f"Error handling simulation config update: {e}")
            await self._send_config_response("simulation", False, str(e))
    
    async def _handle_terrain_config_update(self, message):
        """Handle terrain configuration updates"""
        try:
            if hasattr(message, 'payload'):
                config_data = message.payload
            else:
                config_data = message
            
            terrain_config = TerrainConfig.from_dict(config_data)
            await self.update_terrain_config(terrain_config)
            
        except Exception as e:
            logger.error(f"Error handling terrain config update: {e}")
            await self._send_config_response("terrain", False, str(e))
    
    async def _handle_physics_config_update(self, message):
        """Handle physics configuration updates"""
        try:
            if hasattr(message, 'payload'):
                config_data = message.payload
            else:
                config_data = message
            
            physics_config = PhysicsConfig.from_dict(config_data)
            await self.update_physics_config(physics_config)
            
        except Exception as e:
            logger.error(f"Error handling physics config update: {e}")
            await self._send_config_response("physics", False, str(e))
    
    async def _handle_robot_config_update(self, message):
        """Handle robot configuration updates"""
        try:
            if hasattr(message, 'payload'):
                config_data = message.payload
            else:
                config_data = message
            
            robot_config = RobotConfig.from_dict(config_data)
            await self.update_robot_config(robot_config)
            
        except Exception as e:
            logger.error(f"Error handling robot config update: {e}")
            await self._send_config_response("robot", False, str(e))
    
    async def update_terrain_config(self, terrain_config: TerrainConfig) -> bool:
        """Update terrain configuration"""
        try:
            async with self.update_lock:
                self.state = SimulationServiceState.UPDATING
                
                # Validate configuration
                if self.config.validation_enabled and not terrain_config.validate():
                    raise ValueError("Invalid terrain configuration")
                
                # Backup current configuration
                if self.config.backup_enabled:
                    await self._backup_config("terrain", self.current_terrain_config)
                
                # Update configuration
                old_config = self.current_terrain_config
                self.current_terrain_config = terrain_config
                
                # Update combined simulation config
                if self.current_simulation_config:
                    self.current_simulation_config.terrain = terrain_config
                
                # Save configuration
                await self._save_config("terrain", terrain_config)
                
                # Add to history
                self._add_to_history("terrain_update", {
                    "old": old_config.to_dict() if old_config else None,
                    "new": terrain_config.to_dict(),
                    "timestamp": time.time()
                })
                
                # Publish updated configuration
                message = MessageFactory.create_data(terrain_config.to_dict(), "simulation_service")
                await self.topic_manager.publish_message(topics.TERRAIN_CONFIG, message)
                
                await self._send_config_response("terrain", True, "Terrain configuration updated")
                
                self.state = SimulationServiceState.RUNNING
                logger.info("Terrain configuration updated successfully")
                return True
                
        except Exception as e:
            self.state = SimulationServiceState.ERROR
            logger.error(f"Failed to update terrain configuration: {e}")
            await self._send_config_response("terrain", False, str(e))
            return False
    
    async def update_physics_config(self, physics_config: PhysicsConfig) -> bool:
        """Update physics configuration"""
        try:
            async with self.update_lock:
                self.state = SimulationServiceState.UPDATING
                
                # Validate configuration
                if self.config.validation_enabled and not physics_config.validate():
                    raise ValueError("Invalid physics configuration")
                
                # Backup current configuration
                if self.config.backup_enabled:
                    await self._backup_config("physics", self.current_physics_config)
                
                # Update configuration
                old_config = self.current_physics_config
                self.current_physics_config = physics_config
                
                # Update combined simulation config
                if self.current_simulation_config:
                    self.current_simulation_config.physics = physics_config
                
                # Save configuration
                await self._save_config("physics", physics_config)
                
                # Add to history
                self._add_to_history("physics_update", {
                    "old": old_config.to_dict() if old_config else None,
                    "new": physics_config.to_dict(),
                    "timestamp": time.time()
                })
                
                # Publish updated configuration
                message = MessageFactory.create_data(physics_config.to_dict(), "simulation_service")
                await self.topic_manager.publish_message(topics.PHYSICS_CONFIG, message)
                
                await self._send_config_response("physics", True, "Physics configuration updated")
                
                self.state = SimulationServiceState.RUNNING
                logger.info("Physics configuration updated successfully")
                return True
                
        except Exception as e:
            self.state = SimulationServiceState.ERROR
            logger.error(f"Failed to update physics configuration: {e}")
            await self._send_config_response("physics", False, str(e))
            return False
    
    async def update_robot_config(self, robot_config: RobotConfig) -> bool:
        """Update robot configuration"""
        try:
            async with self.update_lock:
                self.state = SimulationServiceState.UPDATING
                
                # Validate configuration
                if self.config.validation_enabled and not robot_config.validate():
                    raise ValueError("Invalid robot configuration")
                
                # Backup current configuration
                if self.config.backup_enabled:
                    await self._backup_config("robot", self.current_robot_config)
                
                # Update configuration
                old_config = self.current_robot_config
                self.current_robot_config = robot_config
                
                # Update combined simulation config
                if self.current_simulation_config:
                    self.current_simulation_config.robot = robot_config
                
                # Save configuration
                await self._save_config("robot", robot_config)
                
                # Add to history
                self._add_to_history("robot_update", {
                    "old": old_config.to_dict() if old_config else None,
                    "new": robot_config.to_dict(),
                    "timestamp": time.time()
                })
                
                # Publish updated configuration
                message = MessageFactory.create_data(robot_config.to_dict(), "simulation_service")
                await self.topic_manager.publish_message(topics.ROBOT_CONFIG, message)
                
                await self._send_config_response("robot", True, "Robot configuration updated")
                
                self.state = SimulationServiceState.RUNNING
                logger.info("Robot configuration updated successfully")
                return True
                
        except Exception as e:
            self.state = SimulationServiceState.ERROR
            logger.error(f"Failed to update robot configuration: {e}")
            await self._send_config_response("robot", False, str(e))
            return False
    
    async def _update_simulation_config(self, config_data: Dict[str, Any]) -> bool:
        """Update complete simulation configuration"""
        try:
            simulation_config = SimulationConfig.from_dict(config_data)
            
            async with self.update_lock:
                self.state = SimulationServiceState.UPDATING
                
                # Validate configuration
                if self.config.validation_enabled and not simulation_config.validate():
                    raise ValueError("Invalid simulation configuration")
                
                # Backup current configuration
                if self.config.backup_enabled:
                    await self._backup_config("simulation", self.current_simulation_config)
                
                # Update all sub-configurations
                old_config = self.current_simulation_config
                self.current_simulation_config = simulation_config
                self.current_terrain_config = simulation_config.terrain
                self.current_physics_config = simulation_config.physics
                self.current_robot_config = simulation_config.robot
                
                # Save configuration
                await self._save_config("simulation", simulation_config)
                
                # Add to history
                self._add_to_history("simulation_update", {
                    "old": old_config.to_dict() if old_config else None,
                    "new": simulation_config.to_dict(),
                    "timestamp": time.time()
                })
                
                # Publish updated configuration
                message = MessageFactory.create_data(simulation_config.to_dict(), "simulation_service")
                await self.topic_manager.publish_message(topics.SIMULATION_CONFIG, message)
                
                await self._send_config_response("simulation", True, "Simulation configuration updated")
                
                self.state = SimulationServiceState.RUNNING
                logger.info("Simulation configuration updated successfully")
                return True
                
        except Exception as e:
            self.state = SimulationServiceState.ERROR
            logger.error(f"Failed to update simulation configuration: {e}")
            await self._send_config_response("simulation", False, str(e))
            return False
    
    async def _validate_simulation_config(self, config_data: Dict[str, Any]):
        """Validate simulation configuration"""
        try:
            simulation_config = SimulationConfig.from_dict(config_data)
            is_valid = simulation_config.validate()
            
            validation_response = ConfigurationResponse(
                success=is_valid,
                config_type="simulation",
                config_data=config_data,
                error_message="" if is_valid else "Configuration validation failed",
                timestamp=time.time()
            )
            
            message = MessageFactory.create_data(validation_response.to_dict(), "simulation_service")
            await self.topic_manager.publish_message(topics.SIMULATION_STATUS, message)
            
        except Exception as e:
            logger.error(f"Error validating simulation configuration: {e}")
    
    async def _reset_to_defaults(self):
        """Reset configurations to default values"""
        try:
            await self._load_default_configurations()
            await self._publish_current_config("all")
            await self._send_config_response("simulation", True, "Configuration reset to defaults")
            logger.info("Configuration reset to defaults")
            
        except Exception as e:
            logger.error(f"Failed to reset configuration: {e}")
            await self._send_config_response("simulation", False, str(e))
    
    async def _publish_current_config(self, config_type: str):
        """Publish current configuration"""
        try:
            if config_type == "simulation" or config_type == "all":
                if self.current_simulation_config:
                    message = MessageFactory.create_data(
                        self.current_simulation_config.to_dict(), 
                        "simulation_service"
                    )
                    await self.topic_manager.publish_message(topics.SIMULATION_CONFIG, message)
            
            if config_type == "terrain" or config_type == "all":
                if self.current_terrain_config:
                    message = MessageFactory.create_data(
                        self.current_terrain_config.to_dict(), 
                        "simulation_service"
                    )
                    await self.topic_manager.publish_message(topics.TERRAIN_CONFIG, message)
            
            if config_type == "physics" or config_type == "all":
                if self.current_physics_config:
                    message = MessageFactory.create_data(
                        self.current_physics_config.to_dict(), 
                        "simulation_service"
                    )
                    await self.topic_manager.publish_message(topics.PHYSICS_CONFIG, message)
            
            if config_type == "robot" or config_type == "all":
                if self.current_robot_config:
                    message = MessageFactory.create_data(
                        self.current_robot_config.to_dict(), 
                        "simulation_service"
                    )
                    await self.topic_manager.publish_message(topics.ROBOT_CONFIG, message)
            
        except Exception as e:
            logger.error(f"Failed to publish current configuration: {e}")
    
    async def _send_config_response(self, config_type: str, success: bool, message: str):
        """Send configuration response"""
        try:
            response = ConfigurationResponse(
                success=success,
                config_type=config_type,
                error_message=message if not success else "",
                timestamp=time.time()
            )
            
            response_message = MessageFactory.create_status(response.to_dict(), "simulation_service")
            await self.topic_manager.publish_message(topics.SIMULATION_STATUS, response_message)
            
        except Exception as e:
            logger.error(f"Failed to send configuration response: {e}")
    
    async def _save_config(self, config_type: str, config_obj):
        """Save configuration to file"""
        try:
            config_path = os.path.join(
                self.config.config_storage_path, 
                f"{config_type}_config.json"
            )
            
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config_obj.to_dict(), f, indent=2)
                
        except Exception as e:
            logger.error(f"Failed to save {config_type} configuration: {e}")
    
    async def _backup_config(self, config_type: str, config_obj):
        """Backup current configuration"""
        if not config_obj:
            return
            
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_path = os.path.join(
                self.config.backup_path,
                f"{config_type}_config_backup_{timestamp}.json"
            )
            
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(config_obj.to_dict(), f, indent=2)
                
        except Exception as e:
            logger.error(f"Failed to backup {config_type} configuration: {e}")
    
    def _add_to_history(self, operation: str, data: Dict[str, Any]):
        """Add operation to history"""
        self.config_history.append({
            "operation": operation,
            "data": data,
            "timestamp": time.time()
        })
        
        # Maintain history size limit
        if len(self.config_history) > self.max_history_size:
            self.config_history.pop(0)
    
    async def _status_publishing_loop(self):
        """Publish simulation status periodically"""
        try:
            while self.is_initialized:
                status = SimulationStatus(
                    state=self.state,
                    active_config=self.current_simulation_config.simulation_name if self.current_simulation_config else None,
                    pending_updates=len(self.pending_updates),
                    message=f"Simulation service running in {self.state.value} state"
                )
                
                message = MessageFactory.create_status(status.to_dict(), "simulation_service")
                await self.topic_manager.publish_message(topics.SIMULATION_STATUS, message)
                
                await asyncio.sleep(self.config.config_update_interval)
                
        except asyncio.CancelledError:
            logger.debug("Status publishing loop cancelled")
        except Exception as e:
            logger.error(f"Error in status publishing loop: {e}")
    
    async def _config_monitoring_loop(self):
        """Monitor configuration changes and health"""
        try:
            while self.is_initialized:
                # Monitor for any configuration inconsistencies or issues
                # This could include checking file system changes, validation, etc.
                await asyncio.sleep(10.0)  # Check every 10 seconds
                
        except asyncio.CancelledError:
            logger.debug("Config monitoring loop cancelled")
        except Exception as e:
            logger.error(f"Error in config monitoring loop: {e}")
    
    async def get_status(self) -> Dict[str, Any]:
        """Get current service status"""
        return {
            "state": self.state.value,
            "is_initialized": self.is_initialized,
            "current_configs": {
                "simulation": self.current_simulation_config.to_dict() if self.current_simulation_config else None,
                "terrain": self.current_terrain_config.to_dict() if self.current_terrain_config else None,
                "physics": self.current_physics_config.to_dict() if self.current_physics_config else None,
                "robot": self.current_robot_config.to_dict() if self.current_robot_config else None
            },
            "history_count": len(self.config_history),
            "pending_updates": len(self.pending_updates)
        }
    
    async def get_configuration_history(self) -> List[Dict[str, Any]]:
        """Get configuration change history"""
        return self.config_history.copy()
    
    async def shutdown(self):
        """Shutdown the simulation service"""
        logger.info("Shutting down simulation service...")
        
        self.is_initialized = False
        
        # Cancel background tasks
        if self.status_task:
            self.status_task.cancel()
        if self.config_monitor_task:
            self.config_monitor_task.cancel()
        
        # Wait for tasks to complete
        await asyncio.sleep(0.1)
        
        self.state = SimulationServiceState.IDLE
        logger.info("Simulation service shutdown complete")


# Factory function for creating simulation service
async def create_simulation_service(
    session_manager: EnhancedZenohSessionManager,
    config: Optional[SimulationServiceConfig] = None
) -> SimulationService:
    """Create and initialize a simulation service"""
    service = SimulationService(session_manager, config)
    
    if await service.initialize():
        return service
    else:
        raise RuntimeError("Failed to initialize simulation service")