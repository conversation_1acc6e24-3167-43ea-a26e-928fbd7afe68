"""
Deployment Service for Legged Robot Gymnasium
Manages model export and deployment with Zenoh integration
"""

import asyncio
import logging
import time
import os
import shutil
import hashlib
from typing import Optional, Dict, Any, List
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
from pathlib import Path

# Import PyTorch and related libraries
try:
    import torch
    import torch.jit
    torch_available = True
except ImportError:
    torch_available = False

try:
    import onnx
    import onnxruntime
    onnx_available = True
except ImportError:
    onnx_available = False

from zenoh_services.core.enhanced_session_manager import EnhancedZenohSessionManager
from zenoh_services.core.topic_manager import TopicManager
from zenoh_services.core.data_models import (
    DeploymentConfig, DeploymentStatus, DeploymentState, ModelInfo, 
    SerializationMixin, ConfigurationRequest, ConfigurationResponse
)
from zenoh_services.core.message_format import MessageFactory, MessageType, Priority
from zenoh_services.core import topics

# Import legged_gym utilities
try:
    from legged_gym.utils import export_policy_as_jit
    from legged_gym import LEGGED_GYM_ROOT_DIR
    legged_gym_available = True
except ImportError:
    legged_gym_available = False

logger = logging.getLogger(__name__)


class DeploymentServiceState(Enum):
    """Deployment service states"""
    IDLE = "idle"
    PREPARING = "preparing"
    EXPORTING = "exporting"
    VALIDATING = "validating"
    DEPLOYING = "deploying"
    COMPLETED = "completed"
    ERROR = "error"


@dataclass
class DeploymentServiceConfig:
    """Configuration for deployment service"""
    # Default export parameters
    default_export_format: str = "jit"
    default_target_platform: str = "cpu"
    default_optimization_level: str = "default"
    
    # Service parameters
    validation_enabled: bool = True
    backup_original: bool = True
    auto_versioning: bool = True
    
    # File paths
    export_base_path: str = "./exported_models"
    backup_path: str = "./model_backups"
    
    # Performance settings
    max_concurrent_exports: int = 3
    export_timeout: float = 300.0  # 5 minutes
    validation_timeout: float = 60.0  # 1 minute
    
    # Model optimization
    torch_script_optimize: bool = True
    onnx_optimization_level: str = "basic"  # basic, extended, layout
    quantization_enabled: bool = False


@dataclass 
class ExportJobStatus(SerializationMixin):
    """Status of an individual export job"""
    job_id: str
    state: DeploymentServiceState
    source_model: str = ""
    target_format: str = ""
    target_path: str = ""
    progress: float = 0.0
    start_time: float = field(default_factory=time.time)
    last_update: float = field(default_factory=time.time)
    error_message: str = ""
    validation_results: Dict[str, Any] = field(default_factory=dict)
    
    def validate(self) -> bool:
        """Validate export job status"""
        return True


@dataclass
class ModelValidationResult(SerializationMixin):
    """Results from model validation"""
    is_valid: bool
    model_format: str
    model_size_mb: float
    input_shapes: Dict[str, List[int]] = field(default_factory=dict)
    output_shapes: Dict[str, List[int]] = field(default_factory=dict)
    inference_time_ms: float = 0.0
    validation_errors: List[str] = field(default_factory=list)
    compatibility_info: Dict[str, Any] = field(default_factory=dict)
    
    def validate(self) -> bool:
        """Validate validation result"""
        return True


class DeploymentService:
    """
    Deployment service for model export and deployment management with Zenoh integration
    Handles JIT/TorchScript and ONNX model export, validation and version management
    """
    
    def __init__(self, session_manager: EnhancedZenohSessionManager, config: DeploymentServiceConfig = None):
        self.session_manager = session_manager
        self.topic_manager = TopicManager(session_manager)
        self.config = config or DeploymentServiceConfig()
        
        # Service state
        self.state = DeploymentServiceState.IDLE
        self.is_initialized = False
        
        # Job management
        self.active_jobs: Dict[str, ExportJobStatus] = {}
        self.job_history: List[ExportJobStatus] = []
        self.max_history_size = 100
        
        # Model registry
        self.available_models: Dict[str, ModelInfo] = {}
        self.deployed_models: Dict[str, ModelInfo] = {}
        
        # Background tasks
        self.status_task: Optional[asyncio.Task] = None
        self.job_monitor_task: Optional[asyncio.Task] = None
        
        # Export management
        self.export_lock = asyncio.Lock()
        self.job_counter = 0
        
    async def initialize(self) -> bool:
        """Initialize the deployment service"""
        try:
            logger.info("Initializing deployment service...")
            
            # Check dependencies
            if not torch_available:
                logger.warning("PyTorch not available - some export features disabled")
            if not onnx_available:
                logger.warning("ONNX not available - ONNX export disabled")
            if not legged_gym_available:
                logger.warning("Legged Gym utils not available - some export features disabled")
            
            # Create storage directories
            self._create_storage_directories()
            
            # Register topics
            await self._register_topics()
            
            # Setup command subscribers
            await self._setup_command_subscribers()
            
            # Scan for existing models
            await self._scan_available_models()
            
            # Start background tasks
            await self._start_background_tasks()
            
            self.is_initialized = True
            self.state = DeploymentServiceState.IDLE
            logger.info("Deployment service initialized successfully")
            return True
            
        except Exception as e:
            self.state = DeploymentServiceState.ERROR
            logger.error(f"Failed to initialize deployment service: {e}")
            return False
    
    def _create_storage_directories(self):
        """Create necessary storage directories"""
        directories = [
            self.config.export_base_path,
            self.config.backup_path
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
            
        # Create subdirectories for different formats
        format_dirs = ["jit", "onnx", "pt"]
        for fmt in format_dirs:
            fmt_dir = os.path.join(self.config.export_base_path, fmt)
            os.makedirs(fmt_dir, exist_ok=True)
    
    async def _register_topics(self):
        """Register all deployment-related topics"""
        topics_to_register = [
            topics.DEPLOYMENT_COMMAND,
            topics.DEPLOYMENT_STATUS,
            topics.MODEL_INFO
        ]
        
        for topic in topics_to_register:
            await self.topic_manager.register_topic(topic)
            await self.topic_manager.create_publisher(topic)
    
    async def _setup_command_subscribers(self):
        """Setup subscribers for deployment commands"""
        await self.topic_manager.create_subscriber(
            topics.DEPLOYMENT_COMMAND, 
            self._handle_deployment_command
        )
    
    async def _handle_deployment_command(self, message):
        """Handle deployment command requests"""
        try:
            if hasattr(message, 'payload'):
                command_data = message.payload
            else:
                command_data = message
            
            # Create configuration request
            request = ConfigurationRequest.from_dict(command_data)
            
            if request.action == "export":
                await self._handle_export_request(request.config_data)
            elif request.action == "validate":
                await self._handle_validation_request(request.config_data)
            elif request.action == "list_models":
                await self._handle_list_models_request()
            elif request.action == "get_status":
                await self._handle_status_request(request.config_data.get("job_id"))
            elif request.action == "cancel":
                await self._handle_cancel_request(request.config_data.get("job_id"))
            
        except Exception as e:
            logger.error(f"Error handling deployment command: {e}")
            await self._send_deployment_response(False, str(e))
    
    async def _handle_export_request(self, config_data: Dict[str, Any]):
        """Handle model export request"""
        try:
            deployment_config = DeploymentConfig.from_dict(config_data)
            
            if not deployment_config.validate():
                raise ValueError("Invalid deployment configuration")
            
            job_id = await self.export_model(deployment_config)
            
            response = {
                "job_id": job_id,
                "message": "Export job started",
                "timestamp": time.time()
            }
            
            await self._send_deployment_response(True, "Export job started", response)
            
        except Exception as e:
            logger.error(f"Error handling export request: {e}")
            await self._send_deployment_response(False, str(e))
    
    async def _handle_validation_request(self, config_data: Dict[str, Any]):
        """Handle model validation request"""
        try:
            model_path = config_data.get("model_path")
            if not model_path or not os.path.exists(model_path):
                raise ValueError("Invalid or missing model path")
            
            validation_result = await self.validate_model(model_path)
            
            response = {
                "validation_result": validation_result.to_dict(),
                "timestamp": time.time()
            }
            
            await self._send_deployment_response(True, "Model validated", response)
            
        except Exception as e:
            logger.error(f"Error handling validation request: {e}")
            await self._send_deployment_response(False, str(e))
    
    async def _handle_list_models_request(self):
        """Handle list models request"""
        try:
            await self._scan_available_models()  # Refresh model list
            
            response = {
                "available_models": {k: v.to_dict() for k, v in self.available_models.items()},
                "deployed_models": {k: v.to_dict() for k, v in self.deployed_models.items()},
                "timestamp": time.time()
            }
            
            await self._send_deployment_response(True, "Model list retrieved", response)
            
        except Exception as e:
            logger.error(f"Error handling list models request: {e}")
            await self._send_deployment_response(False, str(e))
    
    async def _handle_status_request(self, job_id: Optional[str]):
        """Handle status request"""
        try:
            if job_id:
                if job_id in self.active_jobs:
                    job_status = self.active_jobs[job_id]
                    response = {
                        "job_status": job_status.to_dict(),
                        "timestamp": time.time()
                    }
                else:
                    # Check job history
                    historical_job = next((job for job in self.job_history if job.job_id == job_id), None)
                    if historical_job:
                        response = {
                            "job_status": historical_job.to_dict(),
                            "timestamp": time.time()
                        }
                    else:
                        raise ValueError(f"Job {job_id} not found")
            else:
                # Return service status
                response = await self.get_status()
            
            await self._send_deployment_response(True, "Status retrieved", response)
            
        except Exception as e:
            logger.error(f"Error handling status request: {e}")
            await self._send_deployment_response(False, str(e))
    
    async def _handle_cancel_request(self, job_id: Optional[str]):
        """Handle job cancellation request"""
        try:
            if not job_id:
                raise ValueError("Job ID required for cancellation")
            
            success = await self.cancel_export_job(job_id)
            
            if success:
                await self._send_deployment_response(True, f"Job {job_id} cancelled")
            else:
                await self._send_deployment_response(False, f"Failed to cancel job {job_id}")
            
        except Exception as e:
            logger.error(f"Error handling cancel request: {e}")
            await self._send_deployment_response(False, str(e))
    
    async def export_model(self, config: DeploymentConfig) -> str:
        """Export model according to configuration"""
        async with self.export_lock:
            # Generate job ID
            self.job_counter += 1
            job_id = f"export_{self.job_counter}_{int(time.time())}"
            
            # Create job status
            job_status = ExportJobStatus(
                job_id=job_id,
                state=DeploymentServiceState.PREPARING,
                source_model=config.model_path,
                target_format=config.export_format,
                progress=0.0
            )
            
            self.active_jobs[job_id] = job_status
            
            # Start export task
            export_task = asyncio.create_task(self._execute_export_job(job_id, config))
            
            logger.info(f"Started export job {job_id}")
            return job_id
    
    async def _execute_export_job(self, job_id: str, config: DeploymentConfig):
        """Execute the actual model export job"""
        job_status = self.active_jobs[job_id]
        
        try:
            self.state = DeploymentServiceState.EXPORTING
            job_status.state = DeploymentServiceState.EXPORTING
            job_status.progress = 0.1
            
            # Validate source model exists
            if not os.path.exists(config.model_path):
                raise FileNotFoundError(f"Source model not found: {config.model_path}")
            
            job_status.progress = 0.2
            
            # Generate export path
            export_path = await self._generate_export_path(config)
            job_status.target_path = export_path
            job_status.progress = 0.3
            
            # Backup original model if requested
            if self.config.backup_original:
                await self._backup_model(config.model_path)
            
            job_status.progress = 0.4
            
            # Perform the actual export
            if config.export_format == "jit":
                await self._export_to_jit(config.model_path, export_path, config)
            elif config.export_format == "onnx":
                await self._export_to_onnx(config.model_path, export_path, config)
            else:
                raise ValueError(f"Unsupported export format: {config.export_format}")
            
            job_status.progress = 0.8
            
            # Validate exported model
            if self.config.validation_enabled:
                job_status.state = DeploymentServiceState.VALIDATING
                validation_result = await self.validate_model(export_path)
                job_status.validation_results = validation_result.to_dict()
                
                if not validation_result.is_valid:
                    raise ValueError(f"Exported model validation failed: {validation_result.validation_errors}")
            
            job_status.progress = 0.9
            
            # Update model registry
            await self._register_exported_model(export_path, config)
            
            # Complete job
            job_status.state = DeploymentServiceState.COMPLETED
            job_status.progress = 1.0
            job_status.last_update = time.time()
            
            # Move to history
            self._move_job_to_history(job_id)
            
            # Publish completion status
            await self._publish_export_completion(job_id, export_path)
            
            logger.info(f"Export job {job_id} completed successfully")
            
        except Exception as e:
            job_status.state = DeploymentServiceState.ERROR
            job_status.error_message = str(e)
            job_status.last_update = time.time()
            
            logger.error(f"Export job {job_id} failed: {e}")
            await self._publish_export_error(job_id, str(e))
        
        finally:
            if self.state == DeploymentServiceState.EXPORTING:
                self.state = DeploymentServiceState.IDLE
    
    async def _generate_export_path(self, config: DeploymentConfig) -> str:
        """Generate export file path"""
        source_path = Path(config.model_path)
        
        # Get base name without extension
        base_name = source_path.stem
        
        # Add timestamp if auto-versioning is enabled
        if self.config.auto_versioning:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            base_name = f"{base_name}_{timestamp}"
        
        # Determine file extension
        if config.export_format == "jit":
            extension = ".pt"
        elif config.export_format == "onnx":
            extension = ".onnx"
        else:
            extension = ".bin"
        
        # Create full path
        export_dir = os.path.join(self.config.export_base_path, config.export_format)
        export_path = os.path.join(export_dir, f"{base_name}{extension}")
        
        # Handle custom export path
        if config.export_path:
            custom_path = Path(config.export_path)
            if custom_path.is_absolute():
                export_path = str(custom_path)
            else:
                export_path = os.path.join(export_dir, str(custom_path))
        
        return export_path
    
    async def _backup_model(self, model_path: str):
        """Create backup of original model"""
        try:
            source_path = Path(model_path)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = f"{source_path.stem}_backup_{timestamp}{source_path.suffix}"
            backup_path = os.path.join(self.config.backup_path, backup_name)
            
            shutil.copy2(model_path, backup_path)
            logger.info(f"Model backed up to: {backup_path}")
            
        except Exception as e:
            logger.warning(f"Failed to backup model: {e}")
    
    async def _export_to_jit(self, source_path: str, target_path: str, config: DeploymentConfig):
        """Export model to TorchScript JIT format"""
        if not torch_available:
            raise RuntimeError("PyTorch not available for JIT export")
        
        try:
            # Load the PyTorch model
            model = torch.load(source_path, map_location='cpu')
            
            # Handle different model formats
            if hasattr(model, 'state_dict'):
                # If it's a full model object
                model.eval()
                traced_model = model
            elif isinstance(model, dict) and 'model_state_dict' in model:
                # If it's a checkpoint with state dict
                raise ValueError("Cannot directly export state dict - need model architecture")
            else:
                raise ValueError("Unsupported model format for JIT export")
            
            # Create dummy input for tracing
            dummy_input = torch.randn(1, 48)  # Typical observation size for legged robots
            
            # Trace the model
            with torch.no_grad():
                traced_model = torch.jit.trace(model, dummy_input)
            
            # Optimize if requested
            if self.config.torch_script_optimize:
                traced_model = torch.jit.optimize_for_inference(traced_model)
            
            # Save the traced model
            traced_model.save(target_path)
            
            logger.info(f"Model exported to JIT format: {target_path}")
            
        except Exception as e:
            logger.error(f"JIT export failed: {e}")
            raise
    
    async def _export_to_onnx(self, source_path: str, target_path: str, config: DeploymentConfig):
        """Export model to ONNX format"""
        if not torch_available or not onnx_available:
            raise RuntimeError("PyTorch and ONNX required for ONNX export")
        
        try:
            # Load the PyTorch model
            model = torch.load(source_path, map_location='cpu')
            
            if hasattr(model, 'state_dict'):
                model.eval()
            else:
                raise ValueError("Unsupported model format for ONNX export")
            
            # Create dummy input
            dummy_input = torch.randn(1, 48)
            
            # Export to ONNX
            with torch.no_grad():
                torch.onnx.export(
                    model,
                    dummy_input,
                    target_path,
                    export_params=True,
                    opset_version=11,
                    do_constant_folding=True,
                    input_names=['observation'],
                    output_names=['action'],
                    dynamic_axes={
                        'observation': {0: 'batch_size'},
                        'action': {0: 'batch_size'}
                    }
                )
            
            # Optimize ONNX model if requested
            if self.config.onnx_optimization_level != "basic":
                await self._optimize_onnx_model(target_path)
            
            logger.info(f"Model exported to ONNX format: {target_path}")
            
        except Exception as e:
            logger.error(f"ONNX export failed: {e}")
            raise
    
    async def _optimize_onnx_model(self, model_path: str):
        """Optimize ONNX model"""
        try:
            import onnxoptimizer
            
            # Load ONNX model
            model = onnx.load(model_path)
            
            # Apply optimizations
            if self.config.onnx_optimization_level == "extended":
                passes = onnxoptimizer.get_fuse_and_elimination_passes()
            else:  # layout
                passes = onnxoptimizer.get_available_passes()
            
            optimized_model = onnxoptimizer.optimize(model, passes)
            
            # Save optimized model
            onnx.save(optimized_model, model_path)
            
            logger.info(f"ONNX model optimized: {model_path}")
            
        except ImportError:
            logger.warning("onnxoptimizer not available, skipping optimization")
        except Exception as e:
            logger.warning(f"ONNX optimization failed: {e}")
    
    async def validate_model(self, model_path: str) -> ModelValidationResult:
        """Validate exported model"""
        try:
            if not os.path.exists(model_path):
                return ModelValidationResult(
                    is_valid=False,
                    model_format="unknown",
                    model_size_mb=0.0,
                    validation_errors=["Model file not found"]
                )
            
            # Get file size
            file_size_mb = os.path.getsize(model_path) / (1024 * 1024)
            
            # Determine format from extension
            model_format = Path(model_path).suffix.lower()
            
            validation_result = ModelValidationResult(
                is_valid=True,
                model_format=model_format,
                model_size_mb=file_size_mb
            )
            
            # Perform format-specific validation
            if model_format == ".pt":
                await self._validate_jit_model(model_path, validation_result)
            elif model_format == ".onnx":
                await self._validate_onnx_model(model_path, validation_result)
            else:
                validation_result.validation_errors.append(f"Unsupported format: {model_format}")
                validation_result.is_valid = False
            
            return validation_result
            
        except Exception as e:
            logger.error(f"Model validation failed: {e}")
            return ModelValidationResult(
                is_valid=False,
                model_format="unknown",
                model_size_mb=0.0,
                validation_errors=[str(e)]
            )
    
    async def _validate_jit_model(self, model_path: str, result: ModelValidationResult):
        """Validate JIT/TorchScript model"""
        if not torch_available:
            result.validation_errors.append("PyTorch not available for JIT validation")
            result.is_valid = False
            return
        
        try:
            # Load JIT model
            model = torch.jit.load(model_path, map_location='cpu')
            
            # Test inference
            dummy_input = torch.randn(1, 48)
            start_time = time.time()
            
            with torch.no_grad():
                output = model(dummy_input)
            
            inference_time = (time.time() - start_time) * 1000  # Convert to ms
            
            # Record shapes
            result.input_shapes = {"observation": list(dummy_input.shape)}
            result.output_shapes = {"action": list(output.shape)}
            result.inference_time_ms = inference_time
            
            logger.info(f"JIT model validation successful: {model_path}")
            
        except Exception as e:
            result.validation_errors.append(f"JIT validation error: {str(e)}")
            result.is_valid = False
    
    async def _validate_onnx_model(self, model_path: str, result: ModelValidationResult):
        """Validate ONNX model"""
        if not onnx_available:
            result.validation_errors.append("ONNX not available for validation")
            result.is_valid = False
            return
        
        try:
            # Load and check ONNX model
            model = onnx.load(model_path)
            onnx.checker.check_model(model)
            
            # Test with ONNX Runtime if available
            try:
                session = onnxruntime.InferenceSession(model_path)
                
                # Get input/output info
                input_info = session.get_inputs()[0]
                output_info = session.get_outputs()[0]
                
                result.input_shapes = {input_info.name: input_info.shape}
                result.output_shapes = {output_info.name: output_info.shape}
                
                # Test inference
                dummy_input = {input_info.name: [[0.0] * 48]}
                start_time = time.time()
                
                outputs = session.run(None, dummy_input)
                
                inference_time = (time.time() - start_time) * 1000
                result.inference_time_ms = inference_time
                
            except Exception as e:
                result.validation_errors.append(f"ONNX Runtime test failed: {str(e)}")
            
            logger.info(f"ONNX model validation successful: {model_path}")
            
        except Exception as e:
            result.validation_errors.append(f"ONNX validation error: {str(e)}")
            result.is_valid = False
    
    async def _register_exported_model(self, model_path: str, config: DeploymentConfig):
        """Register exported model in the model registry"""
        try:
            model_name = Path(model_path).stem
            
            model_info = ModelInfo(
                model_path=model_path,
                model_name=model_name,
                training_config=None,  # Would need to be passed or inferred
                creation_time=time.time(),
                file_size=os.path.getsize(model_path),
                model_format=config.export_format
            )
            
            # Calculate model hash for integrity
            model_hash = await self._calculate_model_hash(model_path)
            model_info.performance_metrics["model_hash"] = model_hash
            
            self.deployed_models[model_name] = model_info
            
            # Publish model info
            message = MessageFactory.create_data(model_info.to_dict(), "deployment_service")
            await self.topic_manager.publish_message(topics.MODEL_INFO, message)
            
        except Exception as e:
            logger.error(f"Failed to register exported model: {e}")
    
    async def _calculate_model_hash(self, model_path: str) -> str:
        """Calculate SHA256 hash of model file"""
        try:
            sha256_hash = hashlib.sha256()
            with open(model_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(chunk)
            return sha256_hash.hexdigest()
        except Exception:
            return ""
    
    async def _scan_available_models(self):
        """Scan for available models in the system"""
        try:
            # Scan legged_gym logs directory if available
            if legged_gym_available and LEGGED_GYM_ROOT_DIR:
                logs_dir = os.path.join(LEGGED_GYM_ROOT_DIR, 'logs')
                if os.path.exists(logs_dir):
                    await self._scan_directory_for_models(logs_dir)
            
            # Scan export directory
            if os.path.exists(self.config.export_base_path):
                await self._scan_directory_for_models(self.config.export_base_path)
                
        except Exception as e:
            logger.error(f"Error scanning for models: {e}")
    
    async def _scan_directory_for_models(self, directory: str):
        """Scan directory for model files"""
        try:
            for root, dirs, files in os.walk(directory):
                for file in files:
                    if file.endswith(('.pt', '.pth', '.onnx')):
                        model_path = os.path.join(root, file)
                        
                        # Create model info
                        model_info = ModelInfo(
                            model_path=model_path,
                            model_name=Path(file).stem,
                            training_config=None,
                            creation_time=os.path.getmtime(model_path),
                            file_size=os.path.getsize(model_path),
                            model_format=Path(file).suffix[1:]  # Remove dot
                        )
                        
                        self.available_models[model_info.model_name] = model_info
                        
        except Exception as e:
            logger.error(f"Error scanning directory {directory}: {e}")
    
    async def cancel_export_job(self, job_id: str) -> bool:
        """Cancel an active export job"""
        try:
            if job_id not in self.active_jobs:
                return False
            
            job_status = self.active_jobs[job_id]
            
            # Mark as cancelled (treat as error state)
            job_status.state = DeploymentServiceState.ERROR
            job_status.error_message = "Job cancelled by user"
            job_status.last_update = time.time()
            
            # Move to history
            self._move_job_to_history(job_id)
            
            logger.info(f"Export job {job_id} cancelled")
            return True
            
        except Exception as e:
            logger.error(f"Error cancelling job {job_id}: {e}")
            return False
    
    def _move_job_to_history(self, job_id: str):
        """Move completed job to history"""
        if job_id in self.active_jobs:
            job = self.active_jobs.pop(job_id)
            self.job_history.append(job)
            
            # Maintain history size limit
            if len(self.job_history) > self.max_history_size:
                self.job_history.pop(0)
    
    async def _start_background_tasks(self):
        """Start background monitoring tasks"""
        self.status_task = asyncio.create_task(self._status_publishing_loop())
        self.job_monitor_task = asyncio.create_task(self._job_monitoring_loop())
        logger.debug("Background tasks started")
    
    async def _status_publishing_loop(self):
        """Publish deployment service status periodically"""
        try:
            while self.is_initialized:
                status = {
                    "state": self.state.value,
                    "active_jobs": len(self.active_jobs),
                    "available_models": len(self.available_models),
                    "deployed_models": len(self.deployed_models),
                    "timestamp": time.time()
                }
                
                message = MessageFactory.create_status(status, "deployment_service")
                await self.topic_manager.publish_message(topics.DEPLOY_STATUS, message)
                
                await asyncio.sleep(5.0)  # Publish every 5 seconds
                
        except asyncio.CancelledError:
            logger.debug("Status publishing loop cancelled")
        except Exception as e:
            logger.error(f"Error in status publishing loop: {e}")
    
    async def _job_monitoring_loop(self):
        """Monitor active jobs for timeouts and cleanup"""
        try:
            while self.is_initialized:
                current_time = time.time()
                
                # Check for job timeouts
                timed_out_jobs = []
                for job_id, job_status in self.active_jobs.items():
                    job_duration = current_time - job_status.start_time
                    
                    if job_duration > self.config.export_timeout:
                        timed_out_jobs.append(job_id)
                
                # Handle timed out jobs
                for job_id in timed_out_jobs:
                    job_status = self.active_jobs[job_id]
                    job_status.state = DeploymentServiceState.ERROR
                    job_status.error_message = "Job timed out"
                    job_status.last_update = current_time
                    
                    self._move_job_to_history(job_id)
                    logger.warning(f"Export job {job_id} timed out")
                
                await asyncio.sleep(30.0)  # Check every 30 seconds
                
        except asyncio.CancelledError:
            logger.debug("Job monitoring loop cancelled")
        except Exception as e:
            logger.error(f"Error in job monitoring loop: {e}")
    
    async def _publish_export_completion(self, job_id: str, export_path: str):
        """Publish export completion notification"""
        status = {
            "job_id": job_id,
            "status": "completed",
            "export_path": export_path,
            "timestamp": time.time()
        }
        
        message = MessageFactory.create_status(status, "deployment_service")
        await self.topic_manager.publish_message(topics.DEPLOYMENT_STATUS, message)
    
    async def _publish_export_error(self, job_id: str, error_message: str):
        """Publish export error notification"""
        status = {
            "job_id": job_id,
            "status": "error",
            "error_message": error_message,
            "timestamp": time.time()
        }
        
        message = MessageFactory.create_status(status, "deployment_service")
        await self.topic_manager.publish_message(topics.DEPLOYMENT_STATUS, message)
    
    async def _send_deployment_response(self, success: bool, message: str, data: Dict[str, Any] = None):
        """Send deployment response"""
        try:
            response = ConfigurationResponse(
                success=success,
                config_type="deployment",
                config_data=data or {},
                error_message=message if not success else "",
                timestamp=time.time()
            )
            
            response_message = MessageFactory.create_status(response.to_dict(), "deployment_service")
            await self.topic_manager.publish_message(topics.DEPLOYMENT_STATUS, response_message)
            
        except Exception as e:
            logger.error(f"Failed to send deployment response: {e}")
    
    async def get_status(self) -> Dict[str, Any]:
        """Get current deployment service status"""
        return {
            "state": self.state.value,
            "is_initialized": self.is_initialized,
            "active_jobs": {k: v.to_dict() for k, v in self.active_jobs.items()},
            "available_models_count": len(self.available_models),
            "deployed_models_count": len(self.deployed_models),
            "job_history_count": len(self.job_history),
            "available_models": {k: v.to_dict() for k, v in self.available_models.items()},
            "deployed_models": {k: v.to_dict() for k, v in self.deployed_models.items()},
            "config": {
                "export_formats_available": ["jit"] + (["onnx"] if onnx_available else []),
                "torch_available": torch_available,
                "onnx_available": onnx_available,
                "legged_gym_available": legged_gym_available
            }
        }
    
    async def get_job_status(self, job_id: str) -> Optional[ExportJobStatus]:
        """Get status of a specific job"""
        if job_id in self.active_jobs:
            return self.active_jobs[job_id]
        
        # Check history
        for job in self.job_history:
            if job.job_id == job_id:
                return job
        
        return None
    
    async def get_model_list(self) -> Dict[str, List[ModelInfo]]:
        """Get list of available and deployed models"""
        await self._scan_available_models()  # Refresh list
        
        return {
            "available": list(self.available_models.values()),
            "deployed": list(self.deployed_models.values())
        }
    
    async def shutdown(self):
        """Shutdown the deployment service"""
        logger.info("Shutting down deployment service...")
        
        self.is_initialized = False
        
        # Cancel all active jobs
        for job_id in list(self.active_jobs.keys()):
            await self.cancel_export_job(job_id)
        
        # Cancel background tasks
        if self.status_task:
            self.status_task.cancel()
        if self.job_monitor_task:
            self.job_monitor_task.cancel()
        
        # Wait for tasks to complete
        await asyncio.sleep(0.1)
        
        self.state = DeploymentServiceState.IDLE
        logger.info("Deployment service shutdown complete")


# Factory function for creating deployment service
async def create_deployment_service(
    session_manager: EnhancedZenohSessionManager,
    config: Optional[DeploymentServiceConfig] = None
) -> DeploymentService:
    """Create and initialize a deployment service"""
    service = DeploymentService(session_manager, config)
    
    if await service.initialize():
        return service
    else:
        raise RuntimeError("Failed to initialize deployment service")