"""
Play Service for Legged Robot Gymnasium
Encapsulates testing/simulation logic as an async service with Zenoh integration
"""

import asyncio
import logging
import time
import os
import cv2
import numpy as np
from typing import Optional, Dict, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import threading

# Import Isaac Gym first to avoid PyTorch import order issues
try:
    import isaacgym
    from isaacgym import gymapi
    from isaacgym.torch_utils import *
except ImportError:
    pass

import torch

from zenoh_services.core.enhanced_session_manager import EnhancedZenohSessionManager
from zenoh_services.core.topic_manager import TopicManager
from zenoh_services.core.data_models import (
    PlayConfig, PlayState, RobotState, RobotCommand, SerializationMixin
)
from zenoh_services.core.message_format import MessageFactory, MessageType, Priority
from zenoh_services.core import topics

# Import legged_gym components
from legged_gym.envs import *
from legged_gym.utils import get_args, export_policy_as_jit, task_registry, Logger
from legged_gym import LEGGED_GYM_ROOT_DIR

logger = logging.getLogger(__name__)


class PlayServiceState(Enum):
    """Play service states"""
    IDLE = "idle"
    INITIALIZING = "initializing"
    RUNNING = "running"
    PAUSED = "paused"
    STOPPING = "stopping"
    ERROR = "error"


@dataclass
class PlayServiceConfig:
    """Configuration for play service"""
    # Simulation parameters
    task_name: str = "anymal_c_flat"
    num_envs: int = 50
    render: bool = False
    record_video: bool = False
    export_policy: bool = True
    
    # Control parameters
    use_joystick: bool = False
    fix_command: bool = False
    robot_index: int = 0  # which robot to control/monitor
    
    # Real-time data publishing
    robot_state_publish_interval: float = 0.1  # 10 Hz
    status_publish_interval: float = 1.0       # 1 Hz
    
    # Recording parameters
    video_fps: float = 50.0
    video_resolution: tuple = (1920, 1080)
    log_states: bool = True
    stop_state_log: int = 1000
    
    # Model parameters
    checkpoint: int = -1
    model_path: Optional[str] = None
    
    # Environment parameters
    terrain_type: str = "plane"  # or "trimesh"
    terrain_size: tuple = (5, 5)  # (rows, cols)
    disable_noise: bool = True
    disable_randomization: bool = True


@dataclass
class RobotStateData(SerializationMixin):
    """Extended robot state data for detailed monitoring"""
    # Joint states
    joint_positions: list = field(default_factory=list)
    joint_velocities: list = field(default_factory=list)
    joint_torques: list = field(default_factory=list)
    joint_targets: list = field(default_factory=list)
    
    # Base states
    base_position: list = field(default_factory=list)
    base_orientation: list = field(default_factory=list)
    base_linear_velocity: list = field(default_factory=list)
    base_angular_velocity: list = field(default_factory=list)
    
    # Commands
    velocity_command: list = field(default_factory=list)  # [x, y, yaw]
    
    # Contact information
    contact_forces: list = field(default_factory=list)
    contact_states: list = field(default_factory=list)
    
    # Environment info
    env_index: int = 0
    timestamp: float = 0.0


class PlayService:
    """
    Async play service that wraps legged_gym testing/simulation with Zenoh communication
    """
    
    def __init__(self, session_manager: EnhancedZenohSessionManager, config: PlayServiceConfig = None):
        self.session_manager = session_manager
        self.topic_manager = TopicManager(session_manager)
        self.config = config or PlayServiceConfig()
        
        # Service state
        self.state = PlayServiceState.IDLE
        self.is_initialized = False
        self.simulation_task: Optional[asyncio.Task] = None
        self.publishing_task: Optional[asyncio.Task] = None
        self.status_task: Optional[asyncio.Task] = None
        
        # Environment components
        self.env = None
        self.env_cfg = None
        self.train_cfg = None
        self.policy = None
        self.logger = None
        
        # Video recording
        self.video_writer = None
        self.camera_handle = None
        
        # Control state
        self.velocity_commands = [0.0, 0.0, 0.0, 0.0]  # [x, y, yaw, height]
        self.is_running = False
        self.pause_requested = False
        self.stop_requested = False
        
        # Robot state tracking
        self.current_robot_state: Optional[RobotStateData] = None
        self.robot_index = self.config.robot_index
        
        # Joystick support
        self.joystick = None
        self.joystick_thread = None
        self.joystick_running = False
        
    async def initialize(self) -> bool:
        """Initialize the play service"""
        try:
            self.state = PlayServiceState.INITIALIZING
            logger.info("Initializing play service...")
            
            # Register topics
            await self._register_topics()
            
            # Setup command subscribers
            await self._setup_command_subscribers()
            
            # Initialize joystick if enabled
            if self.config.use_joystick:
                self._initialize_joystick()
            
            self.is_initialized = True
            self.state = PlayServiceState.IDLE
            logger.info("Play service initialized successfully")
            return True
            
        except Exception as e:
            self.state = PlayServiceState.ERROR
            logger.error(f"Failed to initialize play service: {e}")
            return False
    
    async def _register_topics(self):
        """Register all play-related topics"""
        topics_to_register = [
            topics.ROBOT_COMMAND,
            topics.ROBOT_STATE,
            topics.PLAY_STATUS,
            topics.ROBOT_CONTROL_COMMAND
        ]
        
        for topic in topics_to_register:
            await self.topic_manager.register_topic(topic)
            await self.topic_manager.create_publisher(topic)
    
    async def _setup_command_subscribers(self):
        """Setup subscribers for robot commands"""
        await self.topic_manager.create_subscriber(
            topics.ROBOT_CONTROL_COMMAND, 
            self._handle_robot_command
        )
    
    async def _handle_robot_command(self, message):
        """Handle incoming robot control commands"""
        try:
            if hasattr(message, 'payload'):
                command_data = message.payload
            else:
                command_data = message
                
            # Update velocity commands
            if 'velocity' in command_data:
                vel = command_data['velocity']
                self.velocity_commands[0] = vel.get('x', 0.0)
                self.velocity_commands[1] = vel.get('y', 0.0)
                self.velocity_commands[2] = vel.get('yaw', 0.0)
                self.velocity_commands[3] = vel.get('height', 0.0)
                
            logger.debug(f"Updated velocity commands: {self.velocity_commands}")
            
        except Exception as e:
            logger.error(f"Error handling robot command: {e}")
    
    def _initialize_joystick(self):
        """Initialize joystick support"""
        try:
            import pygame
            pygame.init()
            
            if pygame.joystick.get_count() > 0:
                self.joystick = pygame.joystick.Joystick(0)
                self.joystick.init()
                logger.info(f"Joystick initialized: {self.joystick.get_name()}")
            else:
                logger.warning("No joystick found")
                self.config.use_joystick = False
                
        except ImportError:
            logger.warning("pygame not available, disabling joystick support")
            self.config.use_joystick = False
        except Exception as e:
            logger.error(f"Failed to initialize joystick: {e}")
            self.config.use_joystick = False
    
    async def start_simulation(self, config: Optional[PlayConfig] = None) -> bool:
        """Start simulation/testing"""
        if self.state != PlayServiceState.IDLE:
            logger.warning(f"Cannot start simulation in state {self.state}")
            return False
        
        try:
            # Apply config updates if provided
            if config:
                self._update_config(config)
            
            # Initialize environment if needed
            if not self.env:
                await self._initialize_environment()
            
            # Load policy if needed
            if not self.policy:
                await self._load_policy()
            
            # Setup video recording if enabled
            if self.config.record_video:
                await self._setup_video_recording()
            
            # Start simulation task
            self.state = PlayServiceState.RUNNING
            self.is_running = True
            self.stop_requested = False
            self.pause_requested = False
            
            self.simulation_task = asyncio.create_task(self._simulation_loop())
            self.publishing_task = asyncio.create_task(self._robot_state_publishing_loop())
            self.status_task = asyncio.create_task(self._status_publishing_loop())
            
            # Start joystick thread if enabled
            if self.config.use_joystick and self.joystick:
                self._start_joystick_thread()
            
            # Publish simulation started status
            await self._publish_status("Simulation started")
            
            logger.info("Simulation started successfully")
            return True
            
        except Exception as e:
            self.state = PlayServiceState.ERROR
            logger.error(f"Failed to start simulation: {e}")
            await self._publish_status(f"Simulation failed to start: {e}")
            return False
    
    async def stop_simulation(self) -> bool:
        """Stop simulation"""
        if self.state not in [PlayServiceState.RUNNING, PlayServiceState.PAUSED]:
            logger.warning(f"Cannot stop simulation in state {self.state}")
            return False
        
        try:
            self.state = PlayServiceState.STOPPING
            self.stop_requested = True
            self.is_running = False
            
            # Stop joystick thread
            self._stop_joystick_thread()
            
            # Cancel background tasks
            tasks = [self.simulation_task, self.publishing_task, self.status_task]
            for task in tasks:
                if task and not task.done():
                    task.cancel()
            
            # Wait for tasks to complete
            await asyncio.sleep(0.1)
            
            # Close video recording
            if self.video_writer:
                self.video_writer.release()
                self.video_writer = None
            
            self.state = PlayServiceState.IDLE
            await self._publish_status("Simulation stopped")
            
            logger.info("Simulation stopped successfully")
            return True
            
        except Exception as e:
            self.state = PlayServiceState.ERROR
            logger.error(f"Failed to stop simulation: {e}")
            return False
    
    async def pause_simulation(self) -> bool:
        """Pause simulation"""
        if self.state != PlayServiceState.RUNNING:
            logger.warning(f"Cannot pause simulation in state {self.state}")
            return False
        
        self.pause_requested = True
        self.state = PlayServiceState.PAUSED
        await self._publish_status("Simulation paused")
        
        logger.info("Simulation paused")
        return True
    
    async def resume_simulation(self) -> bool:
        """Resume paused simulation"""
        if self.state != PlayServiceState.PAUSED:
            logger.warning(f"Cannot resume simulation in state {self.state}")
            return False
        
        self.pause_requested = False
        self.state = PlayServiceState.RUNNING
        await self._publish_status("Simulation resumed")
        
        logger.info("Simulation resumed")
        return True
    
    def _update_config(self, config: PlayConfig):
        """Update service configuration with play config"""
        self.config.task_name = config.task_name or self.config.task_name
        self.config.num_envs = config.num_envs or self.config.num_envs
        self.config.render = config.render if config.render is not None else self.config.render
        self.config.record_video = config.record_video if config.record_video is not None else self.config.record_video
    
    async def _initialize_environment(self):
        """Initialize simulation environment"""
        try:
            # Create mock args object
            class MockArgs:
                def __init__(self, config: PlayServiceConfig):
                    self.task = config.task_name
                    self.num_envs = config.num_envs
                    self.seed = -1
                    self.checkpoint = config.checkpoint
                    self.headless = not config.render
                    self.run_name = "play_service"
            
            args = MockArgs(self.config)
            
            # Get configs and modify for testing
            self.env_cfg, self.train_cfg = task_registry.get_cfgs(name=args.task)
            
            # Override parameters for testing
            self.env_cfg.env.num_envs = min(self.env_cfg.env.num_envs, self.config.num_envs)
            self.env_cfg.terrain.mesh_type = self.config.terrain_type
            self.env_cfg.terrain.num_rows = self.config.terrain_size[0]
            self.env_cfg.terrain.num_cols = self.config.terrain_size[1]
            
            if self.config.disable_noise:
                self.env_cfg.noise.add_noise = False
                self.env_cfg.noise.curriculum = False
                
            if self.config.disable_randomization:
                # Disable all domain randomization
                for attr in dir(self.env_cfg.domain_rand):
                    if not attr.startswith('_') and hasattr(getattr(self.env_cfg.domain_rand, attr), '__class__'):
                        if isinstance(getattr(self.env_cfg.domain_rand, attr), bool):
                            setattr(self.env_cfg.domain_rand, attr, False)
            
            # Create environment
            self.env, _ = task_registry.make_env(name=args.task, args=args, env_cfg=self.env_cfg)
            
            if self.config.render:
                self.env.set_camera(self.env_cfg.viewer.pos, self.env_cfg.viewer.lookat)
            
            # Initialize logger for state tracking
            if self.config.log_states:
                self.logger = Logger(self.env_cfg.sim.dt)
            
            logger.info(f"Environment initialized for task: {args.task}")
            
        except Exception as e:
            logger.error(f"Failed to initialize environment: {e}")
            raise
    
    async def _load_policy(self):
        """Load trained policy for inference"""
        try:
            # Set resume flag to load trained model
            self.train_cfg.runner.resume = True
            
            # Create mock args
            class MockArgs:
                def __init__(self, config: PlayServiceConfig):
                    self.task = config.task_name
                    self.checkpoint = config.checkpoint
                    self.run_name = "play_service"
            
            args = MockArgs(self.config)
            
            # Create PPO runner and get inference policy
            ppo_runner, _, _ = task_registry.make_alg_runner(
                env=self.env, 
                name=args.task, 
                args=args, 
                train_cfg=self.train_cfg
            )
            
            self.policy = ppo_runner.get_inference_policy(device=self.env.device)
            
            # Export policy if requested
            if self.config.export_policy:
                current_date_str = datetime.now().strftime('%Y-%m-%d')
                export_path = os.path.join(
                    LEGGED_GYM_ROOT_DIR, 'logs', 
                    self.train_cfg.runner.experiment_name, 
                    '0_exported', 'policies'
                )
                export_policy_as_jit(ppo_runner.alg.actor_critic, export_path)
                logger.info(f'Exported policy to: {export_path}')
            
            logger.info("Policy loaded successfully")
            
        except Exception as e:
            logger.error(f"Failed to load policy: {e}")
            raise
    
    async def _setup_video_recording(self):
        """Setup video recording"""
        try:
            if not self.config.render:
                logger.warning("Video recording requires rendering to be enabled")
                return
            
            # Setup camera
            camera_properties = gymapi.CameraProperties()
            camera_properties.width = self.config.video_resolution[0]
            camera_properties.height = self.config.video_resolution[1]
            
            self.camera_handle = self.env.gym.create_camera_sensor(self.env.envs[0], camera_properties)
            
            # Attach camera to robot
            camera_offset = gymapi.Vec3(1, -1, 0.5)
            camera_rotation = gymapi.Quat.from_axis_angle(
                gymapi.Vec3(-0.3, 0.2, 1), np.deg2rad(135)
            )
            
            actor_handle = self.env.gym.get_actor_handle(self.env.envs[0], 0)
            body_handle = self.env.gym.get_actor_rigid_body_handle(self.env.envs[0], actor_handle, 0)
            
            self.env.gym.attach_camera_to_body(
                self.camera_handle, self.env.envs[0], body_handle,
                gymapi.Transform(camera_offset, camera_rotation),
                gymapi.FOLLOW_POSITION
            )
            
            # Setup video writer
            fourcc = cv2.VideoWriter_fourcc(*"mp4v")
            video_dir = os.path.join(LEGGED_GYM_ROOT_DIR, 'videos')
            experiment_dir = os.path.join(video_dir, self.train_cfg.runner.experiment_name)
            
            os.makedirs(experiment_dir, exist_ok=True)
            
            video_path = os.path.join(
                experiment_dir, 
                datetime.now().strftime('%b%d_%H-%M-%S') + '_play_service.mp4'
            )
            
            self.video_writer = cv2.VideoWriter(
                video_path, fourcc, self.config.video_fps, self.config.video_resolution
            )
            
            logger.info(f"Video recording setup: {video_path}")
            
        except Exception as e:
            logger.error(f"Failed to setup video recording: {e}")
            self.config.record_video = False
    
    def _start_joystick_thread(self):
        """Start joystick input thread"""
        if not self.config.use_joystick or not self.joystick:
            return
            
        self.joystick_running = True
        self.joystick_thread = threading.Thread(target=self._joystick_input_loop)
        self.joystick_thread.daemon = True
        self.joystick_thread.start()
        logger.info("Joystick input thread started")
    
    def _stop_joystick_thread(self):
        """Stop joystick input thread"""
        if self.joystick_thread:
            self.joystick_running = False
            self.joystick_thread.join(timeout=1.0)
            logger.info("Joystick input thread stopped")
    
    def _joystick_input_loop(self):
        """Joystick input loop (runs in separate thread)"""
        import pygame
        
        x_vel_max, y_vel_max, yaw_vel_max = 1.5, 1.0, 3.0
        
        while self.joystick_running:
            try:
                pygame.event.get()  # Process pygame events
                
                if self.joystick:
                    # Read joystick axes
                    x_vel = -self.joystick.get_axis(1) * x_vel_max
                    y_vel = -self.joystick.get_axis(0) * y_vel_max
                    yaw_vel = -self.joystick.get_axis(3) * yaw_vel_max
                    
                    # Update velocity commands
                    self.velocity_commands[0] = x_vel
                    self.velocity_commands[1] = y_vel
                    self.velocity_commands[2] = yaw_vel
                
                time.sleep(0.1)  # 10 Hz update rate
                
            except Exception as e:
                logger.error(f"Error in joystick input loop: {e}")
                break
    
    async def _simulation_loop(self):
        """Main simulation loop"""
        try:
            obs = self.env.get_observations()
            iteration = 0
            
            while self.is_running and not self.stop_requested:
                # Handle pause
                while self.pause_requested and not self.stop_requested:
                    await asyncio.sleep(0.1)
                
                if self.stop_requested:
                    break
                
                # Run simulation step in executor to avoid blocking
                loop = asyncio.get_event_loop()
                obs = await loop.run_in_executor(None, self._run_simulation_step, obs, iteration)
                
                iteration += 1
                
                # Small delay to allow other tasks
                await asyncio.sleep(0.001)  # 1ms
            
            logger.info("Simulation loop completed")
            
        except asyncio.CancelledError:
            logger.info("Simulation loop cancelled")
            raise
        except Exception as e:
            self.state = PlayServiceState.ERROR
            logger.error(f"Error in simulation loop: {e}")
            await self._publish_status(f"Simulation error: {e}")
    
    def _run_simulation_step(self, obs, iteration: int):
        """Run single simulation step (synchronous)"""
        # Get policy actions
        with torch.no_grad():
            actions = self.policy(obs.detach())
        
        # Apply velocity commands
        if self.config.fix_command:
            # Fixed command for testing
            self.env.commands[:, 0] = 0.5
            self.env.commands[:, 1] = 0.0
            self.env.commands[:, 2] = 0.0
            self.env.commands[:, 3] = 0.0
        else:
            # Use joystick or external commands
            self.env.commands[:, 0] = self.velocity_commands[0]
            self.env.commands[:, 1] = self.velocity_commands[1]
            self.env.commands[:, 2] = self.velocity_commands[2]
            self.env.commands[:, 3] = self.velocity_commands[3]
        
        # Step environment
        obs, critic_obs, rews, dones, infos = self.env.step(actions.detach())
        
        # Update robot state tracking
        self._update_robot_state(actions, iteration)
        
        # Handle rendering and video recording
        if self.config.render:
            self.env.gym.fetch_results(self.env.sim, True)
            self.env.gym.step_graphics(self.env.sim)
            
            if self.config.record_video and self.camera_handle and self.video_writer:
                self.env.gym.render_all_camera_sensors(self.env.sim)
                img = self.env.gym.get_camera_image(
                    self.env.sim, self.env.envs[0], self.camera_handle, gymapi.IMAGE_COLOR
                )
                img = np.reshape(img, (*self.config.video_resolution, 4))
                img = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)
                self.video_writer.write(img[..., :3])
        
        # Log states if enabled
        if self.config.log_states and self.logger and iteration < self.config.stop_state_log:
            self._log_robot_states(actions, iteration)
        elif iteration == self.config.stop_state_log and self.logger:
            self.logger.plot_states()
        
        return obs
    
    def _update_robot_state(self, actions, iteration: int):
        """Update current robot state data"""
        try:
            robot_idx = self.robot_index
            
            # Extract robot state data
            self.current_robot_state = RobotStateData(
                # Joint states
                joint_positions=self.env.dof_pos[robot_idx].cpu().numpy().tolist(),
                joint_velocities=self.env.dof_vel[robot_idx].cpu().numpy().tolist(),
                joint_torques=self.env.torques[robot_idx].cpu().numpy().tolist(),
                joint_targets=(actions[robot_idx] * self.env.cfg.control.action_scale).cpu().numpy().tolist(),
                
                # Base states
                base_position=self.env.root_states[robot_idx, :3].cpu().numpy().tolist(),
                base_orientation=self.env.root_states[robot_idx, 3:7].cpu().numpy().tolist(),
                base_linear_velocity=self.env.base_lin_vel[robot_idx].cpu().numpy().tolist(),
                base_angular_velocity=self.env.base_ang_vel[robot_idx].cpu().numpy().tolist(),
                
                # Commands
                velocity_command=self.env.commands[robot_idx, :3].cpu().numpy().tolist(),
                
                # Contact information
                contact_forces=self.env.contact_forces[robot_idx, self.env.feet_indices].cpu().numpy().tolist(),
                contact_states=[1 if f[2] > 1.0 else 0 for f in self.env.contact_forces[robot_idx, self.env.feet_indices]],
                
                # Environment info
                env_index=robot_idx,
                timestamp=time.time()
            )
            
        except Exception as e:
            logger.error(f"Error updating robot state: {e}")
    
    def _log_robot_states(self, actions, iteration: int):
        """Log detailed robot states for analysis"""
        robot_idx = self.robot_index
        
        log_data = {
            'iteration': iteration,
            'timestamp': time.time(),
            
            # Joint positions
            **{f'dof_pos[{i}]': self.env.dof_pos[robot_idx, i].item() 
               for i in range(len(self.env.dof_pos[robot_idx]))},
            
            # Joint targets
            **{f'dof_target[{i}]': (actions[robot_idx, i] * self.env.cfg.control.action_scale).item() 
               for i in range(len(actions[robot_idx]))},
            
            # Joint torques
            **{f'dof_torque[{i}]': self.env.torques[robot_idx, i].item() 
               for i in range(len(self.env.torques[robot_idx]))},
            
            # Joint velocities
            **{f'dof_vel[{i}]': self.env.dof_vel[robot_idx, i].item() 
               for i in range(len(self.env.dof_vel[robot_idx]))},
            
            # Commands
            'command_x': self.env.commands[robot_idx, 0].item(),
            'command_y': self.env.commands[robot_idx, 1].item(),
            'command_yaw': self.env.commands[robot_idx, 2].item(),
            
            # Base velocities
            'base_vel_x': self.env.base_lin_vel[robot_idx, 0].item(),
            'base_vel_y': self.env.base_lin_vel[robot_idx, 1].item(),
            'base_vel_z': self.env.base_lin_vel[robot_idx, 2].item(),
            'base_vel_yaw': self.env.base_ang_vel[robot_idx, 2].item(),
            
            # Contact forces
            'contact_forces_z': self.env.contact_forces[robot_idx, self.env.feet_indices, 2].cpu().numpy()
        }
        
        self.logger.log_states(log_data)
    
    async def _robot_state_publishing_loop(self):
        """Publish robot state data periodically"""
        try:
            while self.state == PlayServiceState.RUNNING:
                if self.current_robot_state:
                    message = MessageFactory.create_data(
                        self.current_robot_state.to_dict(),
                        "play_service"
                    )
                    message.header.priority = Priority.HIGH
                    
                    await self.topic_manager.publish_message(topics.ROBOT_STATE, message)
                
                await asyncio.sleep(self.config.robot_state_publish_interval)
                
        except asyncio.CancelledError:
            logger.debug("Robot state publishing loop cancelled")
        except Exception as e:
            logger.error(f"Error in robot state publishing loop: {e}")
    
    async def _status_publishing_loop(self):
        """Publish play status periodically"""
        try:
            while self.state in [PlayServiceState.RUNNING, PlayServiceState.PAUSED]:
                status = {
                    "state": self.state.value,
                    "is_running": self.is_running,
                    "config": {
                        "task_name": self.config.task_name,
                        "num_envs": self.config.num_envs,
                        "robot_index": self.robot_index,
                        "render": self.config.render,
                        "record_video": self.config.record_video
                    },
                    "velocity_commands": self.velocity_commands,
                    "timestamp": time.time()
                }
                
                message = MessageFactory.create_status(status, "play_service")
                await self.topic_manager.publish_message(topics.PLAY_STATUS, message)
                
                await asyncio.sleep(self.config.status_publish_interval)
                
        except asyncio.CancelledError:
            logger.debug("Status publishing loop cancelled")
        except Exception as e:
            logger.error(f"Error in status publishing loop: {e}")
    
    async def _publish_status(self, message_text: str):
        """Publish a status message"""
        status = {
            "state": self.state.value,
            "message": message_text,
            "timestamp": time.time()
        }
        
        status_msg = MessageFactory.create_status(status, "play_service")
        await self.topic_manager.publish_message(topics.PLAY_STATUS, status_msg)
    
    async def get_status(self) -> Dict[str, Any]:
        """Get current service status"""
        return {
            "state": self.state.value,
            "is_initialized": self.is_initialized,
            "is_running": self.is_running,
            "config": {
                "task_name": self.config.task_name,
                "num_envs": self.config.num_envs,
                "robot_index": self.robot_index,
                "render": self.config.render,
                "record_video": self.config.record_video,
                "use_joystick": self.config.use_joystick
            },
            "velocity_commands": self.velocity_commands,
            "robot_state": self.current_robot_state.to_dict() if self.current_robot_state else None
        }
    
    async def shutdown(self):
        """Shutdown the play service"""
        logger.info("Shutting down play service...")
        
        # Stop simulation if running
        if self.state == PlayServiceState.RUNNING:
            await self.stop_simulation()
        
        # Cancel all tasks
        tasks = [self.simulation_task, self.publishing_task, self.status_task]
        for task in tasks:
            if task and not task.done():
                task.cancel()
        
        # Stop joystick thread
        self._stop_joystick_thread()
        
        # Clean up resources
        if self.video_writer:
            self.video_writer.release()
        
        # Wait for tasks to complete
        await asyncio.sleep(0.1)
        
        self.state = PlayServiceState.IDLE
        logger.info("Play service shutdown complete")


# Factory function for creating play service
async def create_play_service(
    session_manager: EnhancedZenohSessionManager,
    config: Optional[PlayServiceConfig] = None
) -> PlayService:
    """Create and initialize a play service"""
    service = PlayService(session_manager, config)
    
    if await service.initialize():
        return service
    else:
        raise RuntimeError("Failed to initialize play service")