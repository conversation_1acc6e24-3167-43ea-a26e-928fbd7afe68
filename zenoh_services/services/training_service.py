"""
Training Service for Legged Robot Gymnasium
Encapsulates training logic as an async service with Zenoh integration
"""

import asyncio
import logging
import time
import os
import threading
from typing import Optional, Dict, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime

# Import Isaac <PERSON> first to avoid PyTorch import order issues
try:
    import isaacgym
except ImportError:
    pass

import torch

from zenoh_services.core.enhanced_session_manager import EnhancedZenohSessionManager
from zenoh_services.core.topic_manager import TopicManager
from zenoh_services.core.data_models import (
    TrainingConfig, TrainingState, TrainingMetrics, TrainingStatus, TrainingCommand
)
from zenoh_services.core.message_format import MessageFactory, MessageType, Priority
from zenoh_services.core.serialization import Serializer
from zenoh_services.core import topics

# Import error handling system
from zenoh_services.core.error_handler import (
    <PERSON>rror<PERSON><PERSON><PERSON>, ErrorContext, ErrorCategory, ErrorSeverity, get_error_handler, handle_errors
)
from zenoh_services.core.fault_detection import get_fault_detection_system

# Import legged_gym components
from legged_gym.envs import *
from legged_gym.utils import get_args, task_registry
from rsl_rl.runners.on_policy_runner import OnPolicyRunner

# Import enhanced PPO runner
from .enhanced_ppo_runner import ZenohIntegratedPPORunner, create_enhanced_ppo_runner

logger = logging.getLogger(__name__)


class TrainingServiceState(Enum):
    """Training service states"""
    IDLE = "idle"
    INITIALIZING = "initializing"
    TRAINING = "training"
    PAUSED = "paused"
    STOPPING = "stopping"
    ERROR = "error"


@dataclass
class TrainingServiceConfig:
    """Configuration for training service"""
    # Training parameters
    task_name: str = "anymal_c_flat"
    num_envs: int = 4096
    max_iterations: int = 1500
    save_interval: int = 50
    
    # Service parameters
    metrics_publish_interval: float = 1.0  # seconds
    status_publish_interval: float = 5.0   # seconds
    checkpoint_publish_enabled: bool = True
    
    # Resource management
    device: str = "cuda"
    num_threads: int = 1
    
    # Logging
    experiment_name: str = "legged_gym_training"
    run_name: str = "default_run"
    log_dir: Optional[str] = None
    resume: bool = False
    checkpoint: int = -1


class TrainingService:
    """
    Async training service that wraps legged_gym training with Zenoh communication
    """
    
    def __init__(self, session_manager: EnhancedZenohSessionManager, config: TrainingServiceConfig = None):
        self.session_manager = session_manager
        self.topic_manager = TopicManager(session_manager)
        self.config = config or TrainingServiceConfig()
        
        # Error handling integration
        self.error_handler = get_error_handler()
        self.fault_detection = get_fault_detection_system()
        self.service_monitor = self.fault_detection.register_service("training_service")
        
        # Service state
        self.state = TrainingServiceState.IDLE
        self.is_initialized = False
        self.training_task: Optional[asyncio.Task] = None
        self.metrics_task: Optional[asyncio.Task] = None
        self.status_task: Optional[asyncio.Task] = None
        
        # Training components
        self.env = None
        self.ppo_runner: Optional[ZenohIntegratedPPORunner] = None
        self.train_cfg = None
        self.log_dir = None
        
        # Training state
        self.current_iteration = 0
        self.start_time = None
        self.pause_requested = False
        self.stop_requested = False
        
        # Metrics tracking
        self.latest_metrics: Optional[TrainingMetrics] = None
        self.training_status: Optional[TrainingStatus] = None
        
        # Command callback
        self.command_callback = None
        
        # Setup error handling callbacks
        self._setup_error_handling()
    
    def _setup_error_handling(self):
        """Setup error handling and monitoring"""
        # Add health checks
        self._setup_health_checks()
        
        # Add recovery actions
        self._setup_recovery_actions()
        
        # Register error callbacks
        self.error_handler.add_error_callback(self._on_error)
        self.error_handler.add_recovery_callback(self._on_recovery)
    
    def _setup_health_checks(self):
        """Setup health checks for training service"""
        from zenoh_services.core.fault_detection import HealthCheck
        
        # Check if training environment is responsive
        def check_env_health():
            try:
                return self.env is not None and hasattr(self.env, 'reset')
            except:
                return False
        
        # Check if PPO runner is healthy
        def check_ppo_health():
            try:
                return self.ppo_runner is not None and self.ppo_runner.alg is not None
            except:
                return False
        
        # Check GPU memory
        def check_gpu_memory():
            try:
                if torch.cuda.is_available():
                    memory_usage = torch.cuda.memory_allocated() / torch.cuda.max_memory_allocated()
                    return memory_usage < 0.9  # Less than 90% usage
                return True
            except:
                return False
        
        self.service_monitor.add_health_check(HealthCheck(
            name="env_health",
            check_function=check_env_health,
            interval=30.0
        ))
        
        self.service_monitor.add_health_check(HealthCheck(
            name="ppo_health", 
            check_function=check_ppo_health,
            interval=30.0
        ))
        
        self.service_monitor.add_health_check(HealthCheck(
            name="gpu_memory",
            check_function=check_gpu_memory,
            interval=60.0
        ))
    
    def _setup_recovery_actions(self):
        """Setup recovery actions for training service"""
        from zenoh_services.core.fault_detection import RecoveryAction as FaultRecoveryAction, FailureType
        
        # Recovery action for training restart
        def restart_training():
            try:
                if self.training_task and not self.training_task.done():
                    self.training_task.cancel()
                # Reset state
                self.state = TrainingServiceState.IDLE
                self.current_iteration = 0
                return True
            except:
                return False
        
        # Recovery action for environment reset
        def reset_environment():
            try:
                if self.env:
                    self.env.reset()
                return True
            except:
                return False
        
        # Recovery action for GPU memory cleanup
        def cleanup_gpu_memory():
            try:
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                    torch.cuda.synchronize()
                return True
            except:
                return False
        
        self.service_monitor.add_recovery_action(
            FailureType.UNRESPONSIVE,
            FaultRecoveryAction("restart_training", restart_training)
        )
        
        self.service_monitor.add_recovery_action(
            FailureType.RESOURCE_EXHAUSTION,
            FaultRecoveryAction("cleanup_gpu", cleanup_gpu_memory)
        )
        
        self.service_monitor.add_recovery_action(
            FailureType.CRASH,
            FaultRecoveryAction("reset_environment", reset_environment)
        )
    
    def _on_error(self, error_info):
        """Handle error notifications"""
        if error_info.context.service_name == "training_service":
            logger.warning(f"Training service error: {error_info.message}")
            # Update service state if critical
            if error_info.severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]:
                self.state = TrainingServiceState.ERROR
    
    def _on_recovery(self, service_name: str, recovery_action: str, success: bool):
        """Handle recovery notifications"""
        if service_name == "training_service":
            if success:
                logger.info(f"Training service recovery successful: {recovery_action}")
                if self.state == TrainingServiceState.ERROR:
                    self.state = TrainingServiceState.IDLE
            else:
                logger.error(f"Training service recovery failed: {recovery_action}")
    
    def _create_error_context(self, function_name: str, additional_data: Dict[str, Any] = None) -> ErrorContext:
        """Create error context for this service"""
        return ErrorContext(
            service_name="training_service",
            service_state=self.state.value,
            timestamp=time.time(),
            thread_id=threading.get_ident(),
            function_name=function_name,
            additional_data={
                "current_iteration": self.current_iteration,
                "session_manager": self.session_manager,
                **(additional_data or {})
            }
        )
        
    @handle_errors(
        category=ErrorCategory.SERVICE,
        severity=ErrorSeverity.HIGH,
        service_name="training_service"
    )
    async def initialize(self) -> bool:
        """Initialize the training service"""
        try:
            self.state = TrainingServiceState.INITIALIZING
            logger.info("Initializing training service...")
            
            # Register training topics
            await self._register_topics()
            
            # Setup command subscriber
            await self._setup_command_subscriber()
            
            # Start service monitoring
            await self.service_monitor.start_monitoring()
            
            self.is_initialized = True
            self.state = TrainingServiceState.IDLE
            logger.info("Training service initialized successfully")
            return True
            
        except Exception as e:
            self.state = TrainingServiceState.ERROR
            context = self._create_error_context("initialize")
            await self.error_handler.handle_error(e, context, ErrorCategory.SERVICE, ErrorSeverity.HIGH)
            logger.error(f"Failed to initialize training service: {e}")
            return False
    
    async def _register_topics(self):
        """Register all training-related topics"""
        topics_to_register = [
            topics.TRAINING_COMMAND,
            topics.TRAINING_STATUS,
            topics.TRAINING_METRICS,
            topics.TRAINING_CHECKPOINT
        ]
        
        for topic in topics_to_register:
            await self.topic_manager.register_topic(topic)
            await self.topic_manager.create_publisher(topic)
    
    async def _setup_command_subscriber(self):
        """Setup subscriber for training commands"""
        await self.topic_manager.create_subscriber(
            topics.TRAINING_COMMAND, 
            self._handle_training_command
        )
    
    async def _handle_training_command(self, message):
        """Handle incoming training commands"""
        try:
            if hasattr(message, 'payload'):
                command_data = message.payload
            else:
                command_data = message

            logger.info(f"Received training command: {command_data}")

            # Handle nested message format from web bridge
            if isinstance(command_data, dict) and 'command' in command_data:
                # Check if command is nested (from web bridge)
                inner_command = command_data.get('command')
                if isinstance(inner_command, dict):
                    command_str = inner_command.get('command', '')
                    config_data = inner_command.get('config')
                else:
                    command_str = command_data.get('command', '')
                    config_data = command_data.get('config')

                # Convert config dict to TrainingConfig if present
                config = None
                if config_data and isinstance(config_data, dict):
                    config = TrainingConfig.from_dict(config_data)
            else:
                # Try to parse as TrainingCommand
                command = TrainingCommand.from_dict(command_data)
                command_str = command.command
                config = command.config

            logger.info(f"Processing training command: {command_str}")

            if command_str == "start":
                await self.start_training(config)
            elif command_str == "stop":
                await self.stop_training()
            elif command_str == "pause":
                await self.pause_training()
            elif command_str == "resume":
                await self.resume_training()
            elif command_str == "save":
                await self.save_checkpoint()
            else:
                logger.warning(f"Unknown training command: {command_str}")

        except Exception as e:
            logger.error(f"Error handling training command: {e}")
            import traceback
            traceback.print_exc()
    
    async def start_training(self, config: Optional[TrainingConfig] = None) -> bool:
        """Start training process"""
        if self.state not in [TrainingServiceState.IDLE, TrainingServiceState.PAUSED]:
            logger.warning(f"Cannot start training in state {self.state}")
            return False
        
        try:
            # Apply config updates if provided
            if config:
                self._update_config(config)
            
            # Initialize training environment if needed
            if not self.ppo_runner:
                await self._initialize_training_environment()
            
            # Start training task
            self.state = TrainingServiceState.TRAINING
            self.start_time = time.time()
            self.stop_requested = False
            self.pause_requested = False
            
            self.training_task = asyncio.create_task(self._training_loop())
            self.metrics_task = asyncio.create_task(self._metrics_publishing_loop())
            self.status_task = asyncio.create_task(self._status_publishing_loop())
            
            # Publish training started status
            await self._publish_status("Training started")
            
            logger.info("Training started successfully")
            return True
            
        except Exception as e:
            self.state = TrainingServiceState.ERROR
            logger.error(f"Failed to start training: {e}")
            await self._publish_status(f"Training failed to start: {e}")
            return False
    
    async def stop_training(self) -> bool:
        """Stop training process"""
        if self.state != TrainingServiceState.TRAINING:
            logger.warning(f"Cannot stop training in state {self.state}")
            return False
        
        try:
            self.state = TrainingServiceState.STOPPING
            self.stop_requested = True
            
            # Cancel background tasks
            if self.training_task:
                self.training_task.cancel()
            if self.metrics_task:
                self.metrics_task.cancel()
            if self.status_task:
                self.status_task.cancel()
            
            # Wait for tasks to complete
            await asyncio.sleep(0.1)
            
            self.state = TrainingServiceState.IDLE
            await self._publish_status("Training stopped")
            
            logger.info("Training stopped successfully")
            return True
            
        except Exception as e:
            self.state = TrainingServiceState.ERROR
            logger.error(f"Failed to stop training: {e}")
            return False
    
    async def pause_training(self) -> bool:
        """Pause training process"""
        if self.state != TrainingServiceState.TRAINING:
            logger.warning(f"Cannot pause training in state {self.state}")
            return False
        
        self.pause_requested = True
        self.state = TrainingServiceState.PAUSED
        await self._publish_status("Training paused")
        
        logger.info("Training paused")
        return True
    
    async def resume_training(self) -> bool:
        """Resume paused training"""
        if self.state != TrainingServiceState.PAUSED:
            logger.warning(f"Cannot resume training in state {self.state}")
            return False
        
        self.pause_requested = False
        self.state = TrainingServiceState.TRAINING
        await self._publish_status("Training resumed")
        
        logger.info("Training resumed")
        return True
    
    async def save_checkpoint(self) -> bool:
        """Save training checkpoint"""
        if not self.ppo_runner:
            logger.warning("No training session to save")
            return False
        
        try:
            # Save checkpoint using PPO runner
            checkpoint_path = os.path.join(
                self.log_dir, 
                f"model_{self.current_iteration}.pt"
            )
            
            # This is a synchronous operation, run in executor
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None, 
                lambda: self.ppo_runner.save(checkpoint_path)
            )
            
            # Publish checkpoint notification
            if self.config.checkpoint_publish_enabled:
                checkpoint_info = {
                    "iteration": self.current_iteration,
                    "path": checkpoint_path,
                    "timestamp": time.time()
                }
                message = MessageFactory.create_data(checkpoint_info, "training_service")
                await self.topic_manager.publish_message(topics.TRAINING_CHECKPOINT, message)
            
            logger.info(f"Checkpoint saved: {checkpoint_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save checkpoint: {e}")
            return False
    
    def _update_config(self, config: TrainingConfig):
        """Update service configuration with training config"""
        self.config.task_name = config.task_name or self.config.task_name
        self.config.num_envs = config.num_envs or self.config.num_envs
        self.config.max_iterations = config.max_iterations or self.config.max_iterations
        self.config.experiment_name = config.experiment_name or self.config.experiment_name
        self.config.run_name = config.run_name or self.config.run_name
    
    async def _initialize_training_environment(self):
        """Initialize training environment and PPO runner"""
        try:
            # Create mock args object for compatibility with existing code
            class MockArgs:
                def __init__(self, config: TrainingServiceConfig):
                    self.task = config.task_name
                    self.resume = config.resume
                    self.experiment_name = config.experiment_name
                    self.run_name = config.run_name
                    self.load_run = -1
                    self.checkpoint = config.checkpoint
                    self.num_envs = config.num_envs
                    self.seed = -1
                    self.max_iterations = config.max_iterations
            
            args = MockArgs(self.config)
            
            # Create environment and enhanced PPO runner with Zenoh integration
            self.env, env_cfg = task_registry.make_env(name=args.task, args=args)
            
            # Create standard runner first to get train_cfg and log_dir
            _, self.train_cfg, self.log_dir = task_registry.make_alg_runner(
                env=self.env, name=args.task, args=args
            )
            
            # Create enhanced PPO runner with Zenoh integration
            self.ppo_runner = create_enhanced_ppo_runner(
                env=self.env,
                train_cfg=self.train_cfg,
                log_dir=self.log_dir,
                device=self.config.device,
                session_manager=self.session_manager
            )
            
            logger.info(f"Training environment initialized for task: {args.task}")
            
        except Exception as e:
            logger.error(f"Failed to initialize training environment: {e}")
            raise
    
    async def _training_loop(self):
        """Main training loop"""
        try:
            max_iterations = self.config.max_iterations
            
            # Initialize Zenoh publishers in PPO runner
            if hasattr(self.ppo_runner, 'initialize_zenoh_publishers'):
                await self.ppo_runner.initialize_zenoh_publishers()
            
            # Set up iteration tracking
            start_iteration = self.current_iteration
            remaining_iterations = max_iterations - start_iteration
            
            if remaining_iterations <= 0:
                logger.info("Training already completed")
                return
            
            # Start PPO learning in executor to avoid blocking
            loop = asyncio.get_event_loop()
            
            # Create a function that runs the PPO learning with interruption support
            def run_ppo_learning():
                # Set current iteration in PPO runner
                self.ppo_runner.current_learning_iteration = start_iteration
                
                # Run PPO learning - this will handle metrics publishing internally
                self.ppo_runner.learn(
                    num_learning_iterations=remaining_iterations,
                    init_at_random_ep_len=True
                )
                
                return self.ppo_runner.current_learning_iteration
            
            # Run PPO learning in executor
            final_iteration = await loop.run_in_executor(None, run_ppo_learning)
            self.current_iteration = final_iteration
            
            # Training completed
            self.state = TrainingServiceState.IDLE
            await self._publish_status("Training completed")
            logger.info(f"Training loop completed after {final_iteration} iterations")
            
        except asyncio.CancelledError:
            logger.info("Training loop cancelled")
            raise
        except Exception as e:
            self.state = TrainingServiceState.ERROR
            logger.error(f"Error in training loop: {e}")
            await self._publish_status(f"Training error: {e}")
    
    def _run_training_iteration(self, iteration: int):
        """Run single training iteration (synchronous) - DEPRECATED"""
        # This method is now deprecated as we use the PPO runner directly
        pass
    
    async def _metrics_publishing_loop(self):
        """Publish training metrics periodically"""
        try:
            while self.state == TrainingServiceState.TRAINING:
                if self.latest_metrics:
                    message = MessageFactory.create_data(
                        self.latest_metrics.to_dict(),
                        "training_service"
                    )
                    message.header.priority = Priority.NORMAL
                    
                    await self.topic_manager.publish_message(topics.TRAINING_METRICS, message)
                
                await asyncio.sleep(self.config.metrics_publish_interval)
                
        except asyncio.CancelledError:
            logger.debug("Metrics publishing loop cancelled")
        except Exception as e:
            logger.error(f"Error in metrics publishing loop: {e}")
    
    async def _status_publishing_loop(self):
        """Publish training status periodically"""
        try:
            while self.state in [TrainingServiceState.TRAINING, TrainingServiceState.PAUSED]:
                status = TrainingStatus(
                    state=TrainingState.TRAINING if self.state == TrainingServiceState.TRAINING else TrainingState.PAUSED,
                    current_iteration=self.current_iteration,
                    total_iterations=self.config.max_iterations,
                    start_time=self.start_time if self.start_time else time.time(),
                    last_update=time.time(),
                    status_message=f"Training in progress - Iteration {self.current_iteration}",
                    progress_percentage=(self.current_iteration / self.config.max_iterations) * 100 if self.config.max_iterations > 0 else 0.0
                )
                
                message = MessageFactory.create_status(status.to_dict(), "training_service")
                await self.topic_manager.publish_message(topics.TRAINING_STATUS, message)
                
                await asyncio.sleep(self.config.status_publish_interval)
                
        except asyncio.CancelledError:
            logger.debug("Status publishing loop cancelled")
        except Exception as e:
            logger.error(f"Error in status publishing loop: {e}")
    
    async def _publish_status(self, message: str):
        """Publish a status message"""
        status = TrainingStatus(
            state=TrainingState.TRAINING if self.state == TrainingServiceState.TRAINING else TrainingState.IDLE,
            current_iteration=self.current_iteration,
            total_iterations=self.config.max_iterations,
            start_time=self.start_time if self.start_time else time.time(),
            last_update=time.time(),
            status_message=message,
            progress_percentage=(self.current_iteration / self.config.max_iterations) * 100 if self.config.max_iterations > 0 else 0.0
        )
        
        status_msg = MessageFactory.create_status(status.to_dict(), "training_service")
        await self.topic_manager.publish_message(topics.TRAINING_STATUS, status_msg)
    
    async def get_status(self) -> Dict[str, Any]:
        """Get current service status"""
        return {
            "state": self.state.value,
            "is_initialized": self.is_initialized,
            "current_iteration": self.current_iteration,
            "total_iterations": self.config.max_iterations,
            "elapsed_time": time.time() - self.start_time if self.start_time else 0.0,
            "config": {
                "task_name": self.config.task_name,
                "num_envs": self.config.num_envs,
                "experiment_name": self.config.experiment_name,
                "run_name": self.config.run_name
            }
        }
    
    async def shutdown(self):
        """Shutdown the training service"""
        logger.info("Shutting down training service...")
        
        # Stop training if running
        if self.state == TrainingServiceState.TRAINING:
            await self.stop_training()
        
        # Cancel all tasks
        tasks = [self.training_task, self.metrics_task, self.status_task]
        for task in tasks:
            if task and not task.done():
                task.cancel()
        
        # Wait for tasks to complete
        await asyncio.sleep(0.1)
        
        self.state = TrainingServiceState.IDLE
        logger.info("Training service shutdown complete")


# Factory function for creating training service
async def create_training_service(
    session_manager: EnhancedZenohSessionManager,
    config: Optional[TrainingServiceConfig] = None
) -> TrainingService:
    """Create and initialize a training service"""
    service = TrainingService(session_manager, config)
    
    if await service.initialize():
        return service
    else:
        raise RuntimeError("Failed to initialize training service")