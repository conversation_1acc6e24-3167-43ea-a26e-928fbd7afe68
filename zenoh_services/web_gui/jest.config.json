{"preset": "ts-jest", "testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/src/setupTests.ts"], "moduleNameMapper": {"^@/(.*)$": "<rootDir>/src/$1"}, "transform": {"^.+\\.tsx?$": ["ts-jest", {"tsconfig": {"jsx": "react-jsx", "esModuleInterop": true, "allowSyntheticDefaultImports": true}}]}, "testRegex": "(/__tests__/.*|(\\.|/)(test|spec))\\.(jsx?|tsx?)$", "moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json", "node"], "collectCoverageFrom": ["src/**/*.{ts,tsx}", "!src/**/*.d.ts", "!src/main.tsx", "!src/vite-env.d.ts"]}