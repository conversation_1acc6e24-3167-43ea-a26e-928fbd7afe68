/**
 * Zustand store for Zenoh client state management
 */

import React from 'react';
import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { ZenohWebSocketClient } from '../services/zenoh-client';
import { 
  ZenohConnectionStatus, 
  ConnectionState,
  StandardZenohMessage,
  TrainingStatus,
  TrainingMetrics,
  PlayStatus,
  RobotState,
  SystemStatus,
  SimulationMetrics,
  DeploymentStatus,
  ZENOH_TOPICS
} from '../types/zenoh-types';

interface ZenohStore {
  // Client and connection
  client: ZenohWebSocketClient | null;
  connectionStatus: ZenohConnectionStatus;
  
  // System state
  systemStatus: SystemStatus | null;
  
  // Training state
  trainingStatus: TrainingStatus | null;
  trainingMetrics: TrainingMetrics | null;
  
  // Play/Testing state
  playStatus: PlayStatus | null;
  robotState: RobotState | null;
  
  // Simulation state
  simulationMetrics: SimulationMetrics | null;
  simulationConfig: any | null;
  
  // Deployment state
  deploymentStatus: DeploymentStatus | null;
  
  // Actions
  initializeClient: (config?: any) => void;
  connect: () => Promise<void>;
  disconnect: () => void;
  subscribe: (topic: string, callback: (message: StandardZenohMessage) => void) => string;
  unsubscribe: (subscriptionId: string) => void;
  sendCommand: (topic: string, command: string, parameters?: Record<string, any>) => void;
  
  // Training-specific actions
  sendTrainingCommand: (command: string, parameters?: Record<string, any>) => void;
  getHistoricalTrainingData?: (timeRange: string) => Promise<void>;
  
  // State update actions
  updateSystemStatus: (status: SystemStatus) => void;
  updateTrainingStatus: (status: TrainingStatus) => void;
  updateTrainingMetrics: (metrics: TrainingMetrics) => void;
  updatePlayStatus: (status: PlayStatus) => void;
  updateRobotState: (state: RobotState) => void;
  updateSimulationMetrics: (metrics: SimulationMetrics) => void;
  updateSimulationConfig: (config: any) => void;
  updateDeploymentStatus: (status: DeploymentStatus) => void;
}

export const useZenohStore = create<ZenohStore>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    client: null,
    connectionStatus: {
      state: ConnectionState.DISCONNECTED,
      reconnectAttempts: 0
    },
    systemStatus: null,
    trainingStatus: null,
    trainingMetrics: null,
    playStatus: null,
    robotState: null,
    simulationMetrics: null,
    simulationConfig: null,
    deploymentStatus: null,

    // Actions
    initializeClient: (config = {}) => {
      const client = new ZenohWebSocketClient(config);
      
      // Set up connection status listener
      client.onConnectionStatusChanged((status) => {
        set({ connectionStatus: status });
      });

      set({ client });
    },

    connect: async () => {
      const { client } = get();
      if (client) {
        await client.connect();
      }
    },

    disconnect: () => {
      const { client } = get();
      if (client) {
        client.disconnect();
      }
    },

    subscribe: (topic: string, callback: (message: StandardZenohMessage) => void) => {
      const { client } = get();
      if (client) {
        return client.subscribe(topic, callback);
      }
      return '';
    },

    unsubscribe: (subscriptionId: string) => {
      const { client } = get();
      if (client) {
        client.unsubscribe(subscriptionId);
      }
    },

    sendCommand: (topic: string, command: string, parameters = {}) => {
      const { client } = get();
      if (client) {
        client.sendCommand(topic, command, parameters);
      }
    },

    // Training-specific actions
    sendTrainingCommand: (command: string, parameters = {}) => {
      const { client } = get();
      if (client) {
        client.sendCommand(ZENOH_TOPICS.TRAINING_COMMAND, command, parameters);
      }
    },

    getHistoricalTrainingData: async (timeRange: string) => {
      const { client } = get();
      if (client) {
        client.sendCommand(ZENOH_TOPICS.TRAINING_COMMAND, 'get_historical_data', { time_range: timeRange });
      }
    },

    // State update actions
    updateSystemStatus: (status: SystemStatus) => {
      set({ systemStatus: status });
    },

    updateTrainingStatus: (status: TrainingStatus) => {
      set({ trainingStatus: status });
    },

    updateTrainingMetrics: (metrics: TrainingMetrics) => {
      set({ trainingMetrics: metrics });
    },

    updatePlayStatus: (status: PlayStatus) => {
      set({ playStatus: status });
    },

    updateRobotState: (state: RobotState) => {
      set({ robotState: state });
    },

    updateSimulationMetrics: (metrics: SimulationMetrics) => {
      set({ simulationMetrics: metrics });
    },

    updateSimulationConfig: (config: any) => {
      set({ simulationConfig: config });
    },

    updateDeploymentStatus: (status: DeploymentStatus) => {
      set({ deploymentStatus: status });
    }
  }))
);

/**
 * Custom hook for auto-subscribing to Zenoh topics
 */
export const useZenohSubscription = (
  topic: string,
  handler: (message: StandardZenohMessage) => void,
  deps: any[] = []
) => {
  const { subscribe, unsubscribe } = useZenohStore();

  React.useEffect(() => {
    const subscriptionId = subscribe(topic, handler);
    
    return () => {
      unsubscribe(subscriptionId);
    };
  }, [topic, ...deps]);
};

/**
 * Initialize Zenoh subscriptions for system topics
 */
export const initializeSystemSubscriptions = () => {
  const store = useZenohStore.getState();
  
  if (!store.client) {
    console.warn('Zenoh client not initialized');
    return;
  }

  // System status subscription
  store.subscribe(ZENOH_TOPICS.SYSTEM_STATUS, (message) => {
    try {
      const systemStatus = message.payload as SystemStatus;
      store.updateSystemStatus(systemStatus);
    } catch (error) {
      console.error('Error processing system status message:', error);
    }
  });

  // Training status subscription
  store.subscribe(ZENOH_TOPICS.TRAINING_STATUS, (message) => {
    try {
      const trainingStatus = message.payload as TrainingStatus;
      store.updateTrainingStatus(trainingStatus);
    } catch (error) {
      console.error('Error processing training status message:', error);
    }
  });

  // Training metrics subscription
  store.subscribe(ZENOH_TOPICS.TRAINING_METRICS, (message) => {
    try {
      const trainingMetrics = message.payload as TrainingMetrics;
      store.updateTrainingMetrics(trainingMetrics);
    } catch (error) {
      console.error('Error processing training metrics message:', error);
    }
  });

  // Play status subscription
  store.subscribe(ZENOH_TOPICS.PLAY_STATUS, (message) => {
    try {
      const playStatus = message.payload as PlayStatus;
      store.updatePlayStatus(playStatus);
    } catch (error) {
      console.error('Error processing play status message:', error);
    }
  });

  // Robot state subscription
  store.subscribe(ZENOH_TOPICS.ROBOT_STATE, (message) => {
    try {
      const robotState = message.payload as RobotState;
      store.updateRobotState(robotState);
    } catch (error) {
      console.error('Error processing robot state message:', error);
    }
  });

  // Simulation metrics subscription
  store.subscribe(ZENOH_TOPICS.SIMULATION_METRICS, (message) => {
    try {
      const simulationMetrics = message.payload as SimulationMetrics;
      store.updateSimulationMetrics(simulationMetrics);
    } catch (error) {
      console.error('Error processing simulation metrics message:', error);
    }
  });

  // Deployment status subscription
  store.subscribe(ZENOH_TOPICS.DEPLOYMENT_STATUS, (message) => {
    try {
      const deploymentStatus = message.payload as DeploymentStatus;
      store.updateDeploymentStatus(deploymentStatus);
    } catch (error) {
      console.error('Error processing deployment status message:', error);
    }
  });
};

/**
 * Training control commands
 */
export const useTrainingCommands = () => {
  const { sendCommand } = useZenohStore();

  return {
    startTraining: (config?: any) => {
      sendCommand(ZENOH_TOPICS.TRAINING_COMMAND, 'start', { config });
    },
    
    stopTraining: () => {
      sendCommand(ZENOH_TOPICS.TRAINING_COMMAND, 'stop');
    },
    
    pauseTraining: () => {
      sendCommand(ZENOH_TOPICS.TRAINING_COMMAND, 'pause');
    },
    
    resumeTraining: () => {
      sendCommand(ZENOH_TOPICS.TRAINING_COMMAND, 'resume');
    },
    
    saveCheckpoint: () => {
      sendCommand(ZENOH_TOPICS.TRAINING_COMMAND, 'save');
    }
  };
};

/**
 * Play/Testing control commands
 */
export const usePlayCommands = () => {
  const { sendCommand } = useZenohStore();

  return {
    startPlay: (config?: any) => {
      sendCommand(ZENOH_TOPICS.PLAY_COMMAND, 'start', { config });
    },
    
    stopPlay: () => {
      sendCommand(ZENOH_TOPICS.PLAY_COMMAND, 'stop');
    },
    
    recordVideo: (enable: boolean) => {
      sendCommand(ZENOH_TOPICS.PLAY_COMMAND, 'record', { enable });
    }
  };
};

/**
 * Robot control commands
 */
export const useRobotCommands = () => {
  const { sendCommand } = useZenohStore();

  return {
    sendVelocityCommand: (linear: [number, number, number], angular: [number, number, number]) => {
      sendCommand(ZENOH_TOPICS.ROBOT_COMMAND, 'velocity', {
        linear_velocity: linear,
        angular_velocity: angular,
        timestamp: Date.now() / 1000
      });
    },
    
    sendPositionCommand: (positions: Record<string, number>) => {
      sendCommand(ZENOH_TOPICS.ROBOT_COMMAND, 'position', {
        target_positions: positions,
        timestamp: Date.now() / 1000
      });
    }
  };
};

/**
 * Configuration commands
 */
export const useConfigCommands = () => {
  const { sendCommand } = useZenohStore();

  return {
    updateSimulationConfig: (config: any) => {
      sendCommand(ZENOH_TOPICS.SIMULATION_CONFIG, 'update', { config });
    },
    
    resetConfiguration: (configType: string) => {
      sendCommand(ZENOH_TOPICS.CONFIG_REQUEST, 'reset', { config_type: configType });
    }
  };
};