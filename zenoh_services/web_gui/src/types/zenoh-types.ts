/**
 * TypeScript interfaces for Zenoh message types
 * Based on Python data models in zenoh_services/core/data_models.py
 */

// Enums
export enum TrainingState {
  IDLE = "idle",
  STARTING = "starting",
  TRAINING = "training",
  PAUSED = "paused",
  STOPPING = "stopping",
  COMPLETED = "completed",
  ERROR = "error"
}

export enum PlayState {
  IDLE = "idle",
  LOADING = "loading",
  RUNNING = "running",
  RECORDING = "recording",
  STOPPED = "stopped",
  ERROR = "error"
}

export enum DeploymentState {
  IDLE = "idle",
  EXPORTING = "exporting",
  VALIDATING = "validating",
  DEPLOYING = "deploying",
  COMPLETED = "completed",
  ERROR = "error"
}

export enum MessageType {
  COMMAND = "command",
  RESPONSE = "response",
  STATUS = "status",
  DATA = "data",
  ERROR = "error",
  HEARTBEAT = "heartbeat"
}

export enum Priority {
  LOW = 0,
  NORMAL = 1,
  HIGH = 2,
  CRITICAL = 3
}

// Error Handling Types
export enum ErrorCategory {
  NETWORK = "network",
  SERVICE = "service",
  TRAINING = "training",
  SIMULATION = "simulation",
  DEPLOYMENT = "deployment",
  CONFIGURATION = "configuration",
  RESOURCE = "resource",
  ZENOH = "zenoh",
  HARDWARE = "hardware",
  DATA = "data"
}

export enum ErrorSeverity {
  LOW = "low",
  MEDIUM = "medium", 
  HIGH = "high",
  CRITICAL = "critical"
}

export enum HealthStatus {
  HEALTHY = "healthy",
  DEGRADED = "degraded",
  UNHEALTHY = "unhealthy",
  CRITICAL = "critical"
}

export enum NotificationType {
  ERROR = "error",
  WARNING = "warning",
  INFO = "info",
  SUCCESS = "success",
  RECOVERY = "recovery",
  CRITICAL = "critical"
}

export enum NotificationPriority {
  LOW = "low",
  MEDIUM = "medium",
  HIGH = "high",
  URGENT = "urgent"
}

export interface ErrorContext {
  service_name: string;
  service_state: string;
  timestamp: number;
  thread_id: number;
  function_name: string;
  additional_data?: Record<string, any>;
}

export interface ErrorInfo {
  error_id: string;
  timestamp: number;
  category: ErrorCategory;
  severity: ErrorSeverity;
  message: string;
  exception_type: string;
  traceback: string;
  context: ErrorContext;
  retry_count?: number;
  resolved?: boolean;
  resolution_timestamp?: number;
  resolution_method?: string;
}

export interface ServiceHealth {
  service_name: string;
  status: HealthStatus;
  last_check: number;
  error_count: number;
  recovery_attempts: number;
  uptime: number;
  metrics?: Record<string, any>;
}

export interface SystemHealth {
  timestamp: number;
  overall_health: string;
  services: Record<string, {
    status: string;
    metrics: Record<string, any>;
  }>;
  global_checks: Record<string, {
    consecutive_failures: number;
    last_check: number;
  }>;
  system_metrics: {
    cpu_usage: number;
    memory_usage: number;
    disk_usage: number;
  };
}

export interface NotificationAction {
  action_id: string;
  label: string;
  action_type: string;
  options?: Record<string, any>;
  requires_confirmation?: boolean;
  timeout?: number;
}

export interface ErrorNotification {
  notification_id: string;
  type: NotificationType;
  priority: NotificationPriority;
  title: string;
  message: string;
  timestamp: number;
  service_name?: string;
  error_id?: string;
  actions?: NotificationAction[];
  auto_dismiss?: boolean;
  dismiss_timeout?: number;
  persistent?: boolean;
  tags?: string[];
  metadata?: Record<string, any>;
}

export interface UserPrompt {
  prompt_id: string;
  title: string;
  message: string;
  prompt_type: string;
  options?: Record<string, any>;
  default_value?: any;
  timeout?: number;
  required?: boolean;
  validation_regex?: string;
  timestamp: number;
}

export interface ErrorStatistics {
  total_errors: number;
  errors_by_category: Record<string, number>;
  errors_by_severity: Record<string, number>;
  errors_by_service: Record<string, number>;
  resolved_errors: number;
  recovery_success_rate: number;
}

export interface FaultScenario {
  name: string;
  fault_type: string;
  simulation_mode: string;
  target_service: string;
  duration: number;
  intensity: number;
  parameters?: Record<string, any>;
  recovery_expected?: boolean;
  cascading_services?: string[];
}

// Message structures
export interface MessageHeader {
  message_id: string;
  timestamp: number;
  message_type: MessageType;
  priority: Priority;
  source_service: string;
  destination_service: string;
  correlation_id: string;
  ttl: number;
}

export interface StandardZenohMessage {
  header: MessageHeader;
  payload: Record<string, any>;
  metadata: Record<string, any>;
}

// Training related interfaces
export interface TrainingConfig {
  task_name: string;
  num_envs: number;
  max_iterations: number;
  learning_rate: number;
  batch_size: number;
  experiment_name: string;
  run_name: string;
  resume: boolean;
  checkpoint: number;
}

export interface TrainingMetrics {
  iteration: number;
  mean_reward: number;
  std_reward: number;
  mean_episode_length: number;
  learning_rate: number;
  entropy: number;
  kl_divergence: number;
  policy_loss: number;
  value_loss: number;
  timestamp: number;
  additional_metrics: Record<string, number>;
}

export interface TrainingStatus {
  state: TrainingState;
  current_iteration: number;
  total_iterations: number;
  start_time: number;
  last_update: number;
  status_message: string;
  progress_percentage: number;
  estimated_time_remaining?: number;
}

// Robot and simulation interfaces
export interface RobotCommand {
  linear_velocity: [number, number, number];
  angular_velocity: [number, number, number];
  timestamp: number;
  command_id: string;
}

export interface RobotState {
  joint_positions: number[]; // 12 elements
  joint_velocities: number[]; // 12 elements
  joint_torques: number[]; // 12 elements
  base_position: [number, number, number];
  base_orientation: [number, number, number, number]; // quaternion
  base_linear_velocity: [number, number, number];
  base_angular_velocity: [number, number, number];
  contact_forces: number[];
  timestamp: number;
  robot_id: string;
}

export interface PlayConfig {
  task_name: string;
  model_path: string;
  num_envs: number;
  record_video: boolean;
  video_path: string;
  use_joystick: boolean;
  fixed_commands: boolean;
  max_episode_length: number;
}

export interface PlayStatus {
  state: PlayState;
  current_episode: number;
  current_step: number;
  start_time: number;
  last_update: number;
  status_message: string;
  is_recording: boolean;
}

// Simulation configuration interfaces
export interface TerrainConfig {
  mesh_type: string;
  curriculum: boolean;
  static_friction: number;
  dynamic_friction: number;
  restitution: number;
  terrain_length: number;
  terrain_width: number;
  horizontal_scale: number;
  vertical_scale: number;
  border_size: number;
  slope_threshold: number;
  num_rows: number;
  num_cols: number;
  max_init_terrain_level: number;
}

export interface PhysicsConfig {
  dt: number;
  substeps: number;
  num_threads: number;
  gravity: [number, number, number];
  solver_type: string;
  num_position_iterations: number;
  num_velocity_iterations: number;
  contact_offset: number;
  rest_offset: number;
  bounce_threshold_velocity: number;
  max_depenetration_velocity: number;
}

export interface RobotConfig {
  name: string;
  urdf_path: string;
  base_mass: number;
  joint_stiffness: number;
  joint_damping: number;
  contact_friction: number;
  control_frequency: number;
  control_type: string;
  action_scale: number;
  decimation: number;
  stiffness: Record<string, number>;
  damping: Record<string, number>;
}

export interface SimulationConfig {
  terrain: TerrainConfig;
  physics: PhysicsConfig;
  robot: RobotConfig;
  num_envs: number;
  episode_length: number;
  simulation_name: string;
  version: string;
}

export interface SimulationMetrics {
  fps: number;
  step_time: number;
  physics_time: number;
  render_time: number;
  memory_usage: number;
  timestamp: number;
  additional_metrics: Record<string, number>;
}

// Deployment interfaces
export interface ModelInfo {
  model_path: string;
  model_name: string;
  training_config: TrainingConfig;
  performance_metrics: Record<string, number>;
  creation_time: number;
  file_size: number;
  model_format: string;
}

export interface DeploymentConfig {
  model_path: string;
  export_format: string;
  export_path: string;
  target_platform: string;
  optimization_level: string;
}

export interface DeploymentStatus {
  state: DeploymentState;
  progress: number;
  status_message: string;
  last_update: number;
  start_time: number;
  exported_model_path: string;
}

// System interfaces
export interface SystemStatus {
  is_healthy: boolean;
  active_services: string[];
  system_load: Record<string, number>;
  error_count: number;
  last_update: number;
  uptime: number;
}



// Command and response interfaces
export interface CommandMessage {
  command: string;
  parameters: Record<string, any>;
  command_id: string;
  timestamp: number;
}

export interface TrainingCommand {
  command: string;
  config?: TrainingConfig;
  command_id: string;
  timestamp: number;
}

export interface RobotControlCommand {
  command_type: string;
  target_values: Record<string, number>;
  command_id: string;
  timestamp: number;
}

export interface ResponseMessage {
  success: boolean;
  message: string;
  data: Record<string, any>;
  command_id: string;
  timestamp: number;
}

export interface ConfigurationRequest {
  config_type: string;
  action: string;
  config_data: Record<string, any>;
  request_id: string;
  timestamp: number;
}

export interface ConfigurationResponse {
  success: boolean;
  config_type: string;
  config_data: Record<string, any>;
  error_message: string;
  request_id: string;
  timestamp: number;
}

// Zenoh topic constants
export const ZENOH_TOPICS = {
  // System
  SYSTEM_STATUS: "legged_gym/system/status",
  SYSTEM_HEALTH: "legged_gym/system/health",
  SYSTEM_ERROR: "legged_gym/system/error",
  
  // Training
  TRAINING_COMMAND: "legged_gym/training/command",
  TRAINING_STATUS: "legged_gym/training/status",
  TRAINING_METRICS: "legged_gym/training/metrics",
  TRAINING_PROGRESS: "legged_gym/training/progress",
  TRAINING_CHECKPOINT: "legged_gym/training/checkpoint",
  
  // Play/Testing
  PLAY_COMMAND: "legged_gym/play/command",
  PLAY_STATUS: "legged_gym/play/status",
  ROBOT_STATE: "legged_gym/robot/state",
  ROBOT_CONTROL: "legged_gym/robot/control",
  ROBOT_COMMAND: "legged_gym/robot/command",
  
  // Simulation
  SIMULATION_CONFIG: "legged_gym/simulation/config",
  SIMULATION_STATUS: "legged_gym/simulation/status",
  SIMULATION_METRICS: "legged_gym/simulation/metrics",
  TERRAIN_CONFIG: "legged_gym/simulation/terrain/config",
  PHYSICS_CONFIG: "legged_gym/simulation/physics/config",
  ROBOT_CONFIG: "legged_gym/simulation/robot/config",
  
  // Deployment
  DEPLOYMENT_COMMAND: "legged_gym/deployment/command",
  DEPLOYMENT_STATUS: "legged_gym/deployment/status",
  MODEL_INFO: "legged_gym/deployment/model/info",
  
  // Configuration
  CONFIG_UPDATE: "legged_gym/config/update",
  CONFIG_REQUEST: "legged_gym/config/request",
  
  // Environment
  ENV_STATUS: "legged_gym/environment/status",
  ENV_COMMAND: "legged_gym/environment/command"
} as const;

// WebSocket connection state
export enum ConnectionState {
  DISCONNECTED = "disconnected",
  CONNECTING = "connecting",
  CONNECTED = "connected",
  RECONNECTING = "reconnecting",
  ERROR = "error"
}

export interface ZenohConnectionStatus {
  state: ConnectionState;
  lastConnected?: Date;
  reconnectAttempts: number;
  error?: string;
}

// Subscription management
export interface Subscription {
  topic: string;
  callback: (message: StandardZenohMessage) => void;
  id: string;
}

export interface ZenohClientConfig {
  url: string;
  reconnectInterval: number;
  maxReconnectAttempts: number;
  heartbeatInterval: number;
}