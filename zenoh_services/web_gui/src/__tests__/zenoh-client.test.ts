/**
 * Basic integration tests for Zenoh WebSocket client
 * This is a simple test file to verify client functionality
 */

import { ZenohWebSocketClient } from '../services/zenoh-client';
import { ConnectionState, MessageType, type StandardZenohMessage } from '../types/zenoh-types';

// Mock WebSocket for testing
class MockWebSocket {
  public static CONNECTING = 0;
  public static OPEN = 1;
  public static CLOSING = 2;
  public static CLOSED = 3;

  public readyState = MockWebSocket.CONNECTING;
  public onopen: ((event: Event) => void) | null = null;
  public onclose: ((event: CloseEvent) => void) | null = null;
  public onerror: ((event: ErrorEvent) => void) | null = null;
  public onmessage: ((event: MessageEvent) => void) | null = null;

  constructor(public url: string) {
    // Simulate async connection
    setTimeout(() => {
      this.readyState = MockWebSocket.OPEN;
      if (this.onopen) {
        this.onopen(new Event('open'));
      }
    }, 100);
  }

  send(data: string) {
    console.log('Mock WebSocket sending:', data);
  }

  close(code?: number, reason?: string) {
    this.readyState = MockWebSocket.CLOSED;
    if (this.onclose) {
      this.onclose(new CloseEvent('close', { code: code || 1000, reason }));
    }
  }

  // Simulate receiving a message
  simulateMessage(data: any) {
    if (this.onmessage && this.readyState === MockWebSocket.OPEN) {
      this.onmessage(new MessageEvent('message', { 
        data: JSON.stringify(data) 
      }));
    }
  }
}

// Replace WebSocket with mock
(global as any).WebSocket = MockWebSocket;

describe('ZenohWebSocketClient Integration Tests', () => {
  let client: ZenohWebSocketClient;
  let mockWs: MockWebSocket;

  beforeEach(() => {
    client = new ZenohWebSocketClient({
      url: 'ws://localhost:8080/test',
      reconnectInterval: 1000,
      maxReconnectAttempts: 3,
      heartbeatInterval: 5000
    });
  });

  afterEach(() => {
    if (client) {
      client.disconnect();
    }
  });

  test('should initialize with correct default state', () => {
    const status = client.getConnectionStatus();
    expect(status.state).toBe(ConnectionState.DISCONNECTED);
    expect(status.reconnectAttempts).toBe(0);
    expect(client.isConnected()).toBe(false);
  });

  test('should connect successfully', async () => {
    const connectionPromise = client.connect();
    
    // Wait for connection to be established
    await new Promise(resolve => setTimeout(resolve, 150));
    
    expect(client.isConnected()).toBe(true);
    const status = client.getConnectionStatus();
    expect(status.state).toBe(ConnectionState.CONNECTED);
  });

  test('should handle connection status changes', async () => {
    const statusChanges: ConnectionState[] = [];
    
    client.onConnectionStatusChanged((status) => {
      statusChanges.push(status.state);
    });

    await client.connect();
    await new Promise(resolve => setTimeout(resolve, 150));

    expect(statusChanges).toContain(ConnectionState.CONNECTING);
    expect(statusChanges).toContain(ConnectionState.CONNECTED);
  });

  test('should subscribe to topics', async () => {
    await client.connect();
    await new Promise(resolve => setTimeout(resolve, 150));

    const messages: StandardZenohMessage[] = [];
    const subscriptionId = client.subscribe('test/topic', (message) => {
      messages.push(message);
    });

    expect(subscriptionId).toBeTruthy();
    expect(typeof subscriptionId).toBe('string');
  });

  test('should receive messages for subscribed topics', async () => {
    await client.connect();
    await new Promise(resolve => setTimeout(resolve, 150));

    const messages: StandardZenohMessage[] = [];
    client.subscribe('test/topic', (message) => {
      messages.push(message);
    });

    // Get the mock WebSocket instance
    const wsInstance = (client as any).ws as MockWebSocket;
    
    // Simulate receiving a message
    const testMessage: StandardZenohMessage = {
      header: {
        message_id: 'test-123',
        timestamp: Date.now() / 1000,
        message_type: MessageType.DATA,
        priority: 1,
        source_service: 'test_service',
        destination_service: '',
        correlation_id: '',
        ttl: 300
      },
      payload: { test: 'data' },
      metadata: {}
    };

    wsInstance.simulateMessage({
      type: 'message',
      topic: 'test/topic',
      data: testMessage
    });

    await new Promise(resolve => setTimeout(resolve, 50));
    expect(messages).toHaveLength(1);
    expect(messages[0]).toEqual(testMessage);
  });

  test('should send commands correctly', async () => {
    await client.connect();
    await new Promise(resolve => setTimeout(resolve, 150));

    const sendSpy = jest.spyOn((client as any).ws, 'send');
    
    client.sendCommand('test/command', 'start', { param1: 'value1' });

    expect(sendSpy).toHaveBeenCalled();
    const sentData = JSON.parse(sendSpy.mock.calls[0][0] as string);
    
    expect(sentData.type).toBe('publish');
    expect(sentData.topic).toBe('test/command');
    expect(sentData.data.payload.command).toBe('start');
    expect(sentData.data.payload.parameters.param1).toBe('value1');
  });

  test('should unsubscribe from topics', async () => {
    await client.connect();
    await new Promise(resolve => setTimeout(resolve, 150));

    const subscriptionId = client.subscribe('test/topic', () => {});
    client.unsubscribe(subscriptionId);

    const sendSpy = jest.spyOn((client as any).ws, 'send');
    
    // The unsubscribe should have been called
    const unsubscribeCalls = sendSpy.mock.calls.filter(call => {
      const data = JSON.parse(call[0] as string);
      return data.type === 'unsubscribe';
    });

    expect(unsubscribeCalls.length).toBeGreaterThan(0);
  });

  test('should disconnect cleanly', async () => {
    await client.connect();
    await new Promise(resolve => setTimeout(resolve, 150));

    expect(client.isConnected()).toBe(true);

    client.disconnect();
    
    const status = client.getConnectionStatus();
    expect(status.state).toBe(ConnectionState.DISCONNECTED);
    expect(client.isConnected()).toBe(false);
  });
});

// Additional utility tests
describe('ZenohWebSocketClient Utility Functions', () => {
  test('should generate unique message IDs', () => {
    const client = new ZenohWebSocketClient();
    const id1 = (client as any).generateId();
    const id2 = (client as any).generateId();
    
    expect(id1).toBeTruthy();
    expect(id2).toBeTruthy();
    expect(id1).not.toBe(id2);
  });

  test('should validate topic matching', () => {
    const client = new ZenohWebSocketClient();
    const topicMatches = (client as any).topicMatches;
    
    expect(topicMatches('test/topic', 'test/topic')).toBe(true);
    expect(topicMatches('test/topic', 'test/other')).toBe(false);
    expect(topicMatches('test', 'test/subtopic')).toBe(false);
  });
});