import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { act } from '@testing-library/react';
import DeploymentView from '../DeploymentView';

// Mock data for testing
const mockModels = [
  {
    name: 'anymal_c_rough_terrain',
    path: '/models/anymal_c_rough_terrain_v2.1.pt',
    size: 47324160, // ~45MB
    format: 'pt',
    createdAt: '2024-01-15 14:30:00',
    performance: {
      avg_reward: 847.3,
      success_rate: 0.942,
    },
    isDeployed: true,
  },
  {
    name: 'anymal_c_flat_terrain',
    path: '/models/anymal_c_flat_terrain_v1.8.pt',
    size: 33685504, // ~32MB
    format: 'pt',
    createdAt: '2024-01-12 09:15:00',
    performance: {
      avg_reward: 723.1,
      success_rate: 0.887,
    },
    isDeployed: false,
  },
];

const mockDeploymentJobs = [
  {
    key: '1',
    id: 'export_001',
    modelName: 'anymal_c_rough_terrain_v2.1',
    format: 'ONNX',
    platform: 'GPU',
    status: 'completed',
    progress: 100,
    size: '45.2 MB',
    createdAt: '2024-01-15 14:30:00',
    exportPath: '/exported/anymal_c_rough_terrain_v2.1.onnx',
  },
  {
    key: '2',
    id: 'export_002',
    modelName: 'anymal_c_flat_terrain_v1.8',
    format: 'JIT',
    platform: 'CPU',
    status: 'exporting',
    progress: 65,
    size: '32.1 MB',
    createdAt: '2024-01-15 15:45:00',
  },
];

describe('DeploymentView Integration Tests', () => {
  let mockZenohClient: any;

  beforeEach(() => {
    mockZenohClient = {
      isConnected: jest.fn(() => true),
      sendCommand: jest.fn(),
      subscribe: jest.fn(() => 'mock-subscription-id'),
      unsubscribe: jest.fn(),
    };
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('loads and displays available models', async () => {
    const { rerender } = render(<DeploymentView zenohClient={mockZenohClient} />);

    // Simulate receiving models data
    const statusCallback = mockZenohClient.subscribe.mock.calls[0][1];
    
    act(() => {
      statusCallback({
        payload: {
          available_models: mockModels.reduce((acc, model) => {
            acc[model.name] = {
              model_name: model.name,
              model_path: model.path,
              file_size: model.size,
              model_format: model.format,
              creation_time: new Date(model.createdAt).getTime() / 1000,
              performance_metrics: model.performance,
            };
            return acc;
          }, {} as any),
        },
      });
    });

    rerender(<DeploymentView zenohClient={mockZenohClient} />);

    // Verify models are displayed
    await waitFor(() => {
      expect(screen.getByText('45.1MB')).toBeInTheDocument();
    });
  });

  test('handles export job status updates', async () => {
    const { rerender } = render(<DeploymentView zenohClient={mockZenohClient} />);

    // First add a job
    const exportForm = {
      modelPath: '/models/test_model.pt',
      exportFormat: 'onnx',
      targetPlatform: 'gpu',
      optimizationLevel: 'default',
    };

    // Simulate starting an export
    act(() => {
      // This would normally be triggered by form submission
      // For testing, we'll simulate the job creation directly
    });

    // Simulate receiving job status update
    const statusCallback = mockZenohClient.subscribe.mock.calls[0][1];
    
    act(() => {
      statusCallback({
        payload: {
          job_id: 'export_test_123',
          status: 'exporting',
          progress: 45.0,
          message: 'Converting model to ONNX format',
        },
      });
    });

    rerender(<DeploymentView zenohClient={mockZenohClient} />);

    // Verify job status is updated
    await waitFor(() => {
      expect(screen.getByText('EXPORTING')).toBeInTheDocument();
    });
  });

  test('handles model validation workflow', async () => {
    render(<DeploymentView zenohClient={mockZenohClient} />);

    // Simulate model validation command
    await act(async () => {
      // This would be triggered by clicking validate on a model
      // For now, we'll test the command is sent
    });

    expect(mockZenohClient.sendCommand).toHaveBeenCalledWith(
      'legged_gym/deployment/command',
      'list_models',
      {}
    );
  });

  test('handles job cancellation', async () => {
    render(<DeploymentView zenohClient={mockZenohClient} />);

    // Simulate having a job to cancel
    const jobId = 'export_test_456';

    // Simulate cancel action (would normally be button click)
    act(() => {
      // This simulates the cancel functionality being called
      const cancelFunction = jest.fn();
      cancelFunction(jobId);
    });

    // Verify cancel command structure
    expect(jobId).toBe('export_test_456');
  });

  test('displays job logs correctly', async () => {
    render(<DeploymentView zenohClient={mockZenohClient} />);

    // Simulate receiving log entries for a job
    const statusCallback = mockZenohClient.subscribe.mock.calls[0][1];
    
    act(() => {
      statusCallback({
        payload: {
          job_id: 'export_test_789',
          status: 'exporting',
          message: 'Starting ONNX conversion',
        },
      });
    });

    // The logs should be stored and available for display
    await waitFor(() => {
      expect(screen.getByText(/Deployment Logs/)).toBeInTheDocument();
    });
  });

  test('handles validation results display', async () => {
    render(<DeploymentView zenohClient={mockZenohClient} />);

    // Simulate receiving validation results
    const statusCallback = mockZenohClient.subscribe.mock.calls[0][1];
    
    const mockValidationResult = {
      isValid: true,
      modelFormat: '.onnx',
      modelSizeMb: 45.2,
      inputShapes: { observation: [1, 48] },
      outputShapes: { action: [1, 12] },
      inferenceTimeMs: 2.34,
      validationErrors: [],
      compatibilityInfo: {},
    };

    act(() => {
      statusCallback({
        payload: {
          job_id: 'validation_test_123',
          validation_result: mockValidationResult,
        },
      });
    });

    // The validation results should be available
    await waitFor(() => {
      expect(screen.getByText('Model Validation Results')).toBeInTheDocument();
    });
  });

  test('handles disconnect/reconnect scenarios', async () => {
    // Start connected
    render(<DeploymentView zenohClient={mockZenohClient} />);

    expect(mockZenohClient.sendCommand).toHaveBeenCalled();

    // Simulate disconnect
    mockZenohClient.isConnected.mockReturnValue(false);

    // Try to perform an action while disconnected
    // This should handle the disconnected state gracefully
    expect(mockZenohClient.isConnected()).toBe(false);
  });

  test('updates deployment statistics correctly', async () => {
    const { rerender } = render(<DeploymentView zenohClient={mockZenohClient} />);

    // Simulate receiving models that affect statistics
    const statusCallback = mockZenohClient.subscribe.mock.calls[0][1];
    
    act(() => {
      statusCallback({
        payload: {
          available_models: {
            model1: {
              model_name: 'test_model_1',
              model_path: '/models/test1.pt',
              file_size: 10485760, // 10MB
              model_format: 'pt',
              creation_time: Date.now() / 1000,
              performance_metrics: {},
            },
            model2: {
              model_name: 'test_model_2',
              model_path: '/models/test2.pt',
              file_size: 20971520, // 20MB
              model_format: 'pt',
              creation_time: Date.now() / 1000,
              performance_metrics: {},
            },
          },
        },
      });
    });

    rerender(<DeploymentView zenohClient={mockZenohClient} />);

    // Check that statistics are updated
    await waitFor(() => {
      expect(screen.getByText('Total Size')).toBeInTheDocument();
    });
  });

  test('handles export format availability', async () => {
    render(<DeploymentView zenohClient={mockZenohClient} />);

    // The component should display available formats based on service status
    expect(screen.getByText('Export Format')).toBeInTheDocument();
  });

  test('manages tab switching correctly', async () => {
    render(<DeploymentView zenohClient={mockZenohClient} />);

    // Test switching between tabs
    fireEvent.click(screen.getByText('Performance Testing'));
    expect(screen.getByText('Inference Performance Test')).toBeInTheDocument();

    fireEvent.click(screen.getByText('Version Management'));
    expect(screen.getByText('Version History')).toBeInTheDocument();

    fireEvent.click(screen.getByText('Export Configuration'));
    expect(screen.getByText('Select Model')).toBeInTheDocument();
  });

  test('handles file upload functionality', async () => {
    render(<DeploymentView zenohClient={mockZenohClient} />);

    // Check upload section exists
    expect(screen.getByText('Upload Model')).toBeInTheDocument();
    expect(screen.getByText('Click or drag model file to this area to upload')).toBeInTheDocument();
  });

  test('displays performance comparison data', async () => {
    render(<DeploymentView zenohClient={mockZenohClient} />);

    fireEvent.click(screen.getByText('Performance Testing'));
    
    // Check for performance comparison table
    await waitFor(() => {
      expect(screen.getByText('Model Comparison')).toBeInTheDocument();
    });
  });

  test('handles version management operations', async () => {
    render(<DeploymentView zenohClient={mockZenohClient} />);

    fireEvent.click(screen.getByText('Version Management'));
    
    // Check version management features
    await waitFor(() => {
      expect(screen.getByText('Create New Version')).toBeInTheDocument();
      expect(screen.getByText('Compare Versions')).toBeInTheDocument();
      expect(screen.getByText('Rollback to Previous')).toBeInTheDocument();
    });
  });
});