import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import '@testing-library/jest-dom';
import { notification } from 'antd';
import MonitoringView from '../MonitoringView';

// Mock antd notification
jest.mock('antd', () => {
  const actual = jest.requireActual('antd');
  return {
    ...actual,
    notification: {
      error: jest.fn(),
      warning: jest.fn(),
      info: jest.fn(),
      close: jest.fn(),
    },
  };
});

// Mock ECharts component
jest.mock('echarts-for-react', () => {
  return jest.fn(({ option, style }) => (
    <div data-testid="echarts-mock" style={style} data-option={JSON.stringify(option)} />
  ));
});

// Mock dayjs
jest.mock('dayjs', () => {
  const mockDayjs = jest.fn(() => ({
    format: jest.fn(() => '2024-01-15 14:30:00'),
    fromNow: jest.fn(() => '5 minutes ago'),
    isAfter: jest.fn(() => true),
    isBefore: jest.fn(() => true),
    toFixed: jest.fn(() => '45.2'),
  }));
  
  // Add the extend method
  (mockDayjs as any).extend = jest.fn();
  
  return mockDayjs;
});

// Mock Zenoh Client
const mockZenohClient = {
  subscribe: jest.fn(),
  unsubscribe: jest.fn(),
  sendCommand: jest.fn(),
  isConnected: jest.fn(() => true),
};

describe('MonitoringView', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
    mockZenohClient.subscribe.mockReturnValue('mock-subscription-id');
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  it('renders without crashing', () => {
    render(<MonitoringView />);
    expect(screen.getByText('System Monitoring & Logs')).toBeInTheDocument();
  });

  it('displays system status correctly', () => {
    render(<MonitoringView />);
    
    expect(screen.getByText('System Uptime')).toBeInTheDocument();
    expect(screen.getByText('Services Status')).toBeInTheDocument();
    expect(screen.getByText('Active Alerts')).toBeInTheDocument();
    expect(screen.getByText('Avg Response Time')).toBeInTheDocument();
  });

  it('renders all tabs correctly', () => {
    render(<MonitoringView />);
    
    expect(screen.getByText('System Overview')).toBeInTheDocument();
    expect(screen.getByText('System Logs')).toBeInTheDocument();
    expect(screen.getByText('Alerts')).toBeInTheDocument();
  });

  it('switches between tabs correctly', async () => {
    render(<MonitoringView />);
    
    // Click on System Logs tab
    fireEvent.click(screen.getByText('System Logs'));
    await waitFor(() => {
      expect(screen.getByText('entries')).toBeInTheDocument();
    });

    // Click on Alerts tab
    fireEvent.click(screen.getByText('Alerts'));
    await waitFor(() => {
      expect(screen.getByText('Active Alerts')).toBeInTheDocument();
    });
  });

  it('displays system metrics charts', () => {
    render(<MonitoringView />);
    
    const charts = screen.getAllByTestId('echarts-mock');
    expect(charts.length).toBeGreaterThan(0);
  });

  it('shows CPU, Memory, GPU, Network, and Disk usage', () => {
    render(<MonitoringView />);
    
    expect(screen.getByText('CPU Usage')).toBeInTheDocument();
    expect(screen.getByText('Memory')).toBeInTheDocument();
    expect(screen.getByText('GPU Usage')).toBeInTheDocument();
    expect(screen.getByText('Network')).toBeInTheDocument();
    expect(screen.getByText('Disk Usage')).toBeInTheDocument();
  });

  it('displays service health status', () => {
    render(<MonitoringView />);
    
    expect(screen.getByText('Service Health Status')).toBeInTheDocument();
    expect(screen.getByText('Training Service')).toBeInTheDocument();
    expect(screen.getByText('Simulation Service')).toBeInTheDocument();
    expect(screen.getByText('Deployment Service')).toBeInTheDocument();
  });

  it('initializes Zenoh subscriptions when client is provided', () => {
    render(<MonitoringView zenohClient={mockZenohClient} />);
    
    expect(mockZenohClient.subscribe).toHaveBeenCalledWith(
      'legged_gym/system/metrics',
      expect.any(Function)
    );
    expect(mockZenohClient.subscribe).toHaveBeenCalledWith(
      'legged_gym/system/health',
      expect.any(Function)
    );
    expect(mockZenohClient.subscribe).toHaveBeenCalledWith(
      'legged_gym/system/logs',
      expect.any(Function)
    );
    expect(mockZenohClient.subscribe).toHaveBeenCalledWith(
      'legged_gym/system/alerts',
      expect.any(Function)
    );
  });

  it('handles real-time metrics updates correctly', async () => {
    render(<MonitoringView zenohClient={mockZenohClient} />);
    
    // Get the metrics subscription callback
    const metricsCallback = mockZenohClient.subscribe.mock.calls.find(
      call => call[0] === 'legged_gym/system/metrics'
    )[1];

    // Simulate metrics update
    const mockMetricsData = {
      payload: {
        cpu: 75.5,
        memory: 62.3,
        gpu: 45.2,
        network: 12.8,
        disk: 68.9,
        temperature: 55.0
      }
    };

    act(() => {
      metricsCallback(mockMetricsData);
    });

    // Metrics should be processed and charts updated
    await waitFor(() => {
      const charts = screen.getAllByTestId('echarts-mock');
      expect(charts.length).toBeGreaterThan(0);
    });
  });

  it('handles log updates correctly', async () => {
    render(<MonitoringView zenohClient={mockZenohClient} />);
    
    // Get the logs subscription callback
    const logsCallback = mockZenohClient.subscribe.mock.calls.find(
      call => call[0] === 'legged_gym/system/logs'
    )[1];

    // Switch to logs tab first
    fireEvent.click(screen.getByText('System Logs'));

    // Simulate log update
    const mockLogData = {
      payload: {
        timestamp: '2024-01-15T14:30:00Z',
        level: 'error',
        service: 'training_service',
        message: 'Test error message',
        correlation_id: 'test-123'
      }
    };

    act(() => {
      logsCallback(mockLogData);
    });

    await waitFor(() => {
      expect(screen.getByText('Test error message')).toBeInTheDocument();
    });
  });

  it('handles alert updates and shows notifications', async () => {
    render(<MonitoringView zenohClient={mockZenohClient} />);
    
    // Get the alerts subscription callback
    const alertsCallback = mockZenohClient.subscribe.mock.calls.find(
      call => call[0] === 'legged_gym/system/alerts'
    )[1];

    // Simulate alert update
    const mockAlertData = {
      payload: {
        id: 'alert-test-123',
        title: 'Test Alert',
        description: 'Test alert description',
        severity: 'critical',
        service: 'test_service',
        timestamp: '2024-01-15T14:30:00Z'
      }
    };

    act(() => {
      alertsCallback(mockAlertData);
    });

    // Should show notification
    expect(notification.error).toHaveBeenCalledWith({
      message: 'Test Alert',
      description: 'Test alert description',
      placement: 'topRight',
      duration: 0,
      key: 'alert-test-123'
    });
  });

  it('filters logs by level correctly', async () => {
    render(<MonitoringView />);
    
    // Switch to logs tab
    fireEvent.click(screen.getByText('System Logs'));

    // Find and click the log level filter
    const logLevelSelect = screen.getByDisplayValue('All Levels');
    fireEvent.mouseDown(logLevelSelect);
    
    await waitFor(() => {
      fireEvent.click(screen.getByText('Error'));
    });

    // Should filter to show only error logs
    await waitFor(() => {
      const logTable = screen.getByRole('table');
      expect(logTable).toBeInTheDocument();
    });
  });

  it('filters logs by service correctly', async () => {
    render(<MonitoringView />);
    
    // Switch to logs tab
    fireEvent.click(screen.getByText('System Logs'));

    // Find and click the service filter
    const serviceSelect = screen.getByDisplayValue('All Services');
    fireEvent.mouseDown(serviceSelect);
    
    await waitFor(() => {
      fireEvent.click(screen.getByText('Training Service'));
    });

    // Should filter to show only training service logs
    await waitFor(() => {
      const logTable = screen.getByRole('table');
      expect(logTable).toBeInTheDocument();
    });
  });

  it('searches logs correctly', async () => {
    render(<MonitoringView />);
    
    // Switch to logs tab
    fireEvent.click(screen.getByText('System Logs'));

    // Find search input and enter search term
    const searchInput = screen.getByPlaceholderText('Search logs...');
    fireEvent.change(searchInput, { target: { value: 'training' } });

    await waitFor(() => {
      // Should filter logs based on search term
      const logTable = screen.getByRole('table');
      expect(logTable).toBeInTheDocument();
    });
  });

  it('exports logs correctly', async () => {
    // Mock document.createElement and click
    const mockLink = {
      setAttribute: jest.fn(),
      click: jest.fn(),
    };
    jest.spyOn(document, 'createElement').mockReturnValue(mockLink as any);
    
    render(<MonitoringView />);
    
    // Switch to logs tab
    fireEvent.click(screen.getByText('System Logs'));

    // Find and click export button
    const exportButton = screen.getByText('Export');
    fireEvent.click(exportButton);

    await waitFor(() => {
      expect(document.createElement).toHaveBeenCalledWith('a');
      expect(mockLink.setAttribute).toHaveBeenCalledWith('href', expect.stringContaining('data:application/json'));
      expect(mockLink.click).toHaveBeenCalled();
    });
  });

  it('acknowledges alerts correctly', async () => {
    render(<MonitoringView />);
    
    // Switch to alerts tab
    fireEvent.click(screen.getByText('Alerts'));

    // Find acknowledge button (if any unacknowledged alerts exist)
    await waitFor(() => {
      const acknowledgeButtons = screen.queryAllByText('Acknowledge');
      if (acknowledgeButtons.length > 0) {
        fireEvent.click(acknowledgeButtons[0]);
      }
    });
  });

  it('resolves alerts correctly', async () => {
    render(<MonitoringView />);
    
    // Switch to alerts tab
    fireEvent.click(screen.getByText('Alerts'));

    // Find resolve button
    await waitFor(() => {
      const resolveButtons = screen.queryAllByText('Resolve');
      if (resolveButtons.length > 0) {
        fireEvent.click(resolveButtons[0]);
      }
    });
  });

  it('toggles real-time monitoring correctly', async () => {
    render(<MonitoringView />);
    
    // Find the real-time toggle switch
    const realtimeSwitch = screen.getByRole('switch');
    fireEvent.click(realtimeSwitch);

    // Should toggle the real-time monitoring state
    await waitFor(() => {
      expect(realtimeSwitch).toBeInTheDocument();
    });
  });

  it('opens alert rules settings drawer', async () => {
    render(<MonitoringView />);
    
    // Switch to alerts tab
    fireEvent.click(screen.getByText('Alerts'));

    // Find and click alert rules button
    const alertRulesButton = screen.getByText('Alert Rules');
    fireEvent.click(alertRulesButton);

    await waitFor(() => {
      expect(screen.getByText('Alert Rules Configuration')).toBeInTheDocument();
    });
  });

  it('updates alert rules correctly', async () => {
    render(<MonitoringView />);
    
    // Switch to alerts tab and open settings
    fireEvent.click(screen.getByText('Alerts'));
    fireEvent.click(screen.getByText('Alert Rules'));

    // Find rule toggles and click one
    await waitFor(() => {
      const ruleSwitches = screen.getAllByRole('switch');
      if (ruleSwitches.length > 1) { // Skip the alerts enabled switch
        fireEvent.click(ruleSwitches[1]);
      }
    });
  });

  it('handles metric threshold violations', async () => {
    render(<MonitoringView zenohClient={mockZenohClient} />);
    
    // Get the metrics subscription callback
    const metricsCallback = mockZenohClient.subscribe.mock.calls.find(
      call => call[0] === 'legged_gym/system/metrics'
    )[1];

    // Simulate high CPU usage that exceeds threshold
    const highCpuMetrics = {
      payload: {
        cpu: 95.0, // Above 80% threshold
        memory: 50.0,
        gpu: 30.0,
        network: 10.0,
        disk: 40.0,
        temperature: 50.0
      }
    };

    act(() => {
      metricsCallback(highCpuMetrics);
    });

    // Should trigger an alert notification
    await waitFor(() => {
      expect(notification.warning).toHaveBeenCalled();
    });
  });

  it('handles error log alerts', async () => {
    render(<MonitoringView zenohClient={mockZenohClient} />);
    
    // Get the logs subscription callback
    const logsCallback = mockZenohClient.subscribe.mock.calls.find(
      call => call[0] === 'legged_gym/system/logs'
    )[1];

    // Simulate critical log entry
    const criticalLog = {
      payload: {
        timestamp: '2024-01-15T14:30:00Z',
        level: 'critical',
        service: 'training_service',
        message: 'Critical system error occurred',
        correlation_id: 'crit-123'
      }
    };

    act(() => {
      logsCallback(criticalLog);
    });

    // Should trigger a critical alert
    await waitFor(() => {
      expect(notification.error).toHaveBeenCalled();
    });
  });

  it('updates metrics automatically when real-time is enabled', async () => {
    render(<MonitoringView />);
    
    // Fast-forward time to trigger automatic updates
    act(() => {
      jest.advanceTimersByTime(5000); // 5 seconds for metrics update
    });

    // Should have triggered metric updates
    await waitFor(() => {
      const charts = screen.getAllByTestId('echarts-mock');
      expect(charts.length).toBeGreaterThan(0);
    });
  });

  it('displays log entry details modal', async () => {
    render(<MonitoringView />);
    
    // Switch to logs tab
    fireEvent.click(screen.getByText('System Logs'));

    await waitFor(() => {
      // Find and click a log entry view button
      const viewButtons = screen.queryAllByRole('button', { name: /view details/i });
      if (viewButtons.length > 0) {
        fireEvent.click(viewButtons[0]);
        expect(screen.getByText('Log Entry Details')).toBeInTheDocument();
      }
    });
  });

  it('cleans up subscriptions on unmount', () => {
    const { unmount } = render(<MonitoringView zenohClient={mockZenohClient} />);
    
    unmount();
    
    expect(mockZenohClient.unsubscribe).toHaveBeenCalledWith('mock-subscription-id');
  });
});