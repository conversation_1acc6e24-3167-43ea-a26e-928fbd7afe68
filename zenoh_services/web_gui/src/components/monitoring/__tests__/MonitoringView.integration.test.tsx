import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import '@testing-library/jest-dom';
import MonitoringView from '../MonitoringView';
import { createMockZenohClient, mockZenohMessages } from './test-utils';

// Mock ECharts component
jest.mock('echarts-for-react', () => {
  return jest.fn(({ option, style }) => (
    <div data-testid="echarts-mock" style={style} data-option={JSON.stringify(option)} />
  ));
});

// Mock antd notification
jest.mock('antd', () => {
  const actual = jest.requireActual('antd');
  return {
    ...actual,
    notification: {
      error: jest.fn(),
      warning: jest.fn(),
      info: jest.fn(),
      close: jest.fn(),
    },
  };
});

// Mock dayjs
jest.mock('dayjs', () => {
  const mockDayjs = jest.fn(() => ({
    format: jest.fn(() => '2024-01-15 14:30:00'),
    fromNow: jest.fn(() => '5 minutes ago'),
    isAfter: jest.fn(() => true),
    isBefore: jest.fn(() => true),
    toFixed: jest.fn(() => '45.2'),
  }));
  
  // Add the extend method
  (mockDayjs as any).extend = jest.fn();
  
  return mockDayjs;
});

describe('MonitoringView Integration Tests', () => {
  let mockZenohClient: ReturnType<typeof createMockZenohClient>;

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
    mockZenohClient = createMockZenohClient(true);
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  describe('End-to-End Monitoring Workflow', () => {
    it('should handle complete monitoring session lifecycle', async () => {
      render(<MonitoringView zenohClient={mockZenohClient} />);

      // 1. Verify initial system overview is displayed
      expect(screen.getByText('System Monitoring & Logs')).toBeInTheDocument();
      expect(screen.getByText('System Uptime')).toBeInTheDocument();
      expect(screen.getByText('HEALTHY')).toBeInTheDocument();

      // 2. Verify all system metrics are displayed
      expect(screen.getByText('CPU Usage')).toBeInTheDocument();
      expect(screen.getByText('Memory')).toBeInTheDocument();
      expect(screen.getByText('GPU Usage')).toBeInTheDocument();

      // 3. Verify services health status
      expect(screen.getByText('Service Health Status')).toBeInTheDocument();
      expect(screen.getByText('Training Service')).toBeInTheDocument();

      // 4. Switch to logs tab and verify functionality
      fireEvent.click(screen.getByText('System Logs'));
      
      await waitFor(() => {
        expect(screen.getByText('System Logs')).toBeInTheDocument();
        expect(screen.getByPlaceholderText('Search logs...')).toBeInTheDocument();
        expect(screen.getByDisplayValue('All Levels')).toBeInTheDocument();
      });

      // 5. Switch to alerts tab and verify functionality
      fireEvent.click(screen.getByText('Alerts'));
      
      await waitFor(() => {
        expect(screen.getByText('Active Alerts')).toBeInTheDocument();
        expect(screen.getByText('Alert Rules')).toBeInTheDocument();
      });

      // 6. Verify Zenoh subscriptions were set up correctly
      expect(mockZenohClient.subscribe).toHaveBeenCalledTimes(4);
      expect(mockZenohClient.subscribe).toHaveBeenCalledWith(
        'legged_gym/system/metrics',
        expect.any(Function)
      );
      expect(mockZenohClient.subscribe).toHaveBeenCalledWith(
        'legged_gym/system/health',
        expect.any(Function)
      );
      expect(mockZenohClient.subscribe).toHaveBeenCalledWith(
        'legged_gym/system/logs',
        expect.any(Function)
      );
      expect(mockZenohClient.subscribe).toHaveBeenCalledWith(
        'legged_gym/system/alerts',
        expect.any(Function)
      );
    });

    it('should handle real-time data updates across all components', async () => {
      render(<MonitoringView zenohClient={mockZenohClient} />);

      // Get subscription callbacks
      const metricsCallback = mockZenohClient.subscribe.mock.calls.find(
        call => call[0] === 'legged_gym/system/metrics'
      )?.[1];
      const logsCallback = mockZenohClient.subscribe.mock.calls.find(
        call => call[0] === 'legged_gym/system/logs'
      )?.[1];
      const alertsCallback = mockZenohClient.subscribe.mock.calls.find(
        call => call[0] === 'legged_gym/system/alerts'
      )?.[1];

      expect(metricsCallback).toBeDefined();
      expect(logsCallback).toBeDefined();
      expect(alertsCallback).toBeDefined();

      // 1. Simulate metrics update
      act(() => {
        metricsCallback?.(mockZenohMessages.metricsUpdate);
      });

      // 2. Switch to logs tab and simulate log update
      fireEvent.click(screen.getByText('System Logs'));
      
      act(() => {
        logsCallback?.(mockZenohMessages.logUpdate);
      });

      await waitFor(() => {
        expect(screen.getByText('Configuration updated successfully')).toBeInTheDocument();
      });

      // 3. Switch to alerts tab and simulate alert update
      fireEvent.click(screen.getByText('Alerts'));

      act(() => {
        alertsCallback?.(mockZenohMessages.alertUpdate);
      });

      await waitFor(() => {
        expect(screen.getByText('Disk Space Warning')).toBeInTheDocument();
      });
    });

    it('should handle error conditions and recovery', async () => {
      // Test with disconnected client
      const disconnectedClient = createMockZenohClient(false);
      render(<MonitoringView zenohClient={disconnectedClient} />);

      // Should still render without crashing
      expect(screen.getByText('System Monitoring & Logs')).toBeInTheDocument();

      // Test reconnection
      disconnectedClient.isConnected.mockReturnValue(true);
      
      // Re-render component
      render(<MonitoringView zenohClient={disconnectedClient} />);
      
      // Should now set up subscriptions
      expect(disconnectedClient.subscribe).toHaveBeenCalled();
    });

    it('should handle high-frequency data updates without performance issues', async () => {
      render(<MonitoringView zenohClient={mockZenohClient} />);

      const metricsCallback = mockZenohClient.subscribe.mock.calls.find(
        call => call[0] === 'legged_gym/system/metrics'
      )?.[1];

      // Simulate rapid metrics updates
      for (let i = 0; i < 10; i++) {
        act(() => {
          metricsCallback?.({
            payload: {
              ...mockZenohMessages.metricsUpdate.payload,
              cpu: 50 + i * 2,
              timestamp: `14:30:${i.toString().padStart(2, '0')}`
            }
          });
        });
      }

      // Component should handle rapid updates gracefully
      await waitFor(() => {
        const charts = screen.getAllByTestId('echarts-mock');
        expect(charts.length).toBeGreaterThan(0);
      });
    });
  });

  describe('Alert Management Workflow', () => {
    it('should handle complete alert lifecycle', async () => {
      render(<MonitoringView zenohClient={mockZenohClient} />);

      // Switch to alerts tab
      fireEvent.click(screen.getByText('Alerts'));

      // Should show initial alerts
      await waitFor(() => {
        expect(screen.getByText('High Memory Usage')).toBeInTheDocument();
        expect(screen.getByText('Service Response Time')).toBeInTheDocument();
      });

      // Acknowledge an alert
      const acknowledgeButtons = screen.queryAllByText('Acknowledge');
      if (acknowledgeButtons.length > 0) {
        fireEvent.click(acknowledgeButtons[0]);
        
        await waitFor(() => {
          expect(screen.queryByText('ACKNOWLEDGED')).toBeInTheDocument();
        });
      }

      // Resolve an alert
      const resolveButtons = screen.queryAllByText('Resolve');
      if (resolveButtons.length > 0) {
        fireEvent.click(resolveButtons[0]);
      }

      // Open alert rules configuration
      fireEvent.click(screen.getByText('Alert Rules'));

      await waitFor(() => {
        expect(screen.getByText('Alert Rules Configuration')).toBeInTheDocument();
        expect(screen.getByText('High CPU Usage')).toBeInTheDocument();
      });

      // Toggle alert rule
      const ruleSwitches = screen.getAllByRole('switch');
      if (ruleSwitches.length > 1) {
        fireEvent.click(ruleSwitches[1]);
      }
    });

    it('should trigger alerts based on metric thresholds', async () => {
      const { notification } = require('antd');
      render(<MonitoringView zenohClient={mockZenohClient} />);

      const metricsCallback = mockZenohClient.subscribe.mock.calls.find(
        call => call[0] === 'legged_gym/system/metrics'
      )?.[1];

      // Simulate high CPU that should trigger alert
      act(() => {
        metricsCallback?.({
          payload: {
            cpu: 95.0, // Above 80% threshold
            memory: 50.0,
            gpu: 30.0,
            network: 10.0,
            disk: 40.0,
            temperature: 50.0,
            timestamp: '14:35:00'
          }
        });
      });

      // Should trigger alert notification
      await waitFor(() => {
        expect(notification.warning).toHaveBeenCalled();
      });
    });
  });

  describe('Log Management Workflow', () => {
    it('should handle complete log viewing and filtering workflow', async () => {
      render(<MonitoringView zenohClient={mockZenohClient} />);

      // Switch to logs tab
      fireEvent.click(screen.getByText('System Logs'));

      await waitFor(() => {
        expect(screen.getByText('System Logs')).toBeInTheDocument();
      });

      // Test log level filtering
      const logLevelSelect = screen.getByDisplayValue('All Levels');
      fireEvent.mouseDown(logLevelSelect);
      
      await waitFor(() => {
        fireEvent.click(screen.getByText('Error'));
      });

      // Test service filtering
      const serviceSelect = screen.getByDisplayValue('All Services');
      fireEvent.mouseDown(serviceSelect);
      
      await waitFor(() => {
        fireEvent.click(screen.getByText('Training Service'));
      });

      // Test log search
      const searchInput = screen.getByPlaceholderText('Search logs...');
      fireEvent.change(searchInput, { target: { value: 'training' } });

      // Test log export
      const exportButton = screen.getByText('Export');
      
      // Mock document.createElement for export test
      const mockLink = {
        setAttribute: jest.fn(),
        click: jest.fn(),
      };
      jest.spyOn(document, 'createElement').mockReturnValue(mockLink as any);
      
      fireEvent.click(exportButton);

      expect(document.createElement).toHaveBeenCalledWith('a');
      expect(mockLink.click).toHaveBeenCalled();
    });

    it('should display log details modal correctly', async () => {
      render(<MonitoringView zenohClient={mockZenohClient} />);

      // Switch to logs tab
      fireEvent.click(screen.getByText('System Logs'));

      await waitFor(() => {
        // Find and click a log entry view button (if any exist)
        const viewButtons = screen.queryAllByRole('button');
        const viewButton = viewButtons.find(button => 
          button.getAttribute('aria-label')?.includes('eye')
        );
        
        if (viewButton) {
          fireEvent.click(viewButton);
          expect(screen.getByText('Log Entry Details')).toBeInTheDocument();
        }
      });
    });
  });

  describe('Real-time Updates Integration', () => {
    it('should handle simultaneous updates from all data sources', async () => {
      render(<MonitoringView zenohClient={mockZenohClient} />);

      // Get all subscription callbacks
      const callbacks = {
        metrics: mockZenohClient.subscribe.mock.calls.find(
          call => call[0] === 'legged_gym/system/metrics'
        )?.[1],
        health: mockZenohClient.subscribe.mock.calls.find(
          call => call[0] === 'legged_gym/system/health'
        )?.[1],
        logs: mockZenohClient.subscribe.mock.calls.find(
          call => call[0] === 'legged_gym/system/logs'
        )?.[1],
        alerts: mockZenohClient.subscribe.mock.calls.find(
          call => call[0] === 'legged_gym/system/alerts'
        )?.[1]
      };

      // Simulate simultaneous updates
      act(() => {
        callbacks.metrics?.(mockZenohMessages.metricsUpdate);
        callbacks.health?.(mockZenohMessages.healthUpdate);
        callbacks.logs?.(mockZenohMessages.logUpdate);
        callbacks.alerts?.(mockZenohMessages.alertUpdate);
      });

      // All updates should be handled correctly
      await waitFor(() => {
        // Metrics should be updated
        const charts = screen.getAllByTestId('echarts-mock');
        expect(charts.length).toBeGreaterThan(0);

        // Switch to logs to verify log update
        fireEvent.click(screen.getByText('System Logs'));
      });

      await waitFor(() => {
        expect(screen.getByText('Configuration updated successfully')).toBeInTheDocument();
      });

      // Switch to alerts to verify alert update
      fireEvent.click(screen.getByText('Alerts'));

      await waitFor(() => {
        expect(screen.getByText('Disk Space Warning')).toBeInTheDocument();
      });
    });

    it('should handle automatic metric updates when real-time is enabled', async () => {
      render(<MonitoringView />);

      // Let automatic updates run
      act(() => {
        jest.advanceTimersByTime(10000); // 10 seconds
      });

      await waitFor(() => {
        // Should have updated metrics automatically
        const charts = screen.getAllByTestId('echarts-mock');
        expect(charts.length).toBeGreaterThan(0);
      });
    });

    it('should stop automatic updates when real-time is disabled', async () => {
      render(<MonitoringView />);

      // Find and toggle real-time switch
      const realtimeSwitch = screen.getByRole('switch');
      fireEvent.click(realtimeSwitch);

      // Advance time
      act(() => {
        jest.advanceTimersByTime(10000);
      });

      // Should not crash and should maintain stable state
      expect(screen.getByText('System Monitoring & Logs')).toBeInTheDocument();
    });
  });

  describe('Component Cleanup and Memory Management', () => {
    it('should properly clean up subscriptions on unmount', () => {
      const { unmount } = render(<MonitoringView zenohClient={mockZenohClient} />);

      // Unmount the component
      unmount();

      // Should unsubscribe from all Zenoh topics
      expect(mockZenohClient.unsubscribe).toHaveBeenCalledTimes(4);
      expect(mockZenohClient.unsubscribe).toHaveBeenCalledWith('mock-subscription-id');
    });

    it('should handle component re-mounting correctly', () => {
      const { unmount, rerender } = render(<MonitoringView zenohClient={mockZenohClient} />);

      // Unmount
      unmount();

      // Re-render
      rerender(<MonitoringView zenohClient={mockZenohClient} />);

      // Should set up subscriptions again
      expect(mockZenohClient.subscribe).toHaveBeenCalledTimes(8); // 4 initial + 4 after re-mount
    });
  });
});