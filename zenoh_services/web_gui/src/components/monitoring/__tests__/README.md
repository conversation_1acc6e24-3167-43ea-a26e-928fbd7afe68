# Monitoring Interface Unit Tests

This document describes the comprehensive unit tests created for the monitoring interface components.

## Test Files Created

### 1. MonitoringView.test.tsx
Comprehensive unit tests for the main MonitoringView component covering:

- **Basic Rendering Tests**
  - Component renders without crashing
  - All system status indicators are displayed
  - Tab navigation works correctly
  - System overview displays CPU, Memory, GPU metrics

- **Zenoh Integration Tests**
  - Zenoh client subscription setup
  - Real-time metrics updates handling
  - Service health updates processing
  - Log entries real-time updates
  - Alert notifications handling

- **User Interaction Tests**
  - Tab switching functionality
  - Log filtering by level and service
  - Log search functionality
  - Alert acknowledgment and resolution
  - Export logs functionality

- **Real-time Features Tests**
  - Automatic metric updates when real-time is enabled
  - Metric threshold violations trigger alerts
  - Critical log entries generate alerts
  - Real-time monitoring toggle functionality

### 2. ResourceMonitor.test.tsx
Comprehensive unit tests for the ResourceMonitor component covering:

- **Basic Rendering Tests**
  - Component renders without crashing
  - All resource cards displayed (CPU, Memory, GPU, Disk, Network)
  - Resource usage values and progress indicators
  - Temperature monitoring for hardware components

- **Real-time Updates Tests**
  - Automatic resource data updates
  - Resource history tracking for charts
  - Process information updates
  - Alert callback when thresholds exceeded

- **User Interface Tests**
  - Time range selector functionality
  - Auto-refresh toggle
  - Settings button interaction
  - Resource trend charts display

- **Process Monitoring Tests**
  - Top processes display
  - CPU usage color coding
  - Memory usage formatting
  - Process data periodic updates

### 3. MonitoringView.integration.test.tsx
Integration tests covering end-to-end monitoring workflows:

- **Complete Monitoring Lifecycle**
  - Initial system overview display
  - Tab navigation and data consistency
  - Zenoh subscription management
  - Component cleanup on unmount

- **Real-time Data Flow Tests**
  - Simultaneous updates from all data sources
  - High-frequency data updates handling
  - Error conditions and recovery
  - Performance under load

- **Alert Management Workflow**
  - Complete alert lifecycle (creation, acknowledgment, resolution)
  - Metric threshold-based alerts
  - Alert rules configuration
  - Notification system integration

- **Log Management Workflow**
  - Log viewing and filtering
  - Search functionality
  - Export operations
  - Log details modal display

### 4. test-utils.ts
Shared testing utilities and mock data including:

- **Mock Implementations**
  - MockZenohClient interface and factory
  - Mock system metrics data
  - Mock service health data
  - Mock log entries and alerts

- **Test Helper Functions**
  - Real-time update simulation
  - ECharts option mocking
  - Assertion helpers
  - Wait utilities

## Test Coverage Areas

### Core Functionality
- ✅ Component rendering and basic UI elements
- ✅ Real-time data updates and subscriptions
- ✅ User interactions and state management
- ✅ Error handling and edge cases

### Zenoh Integration
- ✅ Subscription management
- ✅ Message handling and parsing
- ✅ Connection state management
- ✅ Cleanup on component unmount

### Data Visualization
- ✅ Charts rendering with ECharts
- ✅ Real-time data plotting
- ✅ Resource usage displays
- ✅ Trend visualization

### User Experience
- ✅ Tab navigation and state persistence
- ✅ Filtering and search functionality
- ✅ Export capabilities
- ✅ Alert management interface

## Mock Strategy

The tests use comprehensive mocking approach:

1. **External Dependencies**: ECharts, dayjs, antd notifications
2. **Zenoh Client**: Full mock implementation with subscription tracking
3. **Timers**: Jest fake timers for controlling time-based operations
4. **Data Generation**: Realistic mock data for all system components

## Test Execution Notes

Tests are designed to:
- Run independently without side effects
- Handle async operations properly
- Clean up resources after execution
- Provide meaningful assertions and error messages

## Running Tests

```bash
# Run all monitoring tests
npm test -- --testPathPattern=monitoring

# Run specific test file
npm test -- MonitoringView.test.tsx

# Run with coverage
npm test -- --coverage --testPathPattern=monitoring
```

## Test Configuration

The tests require the following Jest configuration:
- jsdom environment for DOM testing
- React testing library setup
- Mock implementations for external dependencies
- Proper TypeScript and JSX handling

This comprehensive test suite ensures the monitoring interface is robust, reliable, and provides excellent user experience for system monitoring and logging functionality.