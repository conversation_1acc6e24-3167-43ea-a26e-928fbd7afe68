import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import '@testing-library/jest-dom';
import ResourceMonitor from '../ResourceMonitor';

// Mock ECharts component
jest.mock('echarts-for-react', () => {
  return jest.fn(({ option, style }) => (
    <div data-testid="echarts-mock" style={style} data-option={JSON.stringify(option)} />
  ));
});

// Mock Zenoh Client
const mockZenohClient = {
  subscribe: jest.fn(),
  unsubscribe: jest.fn(),
  sendCommand: jest.fn(),
  isConnected: jest.fn(() => true),
};

// Mock resource alert callback
const mockOnResourceAlert = jest.fn();

describe('ResourceMonitor', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  it('renders without crashing', () => {
    render(<ResourceMonitor />);
    expect(screen.getByText('System Resource Monitor')).toBeInTheDocument();
  });

  it('displays all resource cards', () => {
    render(<ResourceMonitor />);
    
    expect(screen.getByText('CPU Usage')).toBeInTheDocument();
    expect(screen.getByText('Memory Usage')).toBeInTheDocument();
    expect(screen.getByText('Disk Usage')).toBeInTheDocument();
    expect(screen.getByText('Network I/O')).toBeInTheDocument();
    expect(screen.getByText('GPU Usage')).toBeInTheDocument();
  });

  it('displays resource usage values and units', () => {
    render(<ResourceMonitor />);
    
    // Should show percentage values for most resources
    const percentageElements = screen.getAllByText(/%$/);
    expect(percentageElements.length).toBeGreaterThan(0);
    
    // Should show MB/s for network
    expect(screen.getByText(/MB\/s$/)).toBeInTheDocument();
  });

  it('shows progress bars for each resource', () => {
    render(<ResourceMonitor />);
    
    // Progress bars are rendered as div elements with specific class
    const progressBars = document.querySelectorAll('.ant-progress');
    expect(progressBars.length).toBeGreaterThanOrEqual(5); // 5 main resources
  });

  it('displays resource trend indicators', () => {
    render(<ResourceMonitor />);
    
    // Trend indicators are arrows: ↗, ↘, →
    const badges = document.querySelectorAll('.ant-badge');
    expect(badges.length).toBeGreaterThanOrEqual(5);
  });

  it('shows resource charts', () => {
    render(<ResourceMonitor />);
    
    const charts = screen.getAllByTestId('echarts-mock');
    expect(charts.length).toBeGreaterThanOrEqual(6); // 5 individual + 1 main trend chart
  });

  it('displays resource details', () => {
    render(<ResourceMonitor />);
    
    // Should show average and peak values
    expect(screen.getByText(/Avg:/)).toBeInTheDocument();
    expect(screen.getByText(/Peak:/)).toBeInTheDocument();
    
    // Should show temperature for CPU and GPU
    expect(screen.getAllByText(/°C/).length).toBeGreaterThan(0);
    
    // Should show memory details
    expect(screen.getByText(/Used:/)).toBeInTheDocument();
  });

  it('displays top processes section', () => {
    render(<ResourceMonitor />);
    
    expect(screen.getByText('Top Processes')).toBeInTheDocument();
    expect(screen.getByText('High CPU usage')).toBeInTheDocument();
  });

  it('shows process information', () => {
    render(<ResourceMonitor />);
    
    // Should display process names, PIDs, and resource usage
    expect(screen.getByText('training_service')).toBeInTheDocument();
    expect(screen.getByText('simulation_service')).toBeInTheDocument();
    expect(screen.getByText('PID:')).toBeInTheDocument();
  });

  it('displays resource usage trends chart', () => {
    render(<ResourceMonitor />);
    
    expect(screen.getByText('Resource Usage Trends')).toBeInTheDocument();
    expect(screen.getByText('Real-time monitoring')).toBeInTheDocument();
  });

  it('has time range selector', () => {
    render(<ResourceMonitor />);
    
    expect(screen.getByText('Last hour')).toBeInTheDocument();
  });

  it('changes time range correctly', async () => {
    render(<ResourceMonitor />);
    
    // Find and click the time range selector
    const timeRangeSelect = screen.getByDisplayValue('Last hour');
    fireEvent.mouseDown(timeRangeSelect);
    
    await waitFor(() => {
      fireEvent.click(screen.getByText('Last 5 minutes'));
    });

    expect(screen.getByDisplayValue('Last 5 minutes')).toBeInTheDocument();
  });

  it('toggles auto refresh correctly', () => {
    render(<ResourceMonitor />);
    
    // Find the auto refresh button
    const autoRefreshButton = screen.getByRole('button', { name: /pause auto refresh/i });
    fireEvent.click(autoRefreshButton);
    
    // Button should change to enable auto refresh
    expect(screen.getByRole('button', { name: /enable auto refresh/i })).toBeInTheDocument();
  });

  it('opens settings modal when settings button is clicked', () => {
    render(<ResourceMonitor />);
    
    // Find and click settings button
    const settingsButton = screen.getByRole('button', { name: /settings/i });
    fireEvent.click(settingsButton);
    
    // Settings functionality would be implemented
    expect(settingsButton).toBeInTheDocument();
  });

  it('updates resource data automatically', async () => {
    render(<ResourceMonitor />);
    
    // Fast-forward time to trigger automatic updates
    act(() => {
      jest.advanceTimersByTime(2000); // 2 seconds for resource update
    });

    // Should have updated the resource data
    await waitFor(() => {
      const charts = screen.getAllByTestId('echarts-mock');
      expect(charts.length).toBeGreaterThan(0);
    });
  });

  it('calls alert callback when threshold is exceeded', async () => {
    render(<ResourceMonitor onResourceAlert={mockOnResourceAlert} />);
    
    // Fast-forward time and wait for resource updates that might trigger alerts
    act(() => {
      jest.advanceTimersByTime(10000); // 10 seconds to allow for potential threshold violations
    });

    // Wait for potential alert callbacks
    await waitFor(() => {
      // Alert callback might be called if random values exceed thresholds
      expect(mockOnResourceAlert).toHaveBeenCalledTimes(expect.any(Number));
    }, { timeout: 1000 });
  });

  it('stops auto refresh when disabled', () => {
    render(<ResourceMonitor />);
    
    // Click to disable auto refresh
    const autoRefreshButton = screen.getByRole('button', { name: /pause auto refresh/i });
    fireEvent.click(autoRefreshButton);
    
    // Fast-forward time
    act(() => {
      jest.advanceTimersByTime(5000);
    });
    
    // Auto refresh should be paused
    expect(screen.getByRole('button', { name: /enable auto refresh/i })).toBeInTheDocument();
  });

  it('displays resource status indicators correctly', () => {
    render(<ResourceMonitor />);
    
    // Should show status badges for each resource
    const statusBadges = document.querySelectorAll('.ant-badge-status-success, .ant-badge-status-warning, .ant-badge-status-error');
    expect(statusBadges.length).toBeGreaterThanOrEqual(5);
  });

  it('shows resource alerts when thresholds are exceeded', async () => {
    render(<ResourceMonitor />);
    
    // Let the component run and potentially generate alert conditions
    act(() => {
      jest.advanceTimersByTime(5000);
    });

    await waitFor(() => {
      // Check if resource alerts are displayed when thresholds are exceeded
      const alertElements = screen.queryAllByText(/Resource Alert/);
      // Alerts may or may not be present depending on random values
      expect(alertElements.length).toBeGreaterThanOrEqual(0);
    });
  });

  it('formats resource values correctly', () => {
    render(<ResourceMonitor />);
    
    // Check that percentage values are properly formatted
    const resourceCards = document.querySelectorAll('.ant-card');
    expect(resourceCards.length).toBeGreaterThanOrEqual(5);
    
    // Values should be displayed with proper units
    expect(screen.getByText(/\d+\.\d+%/)).toBeInTheDocument(); // Percentage format
    expect(screen.getByText(/\d+\.\d+MB\/s/)).toBeInTheDocument(); // Network format
  });

  it('displays memory usage in GB correctly', () => {
    render(<ResourceMonitor />);
    
    // Should show memory usage in GB format
    expect(screen.getByText(/Used: \d+\.\d+GB/)).toBeInTheDocument();
  });

  it('shows process CPU usage with color coding', () => {
    render(<ResourceMonitor />);
    
    // Process CPU usage should be color coded based on usage level
    const processElements = document.querySelectorAll('.ant-list-item');
    expect(processElements.length).toBeGreaterThan(0);
  });

  it('updates process data periodically', async () => {
    render(<ResourceMonitor />);
    
    const initialProcessCount = screen.getAllByText(/PID:/).length;
    
    // Fast-forward time to trigger process updates
    act(() => {
      jest.advanceTimersByTime(5000);
    });

    await waitFor(() => {
      // Process data should still be displayed
      const updatedProcessCount = screen.getAllByText(/PID:/).length;
      expect(updatedProcessCount).toBeGreaterThanOrEqual(initialProcessCount);
    });
  });

  it('handles network resource with unlimited scale', () => {
    render(<ResourceMonitor />);
    
    // Network resource can exceed 100%, unlike other resources
    // The component should handle this correctly
    const networkCard = screen.getByText('Network I/O').closest('.ant-card');
    expect(networkCard).toBeInTheDocument();
  });

  it('maintains resource history for charts', async () => {
    render(<ResourceMonitor />);
    
    // Let the component run to build up history
    act(() => {
      jest.advanceTimersByTime(120000); // 2 minutes
    });

    await waitFor(() => {
      // Charts should have historical data
      const charts = screen.getAllByTestId('echarts-mock');
      charts.forEach(chart => {
        const option = JSON.parse(chart.getAttribute('data-option') || '{}');
        expect(option).toBeDefined();
      });
    });
  });

  it('displays temperature information for hardware components', () => {
    render(<ResourceMonitor />);
    
    // Should show temperature for CPU and GPU
    const temperatureElements = screen.getAllByText(/\d+°C/);
    expect(temperatureElements.length).toBeGreaterThanOrEqual(2); // CPU and GPU temperatures
  });

  it('shows correct chart colors based on status', () => {
    render(<ResourceMonitor />);
    
    // Charts should use different colors based on resource status
    const charts = screen.getAllByTestId('echarts-mock');
    charts.forEach(chart => {
      const option = JSON.parse(chart.getAttribute('data-option') || '{}');
      if (option.series && option.series[0]) {
        expect(option.series[0].lineStyle).toBeDefined();
      }
    });
  });

  it('handles component unmounting gracefully', () => {
    const { unmount } = render(<ResourceMonitor />);
    
    // Should not throw errors when unmounting
    expect(() => unmount()).not.toThrow();
  });

  it('initializes with proper default state', () => {
    render(<ResourceMonitor />);
    
    // Should start with auto refresh enabled
    expect(screen.getByRole('button', { name: /pause auto refresh/i })).toBeInTheDocument();
    
    // Should start with 1 hour time range
    expect(screen.getByDisplayValue('Last hour')).toBeInTheDocument();
  });
});