import React, { useState, useEffect, useCallback } from 'react';
import { Row, Col, Card, Typography, Space, Progress, Statistic, Alert, Badge, Tooltip, Button, Select } from 'antd';
import {
  MonitorOutlined,
  HddOutlined,
  WifiOutlined,
  <PERSON>boltOutlined,
  FireOutlined,
  DashboardOutlined,
  ReloadOutlined,
  SettingOutlined
} from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';

const { Title, Text } = Typography;
const { Option } = Select;

interface ResourceMonitorProps {
  zenohClient?: any;
  onResourceAlert?: (resource: string, value: number, threshold: number) => void;
}

interface SystemResource {
  name: string;
  current: number;
  average: number;
  peak: number;
  threshold: number;
  unit: string;
  status: 'normal' | 'warning' | 'critical';
  trend: 'up' | 'down' | 'stable';
  details: {
    total?: number;
    available?: number;
    used?: number;
    processes?: number;
    temperature?: number;
  };
}

interface ProcessInfo {
  pid: number;
  name: string;
  cpu: number;
  memory: number;
  user: string;
}

const ResourceMonitor: React.FC<ResourceMonitorProps> = ({ zenohClient, onResourceAlert }) => {
  const [resources, setResources] = useState<Record<string, SystemResource>>({});
  const [topProcesses, setTopProcesses] = useState<ProcessInfo[]>([]);
  const [selectedTimeRange, setSelectedTimeRange] = useState<string>('1h');
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [resourceHistory, setResourceHistory] = useState<Record<string, number[]>>({});

  useEffect(() => {
    initializeResourceMonitoring();
    if (autoRefresh) {
      const interval = setInterval(updateResourceData, 2000);
      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  const initializeResourceMonitoring = useCallback(() => {
    // Initialize with mock system resource data
    const initialResources: Record<string, SystemResource> = {
      cpu: {
        name: 'CPU Usage',
        current: 45.2,
        average: 38.7,
        peak: 87.3,
        threshold: 80,
        unit: '%',
        status: 'normal',
        trend: 'stable',
        details: {
          processes: 156,
          temperature: 58
        }
      },
      memory: {
        name: 'Memory Usage',
        current: 62.1,
        average: 58.3,
        peak: 89.2,
        threshold: 85,
        unit: '%',
        status: 'normal',
        trend: 'up',
        details: {
          total: 16384,
          used: 10178,
          available: 6206
        }
      },
      disk: {
        name: 'Disk Usage',
        current: 73.4,
        average: 71.2,
        peak: 78.9,
        threshold: 90,
        unit: '%',
        status: 'normal',
        trend: 'stable',
        details: {
          total: 1024,
          used: 751,
          available: 273
        }
      },
      network: {
        name: 'Network I/O',
        current: 12.8,
        average: 15.3,
        peak: 45.7,
        threshold: 80,
        unit: 'MB/s',
        status: 'normal',
        trend: 'down',
        details: {}
      },
      gpu: {
        name: 'GPU Usage',
        current: 23.5,
        average: 31.2,
        peak: 95.1,
        threshold: 90,
        unit: '%',
        status: 'normal',
        trend: 'stable',
        details: {
          temperature: 52,
          total: 8192,
          used: 1925
        }
      }
    };

    setResources(initialResources);

    // Initialize process data
    const mockProcesses: ProcessInfo[] = [
      { pid: 1234, name: 'training_service', cpu: 15.2, memory: 2048, user: 'admin' },
      { pid: 5678, name: 'simulation_service', cpu: 12.8, memory: 1536, user: 'admin' },
      { pid: 9012, name: 'python', cpu: 8.5, memory: 1024, user: 'admin' },
      { pid: 3456, name: 'zenoh_router', cpu: 3.2, memory: 256, user: 'admin' },
      { pid: 7890, name: 'nginx', cpu: 1.1, memory: 128, user: 'www-data' }
    ];

    setTopProcesses(mockProcesses);

    // Initialize history data
    const history: Record<string, number[]> = {};
    Object.keys(initialResources).forEach(key => {
      history[key] = Array.from({ length: 60 }, () => 
        initialResources[key].current + (Math.random() - 0.5) * 20
      );
    });
    setResourceHistory(history);
  }, []);

  const updateResourceData = useCallback(() => {
    setResources(prev => {
      const updated = { ...prev };
      
      Object.keys(updated).forEach(key => {
        const resource = updated[key];
        
        // Simulate resource value changes
        const change = (Math.random() - 0.5) * 10;
        let newValue = Math.max(0, Math.min(100, resource.current + change));
        
        // Special handling for network (can be higher than 100)
        if (key === 'network') {
          newValue = Math.max(0, resource.current + change);
        }
        
        // Update status based on thresholds
        let status: 'normal' | 'warning' | 'critical' = 'normal';
        if (newValue > resource.threshold * 0.9) {
          status = 'warning';
        }
        if (newValue > resource.threshold) {
          status = 'critical';
          // Trigger alert callback if provided
          if (onResourceAlert) {
            onResourceAlert(resource.name, newValue, resource.threshold);
          }
        }
        
        // Determine trend
        let trend: 'up' | 'down' | 'stable' = 'stable';
        const diff = newValue - resource.current;
        if (Math.abs(diff) > 2) {
          trend = diff > 0 ? 'up' : 'down';
        }
        
        // Update peak if necessary
        const peak = Math.max(resource.peak, newValue);
        
        updated[key] = {
          ...resource,
          current: Number(newValue.toFixed(1)),
          peak,
          status,
          trend
        };
      });
      
      return updated;
    });

    // Update resource history for charts
    setResourceHistory(prev => {
      const updated = { ...prev };
      Object.keys(resources).forEach(key => {
        if (updated[key]) {
          updated[key] = [...updated[key].slice(-59), resources[key].current];
        }
      });
      return updated;
    });

    // Update process data occasionally
    if (Math.random() < 0.3) {
      setTopProcesses(prev => prev.map(process => ({
        ...process,
        cpu: Math.max(0, process.cpu + (Math.random() - 0.5) * 5),
        memory: Math.max(0, process.memory + (Math.random() - 0.5) * 200)
      })).sort((a, b) => b.cpu - a.cpu));
    }
  }, [resources, onResourceAlert]);

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'critical': return '#ff4d4f';
      case 'warning': return '#faad14';
      default: return '#52c41a';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return '↗';
      case 'down': return '↘';
      default: return '→';
    }
  };

  const getResourceChart = (resourceKey: string) => {
    const data = resourceHistory[resourceKey] || [];
    const resource = resources[resourceKey];
    
    return {
      grid: { top: 10, left: 10, right: 10, bottom: 10 },
      xAxis: {
        type: 'category',
        data: Array.from({ length: data.length }, (_, i) => ''),
        axisLabel: { show: false },
        axisLine: { show: false },
        axisTick: { show: false }
      },
      yAxis: {
        type: 'value',
        max: resourceKey === 'network' ? undefined : 100,
        axisLabel: { show: false },
        axisLine: { show: false },
        axisTick: { show: false },
        splitLine: { show: false }
      },
      series: [{
        type: 'line',
        data: data,
        smooth: true,
        symbol: 'none',
        lineStyle: {
          color: getStatusColor(resource?.status || 'normal'),
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [{
              offset: 0, color: getStatusColor(resource?.status || 'normal') + '40'
            }, {
              offset: 1, color: getStatusColor(resource?.status || 'normal') + '00'
            }]
          }
        }
      }]
    };
  };

  const resourceCards = Object.entries(resources).map(([key, resource]) => {
    const icon = {
      cpu: <ThunderboltOutlined />,
      memory: <MonitorOutlined />,
      disk: <HddOutlined />,
      network: <WifiOutlined />,
      gpu: <MonitorOutlined />
    }[key] || <DashboardOutlined />;

    return (
      <Col xs={24} sm={12} lg={8} xl={6} key={key}>
        <Card size="small" style={{ height: 200 }}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Space>
                {icon}
                <Text strong>{resource.name}</Text>
              </Space>
              <Badge 
                status={resource.status === 'critical' ? 'error' : resource.status === 'warning' ? 'warning' : 'success'} 
                text={getTrendIcon(resource.trend)}
              />
            </div>
            
            <div style={{ textAlign: 'center' }}>
              <div style={{ 
                fontSize: '24px', 
                fontWeight: 'bold', 
                color: getStatusColor(resource.status)
              }}>
                {resource.current}{resource.unit}
              </div>
              <Progress
                percent={key === 'network' ? Math.min(100, (resource.current / resource.threshold) * 100) : resource.current}
                strokeColor={getStatusColor(resource.status)}
                size="small"
                showInfo={false}
              />
            </div>
            
            <div style={{ height: 50 }}>
              <ReactECharts
                option={getResourceChart(key)}
                style={{ height: '100%', width: '100%' }}
                notMerge={true}
                lazyUpdate={true}
              />
            </div>
            
            <div style={{ fontSize: '12px', color: '#666' }}>
              <div>Avg: {resource.average}{resource.unit} | Peak: {resource.peak.toFixed(1)}{resource.unit}</div>
              {resource.details.temperature && (
                <div><FireOutlined /> {resource.details.temperature}°C</div>
              )}
              {resource.details.processes && (
                <div>Processes: {resource.details.processes}</div>
              )}
              {resource.details.total && resource.details.used && (
                <div>
                  Used: {(resource.details.used / 1024).toFixed(1)}GB / {(resource.details.total / 1024).toFixed(1)}GB
                </div>
              )}
            </div>
          </Space>
        </Card>
      </Col>
    );
  });

  const processColumns = [
    {
      title: 'PID',
      dataIndex: 'pid',
      key: 'pid',
      width: 80
    },
    {
      title: 'Process Name',
      dataIndex: 'name',
      key: 'name',
      width: 150
    },
    {
      title: 'CPU %',
      dataIndex: 'cpu',
      key: 'cpu',
      width: 80,
      render: (cpu: number) => (
        <span style={{ color: cpu > 20 ? '#ff4d4f' : cpu > 10 ? '#faad14' : '#52c41a' }}>
          {cpu.toFixed(1)}%
        </span>
      ),
      sorter: (a: ProcessInfo, b: ProcessInfo) => b.cpu - a.cpu
    },
    {
      title: 'Memory',
      dataIndex: 'memory',
      key: 'memory',
      width: 100,
      render: (memory: number) => `${memory}MB`
    },
    {
      title: 'User',
      dataIndex: 'user',
      key: 'user',
      width: 100
    }
  ];

  return (
    <div>
      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
        <Col span={24}>
          <Card 
            title={
              <Space>
                <MonitorOutlined />
                <span>System Resource Monitor</span>
              </Space>
            }
            extra={
              <Space>
                <Select 
                  value={selectedTimeRange} 
                  onChange={setSelectedTimeRange}
                  size="small"
                >
                  <Option value="5m">Last 5 minutes</Option>
                  <Option value="1h">Last hour</Option>
                  <Option value="6h">Last 6 hours</Option>
                  <Option value="24h">Last 24 hours</Option>
                </Select>
                <Tooltip title={autoRefresh ? 'Pause auto refresh' : 'Enable auto refresh'}>
                  <Button
                    size="small"
                    type={autoRefresh ? 'primary' : 'default'}
                    icon={<ReloadOutlined spin={autoRefresh} />}
                    onClick={() => setAutoRefresh(!autoRefresh)}
                  />
                </Tooltip>
                <Button
                  size="small"
                  icon={<SettingOutlined />}
                  onClick={() => {
                    // Open settings modal
                  }}
                />
              </Space>
            }
          >
            <Row gutter={[16, 16]}>
              {resourceCards}
            </Row>
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        <Col xs={24} lg={14}>
          <Card 
            title="Resource Usage Trends" 
            size="small"
            extra={
              <Text type="secondary">Real-time monitoring</Text>
            }
          >
            <ReactECharts
              option={{
                grid: { top: 30, left: 50, right: 30, bottom: 30 },
                tooltip: {
                  trigger: 'axis',
                  backgroundColor: 'rgba(0,0,0,0.8)',
                  textStyle: { color: '#fff' }
                },
                legend: {
                  data: Object.keys(resources).map(key => resources[key].name)
                },
                xAxis: {
                  type: 'category',
                  data: Array.from({ length: 60 }, (_, i) => `${59-i}s ago`).reverse()
                },
                yAxis: [
                  {
                    type: 'value',
                    name: 'Usage (%)',
                    max: 100,
                    axisLabel: { formatter: '{value}%' }
                  },
                  {
                    type: 'value',
                    name: 'Network (MB/s)',
                    axisLabel: { formatter: '{value}MB/s' }
                  }
                ],
                series: Object.entries(resources).map(([key, resource], index) => ({
                  name: resource.name,
                  type: 'line',
                  data: resourceHistory[key] || [],
                  smooth: true,
                  yAxisIndex: key === 'network' ? 1 : 0,
                  lineStyle: { 
                    color: getStatusColor(resource.status),
                    width: 2 
                  }
                }))
              }}
              style={{ height: '300px', width: '100%' }}
              notMerge={true}
              lazyUpdate={true}
            />
          </Card>
        </Col>

        <Col xs={24} lg={10}>
          <Card 
            title="Top Processes" 
            size="small"
            extra={
              <Badge count={topProcesses.filter(p => p.cpu > 10).length} showZero={false}>
                <Text type="secondary">High CPU usage</Text>
              </Badge>
            }
          >
            <div style={{ maxHeight: 300, overflowY: 'auto' }}>
              {topProcesses.map((process, index) => (
                <div key={process.pid} style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: '8px 0',
                  borderBottom: index < topProcesses.length - 1 ? '1px solid #f0f0f0' : 'none'
                }}>
                  <div>
                    <Text strong>{process.name}</Text>
                    <br />
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      PID: {process.pid} | {process.user}
                    </Text>
                  </div>
                  <div style={{ textAlign: 'right' }}>
                    <div style={{ 
                      color: process.cpu > 20 ? '#ff4d4f' : process.cpu > 10 ? '#faad14' : '#52c41a',
                      fontWeight: 'bold'
                    }}>
                      {process.cpu.toFixed(1)}%
                    </div>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {process.memory}MB
                    </Text>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </Col>
      </Row>

      {/* Resource Alerts */}
      {Object.values(resources).some(r => r.status !== 'normal') && (
        <Row style={{ marginTop: 16 }}>
          <Col span={24}>
            <Alert
              message="Resource Alert"
              description={
                <Space direction="vertical">
                  {Object.entries(resources)
                    .filter(([_, resource]) => resource.status !== 'normal')
                    .map(([key, resource]) => (
                      <div key={key}>
                        <Text strong>{resource.name}</Text>: {resource.current}{resource.unit} 
                        ({resource.status === 'critical' ? 'Critical' : 'Warning'} - threshold: {resource.threshold}{resource.unit})
                      </div>
                    ))
                  }
                </Space>
              }
              type={Object.values(resources).some(r => r.status === 'critical') ? 'error' : 'warning'}
              showIcon
              closable
            />
          </Col>
        </Row>
      )}
    </div>
  );
};

export default ResourceMonitor;