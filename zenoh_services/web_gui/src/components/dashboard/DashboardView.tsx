import React from 'react';
import { Row, Col, Card, Statistic, Progress, Typography, Space, Timeline, List, Button, Tag, Divider, Alert } from 'antd';
import { useTranslation } from 'react-i18next';
import ReactECharts from 'echarts-for-react';
import {
  RocketOutlined,
  RobotOutlined,
  CloudUploadOutlined,
  MonitorOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  DashboardOutlined,
  TrophyOutlined,
  <PERSON>boltOutlined,
  DatabaseOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  ReloadOutlined,
  SettingOutlined,
  LineChartOutlined,
  BarChartOutlined,
  PieChartOutlined,
} from '@ant-design/icons';

const { Title, Text } = Typography;

interface DashboardViewProps {}

const DashboardView: React.FC<DashboardViewProps> = () => {
  const { t } = useTranslation();
  // Enhanced mock data - will be replaced with real data from Zenoh
  const systemStats = {
    trainingSessions: 12,
    modelsDeployed: 8,
    simulationsRunning: 3,
    uptime: 99.2,
    totalRewards: 15420.5,
    avgSuccessRate: 87.3,
    activeEnvironments: 5,
    totalEpisodes: 45600,
  };

  // Performance metrics for charts
  const performanceData = {
    trainingProgress: [
      { episode: 0, reward: 0, success_rate: 0 },
      { episode: 1000, reward: 150, success_rate: 15 },
      { episode: 2000, reward: 380, success_rate: 35 },
      { episode: 3000, reward: 620, success_rate: 58 },
      { episode: 4000, reward: 850, success_rate: 72 },
      { episode: 5000, reward: 1050, success_rate: 84 },
      { episode: 6000, reward: 1200, success_rate: 87 },
    ],
    systemMetrics: {
      cpu: [45, 52, 48, 61, 55, 67, 59, 63, 58, 71, 65, 69],
      gpu: [78, 82, 85, 79, 88, 91, 86, 89, 92, 87, 90, 85],
      memory: [62, 65, 68, 71, 69, 73, 75, 72, 76, 78, 74, 77],
    }
  };

  const recentActivities = [
    {
      icon: <RocketOutlined style={{ color: '#52c41a' }} />,
      title: '训练完成: anymal_c_rough_terrain',
      timestamp: '2分钟前',
      type: 'success'
    },
    {
      icon: <RobotOutlined style={{ color: '#1890ff' }} />,
      title: '仿真启动: flat_terrain_test',
      timestamp: '5分钟前',
      type: 'info'
    },
    {
      icon: <CloudUploadOutlined style={{ color: '#faad14' }} />,
      title: '模型部署进行中',
      timestamp: '10分钟前',
      type: 'warning'
    },
    {
      icon: <MonitorOutlined style={{ color: '#52c41a' }} />,
      title: '所有服务运行正常',
      timestamp: '15分钟前',
      type: 'success'
    },
  ];

  const serviceStatuses = [
    { name: '训练服务', status: 'online', uptime: '99.8%' },
    { name: '仿真服务', status: 'online', uptime: '99.5%' },
    { name: '部署服务', status: 'online', uptime: '98.9%' },
    { name: '配置服务', status: 'online', uptime: '99.9%' },
    { name: '播放服务', status: 'maintenance', uptime: '95.2%' },
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'maintenance':
        return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;
      default:
        return <ClockCircleOutlined style={{ color: '#f5222d' }} />;
    }
  };

  // Chart configurations
  const getTrainingProgressChart = () => ({
    title: {
      text: '训练进度',
      textStyle: { color: '#ffffff', fontSize: 16, fontWeight: 'bold' }
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0,0,0,0.8)',
      borderColor: '#1890ff',
      textStyle: { color: '#ffffff' }
    },
    legend: {
      data: ['奖励值', '成功率'],
      textStyle: { color: '#ffffff' }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: performanceData.trainingProgress.map(d => d.episode),
      axisLine: { lineStyle: { color: '#ffffff' } },
      axisLabel: { color: '#ffffff' }
    },
    yAxis: [
      {
        type: 'value',
        name: '奖励值',
        axisLine: { lineStyle: { color: '#ffffff' } },
        axisLabel: { color: '#ffffff' },
        nameTextStyle: { color: '#ffffff' }
      },
      {
        type: 'value',
        name: '成功率 (%)',
        axisLine: { lineStyle: { color: '#ffffff' } },
        axisLabel: { color: '#ffffff' },
        nameTextStyle: { color: '#ffffff' }
      }
    ],
    series: [
      {
        name: '奖励值',
        type: 'line',
        data: performanceData.trainingProgress.map(d => d.reward),
        smooth: true,
        lineStyle: { color: '#1890ff', width: 3 },
        itemStyle: { color: '#1890ff' }
      },
      {
        name: '成功率',
        type: 'line',
        yAxisIndex: 1,
        data: performanceData.trainingProgress.map(d => d.success_rate),
        smooth: true,
        lineStyle: { color: '#52c41a', width: 3 },
        itemStyle: { color: '#52c41a' }
      }
    ]
  });

  const getSystemMetricsChart = () => ({
    title: {
      text: '系统指标 (最近12小时)',
      textStyle: { color: '#ffffff', fontSize: 16, fontWeight: 'bold' }
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0,0,0,0.8)',
      borderColor: '#1890ff',
      textStyle: { color: '#ffffff' }
    },
    legend: {
      data: ['CPU使用率', 'GPU使用率', '内存使用率'],
      textStyle: { color: '#ffffff' }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: Array.from({length: 12}, (_, i) => `${i+1}h`),
      axisLine: { lineStyle: { color: '#ffffff' } },
      axisLabel: { color: '#ffffff' }
    },
    yAxis: {
      type: 'value',
      max: 100,
      axisLine: { lineStyle: { color: '#ffffff' } },
      axisLabel: { color: '#ffffff', formatter: '{value}%' }
    },
    series: [
      {
        name: 'CPU使用率',
        type: 'line',
        data: performanceData.systemMetrics.cpu,
        smooth: true,
        lineStyle: { color: '#faad14', width: 2 },
        areaStyle: { color: 'rgba(250, 173, 20, 0.1)' }
      },
      {
        name: 'GPU使用率',
        type: 'line',
        data: performanceData.systemMetrics.gpu,
        smooth: true,
        lineStyle: { color: '#f5222d', width: 2 },
        areaStyle: { color: 'rgba(245, 34, 45, 0.1)' }
      },
      {
        name: '内存使用率',
        type: 'line',
        data: performanceData.systemMetrics.memory,
        smooth: true,
        lineStyle: { color: '#722ed1', width: 2 },
        areaStyle: { color: 'rgba(114, 46, 209, 0.1)' }
      }
    ]
  });

  return (
    <div style={{ background: 'transparent' }}>
      {/* Header Section */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '32px'
      }}>
        <div>
          <Title level={1} style={{
            margin: 0,
            color: '#ffffff',
            fontSize: '32px',
            fontWeight: 700,
            display: 'flex',
            alignItems: 'center',
            gap: '16px'
          }}>
            <DashboardOutlined style={{ color: '#1890ff' }} />
            系统仪表盘
          </Title>
          <Text style={{
            color: 'rgba(255,255,255,0.6)',
            fontSize: '16px',
            marginTop: '8px',
            display: 'block'
          }}>
            EngineAI足式机器人平台实时监控和控制中心
          </Text>
        </div>
        <Space size="middle">
          <Button type="primary" icon={<PlayCircleOutlined />} size="large">
            开始训练
          </Button>
          <Button icon={<ReloadOutlined />} size="large">
            刷新
          </Button>
          <Button icon={<SettingOutlined />} size="large">
            设置
          </Button>
        </Space>
      </div>

      {/* Quick Status Alert */}
      <Alert
        message="系统状态：所有服务运行正常"
        description="最后更新：2分钟前 • 下次维护：计划于周日凌晨2:00"
        type="success"
        showIcon
        style={{
          marginBottom: '32px',
          background: 'rgba(82, 196, 26, 0.1)',
          border: '1px solid rgba(82, 196, 26, 0.3)',
          borderRadius: '8px'
        }}
      />

      {/* Enhanced System Overview Cards */}
      <Row gutter={[32, 32]} style={{ marginBottom: '40px' }}>
        <Col xs={24} sm={12} lg={6}>
          <Card
            className="dashboard-card"
            style={{
              background: 'linear-gradient(135deg, rgba(24,144,255,0.1) 0%, rgba(24,144,255,0.05) 100%)',
              border: '1px solid rgba(24,144,255,0.2)',
              borderRadius: '12px',
              height: '160px'
            }}
          >
            <div style={{ textAlign: 'center', padding: '20px 0' }}>
              <RocketOutlined style={{ fontSize: '32px', color: '#1890ff', marginBottom: '16px' }} />
              <div style={{ fontSize: '36px', fontWeight: 700, color: '#ffffff', marginBottom: '8px' }}>
                {systemStats.trainingSessions}
              </div>
              <div style={{ fontSize: '14px', color: 'rgba(255,255,255,0.7)', textTransform: 'uppercase', letterSpacing: '1px' }}>
                {t('dashboard.trainingSessions')}
              </div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card
            className="dashboard-card"
            style={{
              background: 'linear-gradient(135deg, rgba(82,196,26,0.1) 0%, rgba(82,196,26,0.05) 100%)',
              border: '1px solid rgba(82,196,26,0.2)',
              borderRadius: '12px',
              height: '160px'
            }}
          >
            <div style={{ textAlign: 'center', padding: '20px 0' }}>
              <TrophyOutlined style={{ fontSize: '32px', color: '#52c41a', marginBottom: '16px' }} />
              <div style={{ fontSize: '36px', fontWeight: 700, color: '#ffffff', marginBottom: '8px' }}>
                {systemStats.modelsDeployed}
              </div>
              <div style={{ fontSize: '14px', color: 'rgba(255,255,255,0.7)', textTransform: 'uppercase', letterSpacing: '1px' }}>
                {t('dashboard.modelsDeployed')}
              </div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card
            className="dashboard-card"
            style={{
              background: 'linear-gradient(135deg, rgba(250,173,20,0.1) 0%, rgba(250,173,20,0.05) 100%)',
              border: '1px solid rgba(250,173,20,0.2)',
              borderRadius: '12px',
              height: '160px'
            }}
          >
            <div style={{ textAlign: 'center', padding: '20px 0' }}>
              <ThunderboltOutlined style={{ fontSize: '32px', color: '#faad14', marginBottom: '16px' }} />
              <div style={{ fontSize: '36px', fontWeight: 700, color: '#ffffff', marginBottom: '8px' }}>
                {systemStats.simulationsRunning}
              </div>
              <div style={{ fontSize: '14px', color: 'rgba(255,255,255,0.7)', textTransform: 'uppercase', letterSpacing: '1px' }}>
                {t('dashboard.activeSimulations')}
              </div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card
            className="dashboard-card"
            style={{
              background: 'linear-gradient(135deg, rgba(114,46,209,0.1) 0%, rgba(114,46,209,0.05) 100%)',
              border: '1px solid rgba(114,46,209,0.2)',
              borderRadius: '12px',
              height: '160px'
            }}
          >
            <div style={{ textAlign: 'center', padding: '20px 0' }}>
              <DatabaseOutlined style={{ fontSize: '32px', color: '#722ed1', marginBottom: '16px' }} />
              <div style={{ fontSize: '36px', fontWeight: 700, color: '#ffffff', marginBottom: '8px' }}>
                {systemStats.uptime}%
              </div>
              <div style={{ fontSize: '14px', color: 'rgba(255,255,255,0.7)', textTransform: 'uppercase', letterSpacing: '1px' }}>
                {t('dashboard.systemUptime')}
              </div>
              <Progress
                percent={systemStats.uptime}
                showInfo={false}
                strokeColor="#722ed1"
                style={{ marginTop: '12px' }}
                strokeWidth={6}
              />
            </div>
          </Card>
        </Col>
      </Row>

      {/* Performance Metrics and Charts */}
      <Row gutter={[32, 32]} style={{ marginBottom: '40px' }}>
        <Col xs={24} lg={16}>
          <Card
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                <LineChartOutlined style={{ color: '#1890ff' }} />
                <span>{t('dashboard.trainingProgressChart')}</span>
              </div>
            }
            className="dashboard-card"
            style={{ height: '400px' }}
          >
            <ReactECharts
              option={getTrainingProgressChart()}
              style={{ height: '320px' }}
              theme="dark"
            />
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                <BarChartOutlined style={{ color: '#52c41a' }} />
                <span>{t('dashboard.performanceMetrics')}</span>
              </div>
            }
            className="dashboard-card"
            style={{ height: '400px' }}
          >
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              <div style={{ textAlign: 'center', padding: '20px 0' }}>
                <div style={{ fontSize: '28px', fontWeight: 700, color: '#1890ff', marginBottom: '8px' }}>
                  {systemStats.totalRewards.toLocaleString()}
                </div>
                <div style={{ fontSize: '14px', color: 'rgba(255,255,255,0.7)' }}>
                  累计奖励值
                </div>
              </div>
              <Divider style={{ borderColor: 'rgba(255,255,255,0.1)' }} />
              <div style={{ textAlign: 'center', padding: '20px 0' }}>
                <div style={{ fontSize: '28px', fontWeight: 700, color: '#52c41a', marginBottom: '8px' }}>
                  {systemStats.avgSuccessRate}%
                </div>
                <div style={{ fontSize: '14px', color: 'rgba(255,255,255,0.7)' }}>
                  平均成功率
                </div>
              </div>
              <Divider style={{ borderColor: 'rgba(255,255,255,0.1)' }} />
              <div style={{ textAlign: 'center', padding: '20px 0' }}>
                <div style={{ fontSize: '28px', fontWeight: 700, color: '#faad14', marginBottom: '8px' }}>
                  {systemStats.totalEpisodes.toLocaleString()}
                </div>
                <div style={{ fontSize: '14px', color: 'rgba(255,255,255,0.7)' }}>
                  总训练轮次
                </div>
              </div>
            </Space>
          </Card>
        </Col>
      </Row>

      <Row gutter={[32, 32]}>
        {/* System Metrics Chart */}
        <Col xs={24} lg={16}>
          <Card
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                <PieChartOutlined style={{ color: '#722ed1' }} />
                <span>系统性能</span>
              </div>
            }
            className="dashboard-card"
            style={{ height: '400px' }}
          >
            <ReactECharts
              option={getSystemMetricsChart()}
              style={{ height: '320px' }}
              theme="dark"
            />
          </Card>
        </Col>

        {/* Service Status and Recent Activities */}
        <Col xs={24} lg={8}>
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            {/* Service Status */}
            <Card
              title={
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                  <MonitorOutlined style={{ color: '#52c41a' }} />
                  <span>{t('monitoring.serviceHealth')}</span>
                </div>
              }
              className="dashboard-card"
              style={{ height: '180px' }}
            >
              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                {serviceStatuses.slice(0, 3).map((service, index) => (
                  <div key={index} style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '8px 0'
                  }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                      {getStatusIcon(service.status)}
                      <Text style={{ color: '#ffffff', fontSize: '14px' }}>{service.name}</Text>
                    </div>
                    <Tag color={service.status === 'online' ? 'green' : 'orange'}>
                      {service.uptime}
                    </Tag>
                  </div>
                ))}
              </Space>
            </Card>

            {/* Recent Activities */}
            <Card
              title={
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                  <ClockCircleOutlined style={{ color: '#1890ff' }} />
                  <span>{t('dashboard.recentActivities')}</span>
                </div>
              }
              className="dashboard-card"
              style={{ height: '200px' }}
            >
              <Timeline
                items={recentActivities.slice(0, 3).map((activity, index) => ({
                  dot: activity.icon,
                  children: (
                    <div>
                      <Text style={{ fontSize: '13px', color: '#ffffff', display: 'block' }}>
                        {activity.title}
                      </Text>
                      <Text style={{ fontSize: '11px', color: 'rgba(255,255,255,0.5)' }}>
                        {activity.timestamp}
                      </Text>
                    </div>
                  ),
                }))}
              />
            </Card>
          </Space>
        </Col>
      </Row>
    </div>
  );
};

export default DashboardView;