import React, { useState } from 'react';
import { Layout, Menu, Typography, Badge, Space, Breadcrumb, Dropdown, Avatar, Button } from 'antd';
import { useTranslation } from 'react-i18next';
import {
  DashboardOutlined,
  ExperimentOutlined,
  ControlOutlined,
  SettingOutlined,
  DeploymentUnitOutlined,
  MonitorOutlined,
  RobotOutlined,
  UserOutlined,
  BellOutlined,
  SearchOutlined,
  FullscreenOutlined,
  HomeOutlined,
} from '@ant-design/icons';
import SystemStatusBar from './SystemStatusBar';
import DashboardView from '../dashboard/DashboardView';
import TrainingView from '../training/TrainingView';
import SimulationView from '../simulation/SimulationView';
import DeploymentView from '../deployment/DeploymentView';
import MonitoringView from '../monitoring/MonitoringView';
import { useZenohInitialization } from '../../hooks/useZenohInitialization';

const { Head<PERSON>, Sider, Content } = Layout;
const { Title, Text } = Typography;

interface MainLayoutProps {}

const MainLayout: React.FC<MainLayoutProps> = () => {
  const { t } = useTranslation();
  const [collapsed, setCollapsed] = useState(false);
  const [selectedKey, setSelectedKey] = useState('dashboard');

  // Initialize Zenoh client and connection
  const { isConnected } = useZenohInitialization({
    url: 'ws://localhost:8080', // Web backend WebSocket endpoint
    reconnectInterval: 5000,
    maxReconnectAttempts: 10,
    heartbeatInterval: 30000
  });

  const menuItems = [
    {
      key: 'dashboard',
      icon: <DashboardOutlined />,
      label: t('menu.dashboard'),
      description: '系统概览和状态监控'
    },
    {
      key: 'training',
      icon: <ExperimentOutlined />,
      label: t('menu.training'),
      description: '机器学习模型训练与实验'
    },
    {
      key: 'simulation',
      icon: <RobotOutlined />,
      label: t('menu.simulation'),
      description: '机器人仿真测试环境'
    },
    {
      key: 'deployment',
      icon: <DeploymentUnitOutlined />,
      label: t('menu.deployment'),
      description: '模型发布和管理'
    },
    {
      key: 'monitoring',
      icon: <MonitorOutlined />,
      label: t('menu.monitoring'),
      description: '系统监控和数据分析'
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: t('menu.settings'),
      description: '系统配置和参数设置'
    },
  ];

  const userMenuItems = [
    {
      key: 'profile',
      label: '用户资料',
    },
    {
      key: 'preferences',
      label: '偏好设置',
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      label: '退出登录',
    },
  ];

  const getBreadcrumbItems = () => {
    const items = [
      {
        href: '#',
        title: <HomeOutlined />,
      },
    ];

    const currentItem = menuItems.find(item => item.key === selectedKey);
    if (currentItem) {
      items.push({
        href: '#',
        title: <span>{currentItem.label}</span>,
      });
    }

    return items;
  };

  const renderContent = () => {
    switch (selectedKey) {
      case 'dashboard':
        return <DashboardView />;
      case 'training':
        return <TrainingView />;
      case 'simulation':
        return <SimulationView />;
      case 'deployment':
        return <DeploymentView />;
      case 'monitoring':
        return <MonitoringView />;
      case 'settings':
        return <div>系统设置页面 (即将推出)</div>;
      default:
        return <DashboardView />;
    }
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      {/* Enhanced Sidebar */}
      <Sider
        theme="dark"
        collapsible
        collapsed={collapsed}
        onCollapse={setCollapsed}
        width={280}
        style={{
          background: 'linear-gradient(180deg, #001529 0%, #002140 100%)',
          boxShadow: '2px 0 8px rgba(0,0,0,0.15)'
        }}
      >
        {/* Logo Section */}
        <div style={{
          padding: collapsed ? '24px 16px' : '32px 24px',
          textAlign: 'center',
          borderBottom: '1px solid rgba(255,255,255,0.15)',
          background: 'linear-gradient(135deg, rgba(0,0,0,0.4) 0%, rgba(0,0,0,0.2) 100%)',
          position: 'relative'
        }}>
          {/* Background decoration */}
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'radial-gradient(circle at center, rgba(24,144,255,0.1) 0%, transparent 70%)',
            pointerEvents: 'none'
          }} />

          <Space direction="vertical" size="small" style={{ width: '100%', position: 'relative', zIndex: 1 }}>
            <RobotOutlined
              style={{
                fontSize: collapsed ? '32px' : '48px',
                color: '#1890ff',
                transition: 'all 0.3s ease',
                filter: 'drop-shadow(0 4px 8px rgba(24,144,255,0.4))',
                textShadow: '0 0 20px rgba(24,144,255,0.6)'
              }}
            />
            {!collapsed && (
              <>
                <Title
                  level={2}
                  style={{
                    margin: '8px 0 4px 0',
                    color: '#ffffff',
                    fontWeight: 800,
                    fontSize: '24px',
                    textShadow: '0 2px 4px rgba(0,0,0,0.5)',
                    letterSpacing: '0.5px'
                  }}
                >
                  EngineAI
                </Title>
                <Text style={{
                  color: 'rgba(255,255,255,0.8)',
                  fontSize: '13px',
                  fontWeight: 500,
                  textShadow: '0 1px 2px rgba(0,0,0,0.5)',
                  letterSpacing: '0.3px'
                }}>
                  足式机器人平台
                </Text>
              </>
            )}
          </Space>
        </div>

        {/* Enhanced Menu */}
        <div style={{ marginTop: '20px', padding: '0 12px' }}>
          {menuItems.map(item => (
            <div
              key={item.key}
              onClick={() => setSelectedKey(item.key)}
              className={`custom-menu-item ${selectedKey === item.key ? 'selected' : ''}`}
              style={{
                display: 'flex',
                alignItems: 'center',
                padding: collapsed ? '16px 12px' : '16px 20px',
                margin: '8px 0',
                borderRadius: '12px',
                cursor: 'pointer',
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                background: selectedKey === item.key
                  ? 'linear-gradient(135deg, rgba(24, 144, 255, 0.2) 0%, rgba(24, 144, 255, 0.1) 100%)'
                  : 'transparent',
                border: selectedKey === item.key
                  ? '1px solid rgba(24, 144, 255, 0.3)'
                  : '1px solid transparent',
                boxShadow: selectedKey === item.key
                  ? '0 4px 16px rgba(24, 144, 255, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
                  : 'none',
                position: 'relative',
                overflow: 'hidden'
              }}
              onMouseEnter={(e) => {
                if (selectedKey !== item.key) {
                  e.currentTarget.style.background = 'rgba(24, 144, 255, 0.08)';
                  e.currentTarget.style.transform = 'translateX(4px)';
                  e.currentTarget.style.boxShadow = '0 4px 16px rgba(24, 144, 255, 0.15)';
                }
              }}
              onMouseLeave={(e) => {
                if (selectedKey !== item.key) {
                  e.currentTarget.style.background = 'transparent';
                  e.currentTarget.style.transform = 'translateX(0)';
                  e.currentTarget.style.boxShadow = 'none';
                }
              }}
            >
              {/* Icon */}
              <div style={{
                fontSize: '20px',
                color: selectedKey === item.key ? '#1890ff' : 'rgba(255,255,255,0.8)',
                marginRight: collapsed ? '0' : '16px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                minWidth: '20px',
                transition: 'color 0.3s ease'
              }}>
                {item.icon}
              </div>

              {/* Text Content */}
              {!collapsed && (
                <div style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'flex-start',
                  flex: 1,
                  minWidth: 0
                }}>
                  <span style={{
                    fontSize: '16px',
                    fontWeight: 600,
                    color: selectedKey === item.key ? '#ffffff' : 'rgba(255,255,255,0.9)',
                    lineHeight: '1.2',
                    transition: 'color 0.3s ease'
                  }}>
                    {item.label}
                  </span>
                  <span style={{
                    fontSize: '12px',
                    color: selectedKey === item.key ? 'rgba(255,255,255,0.8)' : 'rgba(255,255,255,0.6)',
                    marginTop: '4px',
                    lineHeight: '1.2',
                    transition: 'color 0.3s ease'
                  }}>
                    {item.description}
                  </span>
                </div>
              )}
            </div>
          ))}
        </div>
      </Sider>

      <Layout>
        {/* Enhanced Header */}
        <Header style={{
          padding: '0 32px',
          background: 'linear-gradient(90deg, #001529 0%, #002140 100%)',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          borderBottom: '1px solid rgba(255,255,255,0.1)',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          height: '72px'
        }}>
          {/* Left Section - Breadcrumb */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '24px' }}>
            <Breadcrumb
              items={getBreadcrumbItems()}
              style={{
                fontSize: '14px',
                color: 'rgba(255,255,255,0.7)'
              }}
            />
            <Title
              level={2}
              style={{
                margin: 0,
                color: 'white',
                fontWeight: 600,
                textTransform: 'capitalize'
              }}
            >
              {selectedKey}
            </Title>
          </div>

          {/* Right Section - Actions and Status */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            <Button
              type="text"
              icon={<SearchOutlined />}
              style={{ color: 'rgba(255,255,255,0.7)' }}
            />
            <Button
              type="text"
              icon={<FullscreenOutlined />}
              style={{ color: 'rgba(255,255,255,0.7)' }}
            />
            <Badge count={3} size="small">
              <Button
                type="text"
                icon={<BellOutlined />}
                style={{ color: 'rgba(255,255,255,0.7)' }}
              />
            </Badge>
            <SystemStatusBar />
            <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                cursor: 'pointer',
                padding: '8px 12px',
                borderRadius: '6px',
                transition: 'background 0.3s',

              }}>
                <Avatar size="small" icon={<UserOutlined />} />
                <Text style={{ color: 'white', fontSize: '14px' }}>管理员</Text>
              </div>
            </Dropdown>
          </div>
        </Header>

        {/* Enhanced Content Area */}
        <Content style={{
          padding: '32px 40px 40px 40px',
          background: '#0a0a0a',
          overflow: 'auto',
          minHeight: 'calc(100vh - 72px)',
          position: 'relative'
        }}>
          <div style={{
            background: 'linear-gradient(135deg, rgba(24,144,255,0.03) 0%, rgba(24,144,255,0.01) 100%)',
            borderRadius: '12px',
            padding: '32px',
            minHeight: 'calc(100vh - 144px)',
            border: '1px solid rgba(255,255,255,0.05)'
          }}>
            {renderContent()}
          </div>
        </Content>
      </Layout>
    </Layout>
  );
};

export default MainLayout;