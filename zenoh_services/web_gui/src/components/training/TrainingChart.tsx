import React, { useState, useEffect } from 'react';
import ReactECharts from 'echarts-for-react';
import { Select, Space, Button } from 'antd';
import { ReloadOutlined, DownloadOutlined } from '@ant-design/icons';
import { useZenohStore } from '../../stores/zenoh-store';

const { Option } = Select;

interface TrainingChartProps {}

interface ChartDataPoint {
  iteration: number;
  reward: number;
  loss: number;
  learningRate: number;
  entropy?: number;
  klDivergence?: number;
  policyLoss?: number;
  valueLoss?: number;
}

const TrainingChart: React.FC<TrainingChartProps> = () => {
  const { trainingMetrics, getHistoricalTrainingData } = useZenohStore();
  const [chartData, setChartData] = useState<ChartDataPoint[]>([]);
  const [selectedMetrics, setSelectedMetrics] = useState<string[]>(['reward', 'loss', 'learningRate']);
  const [timeRange, setTimeRange] = useState<string>('last_100');

  useEffect(() => {
    // Update chart data when new training metrics arrive
    if (trainingMetrics) {
      setChartData(prev => {
        const newDataPoint: ChartDataPoint = {
          iteration: trainingMetrics.iteration,
          reward: trainingMetrics.mean_reward,
          loss: (trainingMetrics.policy_loss + trainingMetrics.value_loss) / 2,
          learningRate: trainingMetrics.learning_rate,
          entropy: trainingMetrics.entropy,
          klDivergence: trainingMetrics.kl_divergence,
          policyLoss: trainingMetrics.policy_loss,
          valueLoss: trainingMetrics.value_loss,
        };

        // Keep only the last N data points based on time range
        const maxPoints = timeRange === 'last_50' ? 50 : 
                         timeRange === 'last_100' ? 100 : 
                         timeRange === 'last_500' ? 500 : 100;

        const updatedData = [...prev, newDataPoint].slice(-maxPoints);
        return updatedData;
      });
    }
  }, [trainingMetrics, timeRange]);

  const handleMetricToggle = (value: string[]) => {
    setSelectedMetrics(value);
  };

  const handleTimeRangeChange = (value: string) => {
    setTimeRange(value);
    // Optionally fetch historical data when range changes
    if (getHistoricalTrainingData) {
      getHistoricalTrainingData(value);
    }
  };

  const handleRefresh = () => {
    // Refresh chart data
    if (getHistoricalTrainingData) {
      getHistoricalTrainingData(timeRange);
    }
  };

  const handleExportData = () => {
    // Export chart data as CSV
    const csvContent = [
      ['Iteration', 'Reward', 'Loss', 'Learning Rate', 'Entropy', 'KL Divergence', 'Policy Loss', 'Value Loss'].join(','),
      ...chartData.map(point => [
        point.iteration,
        point.reward.toFixed(4),
        point.loss.toFixed(4),
        point.learningRate.toFixed(6),
        point.entropy?.toFixed(4) || '',
        point.klDivergence?.toFixed(4) || '',
        point.policyLoss?.toFixed(4) || '',
        point.valueLoss?.toFixed(4) || ''
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `training_metrics_${new Date().toISOString().slice(0, 10)}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const getOption = () => {
    const iterations = chartData.map(point => point.iteration);
    
    const metricConfigs: Record<string, any> = {
      reward: {
        name: 'Mean Reward',
        data: chartData.map(point => point.reward),
        color: '#52c41a',
        yAxisIndex: 0,
        type: 'line'
      },
      loss: {
        name: 'Combined Loss',
        data: chartData.map(point => point.loss),
        color: '#f5222d',
        yAxisIndex: 1,
        type: 'line'
      },
      learningRate: {
        name: 'Learning Rate',
        data: chartData.map(point => point.learningRate),
        color: '#1890ff',
        yAxisIndex: 1,
        type: 'line',
        lineStyle: { type: 'dashed' }
      },
      entropy: {
        name: 'Entropy',
        data: chartData.map(point => point.entropy || 0),
        color: '#722ed1',
        yAxisIndex: 1,
        type: 'line'
      },
      klDivergence: {
        name: 'KL Divergence',
        data: chartData.map(point => point.klDivergence || 0),
        color: '#faad14',
        yAxisIndex: 1,
        type: 'line'
      },
      policyLoss: {
        name: 'Policy Loss',
        data: chartData.map(point => point.policyLoss || 0),
        color: '#eb2f96',
        yAxisIndex: 1,
        type: 'line'
      },
      valueLoss: {
        name: 'Value Loss',
        data: chartData.map(point => point.valueLoss || 0),
        color: '#13c2c2',
        yAxisIndex: 1,
        type: 'line'
      }
    };

    const series = selectedMetrics.map(metric => ({
      ...metricConfigs[metric],
      smooth: true,
      lineStyle: {
        ...metricConfigs[metric].lineStyle,
        color: metricConfigs[metric].color,
        width: 2
      },
      itemStyle: {
        color: metricConfigs[metric].color
      },
      symbol: 'circle',
      symbolSize: 4,
      animation: false
    }));

    return {
      backgroundColor: 'transparent',
      grid: { 
        top: 60, 
        left: 60, 
        right: 60, 
        bottom: 60 
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0,0,0,0.8)',
        borderColor: '#333',
        textStyle: {
          color: '#fff'
        },
        formatter: (params: any) => {
          const iteration = params[0]?.axisValue;
          let content = `<div style="margin-bottom: 8px;"><strong>Iteration: ${iteration}</strong></div>`;
          
          params.forEach((param: any) => {
            const value = typeof param.value === 'number' ? param.value.toFixed(4) : param.value;
            content += `
              <div style="display: flex; align-items: center; margin: 4px 0;">
                <div style="width: 12px; height: 12px; background-color: ${param.color}; margin-right: 8px; border-radius: 50%;"></div>
                <span>${param.seriesName}: ${value}</span>
              </div>
            `;
          });
          
          return content;
        }
      },
      legend: {
        data: selectedMetrics.map(metric => metricConfigs[metric].name),
        textStyle: {
          color: '#fff'
        },
        top: 10
      },
      xAxis: {
        type: 'category',
        data: iterations,
        axisLabel: {
          color: '#fff'
        },
        axisLine: {
          lineStyle: {
            color: '#fff'
          }
        },
        name: 'Iteration',
        nameTextStyle: {
          color: '#fff'
        }
      },
      yAxis: [
        {
          type: 'value',
          name: 'Reward',
          position: 'left',
          axisLabel: {
            color: '#fff'
          },
          axisLine: {
            lineStyle: {
              color: '#fff'
            }
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.1)'
            }
          },
          nameTextStyle: {
            color: '#fff'
          }
        },
        {
          type: 'value',
          name: 'Loss / Metrics',
          position: 'right',
          axisLabel: {
            color: '#fff'
          },
          axisLine: {
            lineStyle: {
              color: '#fff'
            }
          },
          splitLine: {
            show: false
          },
          nameTextStyle: {
            color: '#fff'
          }
        }
      ],
      series
    };
  };

  return (
    <div>
      {/* Chart Controls */}
      <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: '8px' }}>
        <Space>
          <Select
            mode="multiple"
            placeholder="Select metrics"
            value={selectedMetrics}
            onChange={handleMetricToggle}
            style={{ minWidth: 200 }}
            size="small"
          >
            <Option value="reward">Mean Reward</Option>
            <Option value="loss">Combined Loss</Option>
            <Option value="learningRate">Learning Rate</Option>
            <Option value="entropy">Entropy</Option>
            <Option value="klDivergence">KL Divergence</Option>
            <Option value="policyLoss">Policy Loss</Option>
            <Option value="valueLoss">Value Loss</Option>
          </Select>
          
          <Select
            value={timeRange}
            onChange={handleTimeRangeChange}
            style={{ width: 120 }}
            size="small"
          >
            <Option value="last_50">Last 50</Option>
            <Option value="last_100">Last 100</Option>
            <Option value="last_500">Last 500</Option>
            <Option value="all">All Data</Option>
          </Select>
        </Space>

        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={handleRefresh}
            size="small"
            title="Refresh data"
          />
          <Button
            icon={<DownloadOutlined />}
            onClick={handleExportData}
            size="small"
            title="Export as CSV"
            disabled={chartData.length === 0}
          />
        </Space>
      </div>

      {/* Chart */}
      <ReactECharts 
        option={getOption()} 
        style={{ height: '400px', width: '100%' }}
        notMerge={true}
        lazyUpdate={true}
      />

      {/* Data Summary */}
      {chartData.length > 0 && (
        <div style={{ marginTop: '16px', padding: '12px', backgroundColor: 'rgba(255,255,255,0.05)', borderRadius: '4px' }}>
          <Space size="large">
            <span style={{ color: '#fff', fontSize: '12px' }}>
              <strong>Data Points:</strong> {chartData.length}
            </span>
            <span style={{ color: '#fff', fontSize: '12px' }}>
              <strong>Latest Reward:</strong> {chartData[chartData.length - 1]?.reward.toFixed(4) || 'N/A'}
            </span>
            <span style={{ color: '#fff', fontSize: '12px' }}>
              <strong>Best Reward:</strong> {Math.max(...chartData.map(p => p.reward)).toFixed(4)}
            </span>
            <span style={{ color: '#fff', fontSize: '12px' }}>
              <strong>Latest Iteration:</strong> {chartData[chartData.length - 1]?.iteration || 'N/A'}
            </span>
          </Space>
        </div>
      )}
    </div>
  );
};

export default TrainingChart;