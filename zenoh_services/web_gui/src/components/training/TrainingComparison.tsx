import React, { useState, useEffect } from 'react';
import { Card, Select, Button, Space, Table, Tag, Divider } from 'antd';
import { SwapOutlined, BarChartOutlined, DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';
import { useZenohStore } from '../../stores/zenoh-store';

const { Option } = Select;

interface TrainingRun {
  id: string;
  name: string;
  taskName: string;
  startTime: string;
  iterations: number;
  bestReward: number;
  status: string;
  color: string;
}

interface ComparisonMetrics {
  iteration: number;
  runs: {
    [runId: string]: {
      reward: number;
      loss: number;
      learningRate: number;
    };
  };
}

interface TrainingComparisonProps {
  visible?: boolean;
}

const TrainingComparison: React.FC<TrainingComparisonProps> = ({ visible = true }) => {
  const { client, sendTrainingCommand } = useZenohStore();
  const [selectedRuns, setSelectedRuns] = useState<string[]>([]);
  const [availableRuns, setAvailableRuns] = useState<TrainingRun[]>([]);
  const [comparisonData, setComparisonData] = useState<ComparisonMetrics[]>([]);
  const [loading, setLoading] = useState(false);

  // Mock data for available runs
  useEffect(() => {
    const mockRuns: TrainingRun[] = [
      {
        id: 'run_001',
        name: 'Baseline Run',
        taskName: 'anymal_c_rough_terrain',
        startTime: '2024-01-15 10:00:00',
        iterations: 1500,
        bestReward: 195.2,
        status: 'completed',
        color: '#52c41a'
      },
      {
        id: 'run_002',
        name: 'High Learning Rate',
        taskName: 'anymal_c_rough_terrain',
        startTime: '2024-01-16 14:30:00',
        iterations: 1200,
        bestReward: 142.8,
        status: 'completed',
        color: '#1890ff'
      },
      {
        id: 'run_003',
        name: 'More Environments',
        taskName: 'anymal_c_rough_terrain',
        startTime: '2024-01-17 09:15:00',
        iterations: 1800,
        bestReward: 218.5,
        status: 'completed',
        color: '#722ed1'
      },
      {
        id: 'run_004',
        name: 'Current Run',
        taskName: 'anymal_c_rough_terrain',
        startTime: '2024-01-18 11:20:00',
        iterations: 800,
        bestReward: 167.3,
        status: 'running',
        color: '#faad14'
      }
    ];

    setAvailableRuns(mockRuns);
  }, []);

  // Generate mock comparison data when runs are selected
  useEffect(() => {
    if (selectedRuns.length > 0) {
      const data: ComparisonMetrics[] = [];
      const maxIterations = Math.max(...selectedRuns.map(runId => {
        const run = availableRuns.find(r => r.id === runId);
        return run ? run.iterations : 0;
      }));

      for (let i = 0; i <= maxIterations; i += 10) {
        const metrics: ComparisonMetrics = {
          iteration: i,
          runs: {}
        };

        selectedRuns.forEach(runId => {
          const run = availableRuns.find(r => r.id === runId);
          if (run && i <= run.iterations) {
            // Generate realistic training curves
            const progress = i / run.iterations;
            const baseReward = run.bestReward * 0.3;
            const rewardGrowth = run.bestReward * 0.7 * (1 - Math.exp(-progress * 3));
            const noise = (Math.random() - 0.5) * run.bestReward * 0.1;
            
            metrics.runs[runId] = {
              reward: baseReward + rewardGrowth + noise,
              loss: 2 * Math.exp(-progress * 2) + Math.random() * 0.5,
              learningRate: 0.0003 * Math.exp(-progress * 0.5)
            };
          }
        });

        data.push(metrics);
      }

      setComparisonData(data);
    } else {
      setComparisonData([]);
    }
  }, [selectedRuns, availableRuns]);

  const handleRunSelection = (runIds: string[]) => {
    setSelectedRuns(runIds.slice(0, 4)); // Limit to 4 runs for readability
  };

  const handleLoadHistoricalData = async () => {
    setLoading(true);
    // In real implementation, this would fetch data from Zenoh
    if (client) {
      sendTrainingCommand('get_training_runs', { run_ids: selectedRuns });
    }
    
    // Simulate loading delay
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  };

  const getComparisonChart = () => {
    if (comparisonData.length === 0) return null;

    const iterations = comparisonData.map(d => d.iteration);
    const series = selectedRuns.map(runId => {
      const run = availableRuns.find(r => r.id === runId);
      if (!run) return null;

      return {
        name: run.name,
        type: 'line',
        data: comparisonData.map(d => d.runs[runId]?.reward || null),
        smooth: true,
        lineStyle: {
          color: run.color,
          width: 2
        },
        itemStyle: {
          color: run.color
        },
        symbol: 'circle',
        symbolSize: 4
      };
    }).filter(Boolean);

    return {
      backgroundColor: 'transparent',
      title: {
        text: 'Training Reward Comparison',
        textStyle: {
          color: '#fff',
          fontSize: 16
        }
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0,0,0,0.8)',
        borderColor: '#333',
        textStyle: {
          color: '#fff'
        }
      },
      legend: {
        data: selectedRuns.map(runId => {
          const run = availableRuns.find(r => r.id === runId);
          return run ? run.name : '';
        }),
        textStyle: {
          color: '#fff'
        },
        top: 40
      },
      grid: {
        top: 80,
        left: 60,
        right: 60,
        bottom: 60
      },
      xAxis: {
        type: 'category',
        data: iterations,
        name: 'Iteration',
        nameTextStyle: {
          color: '#fff'
        },
        axisLabel: {
          color: '#fff'
        },
        axisLine: {
          lineStyle: {
            color: '#fff'
          }
        }
      },
      yAxis: {
        type: 'value',
        name: 'Mean Reward',
        nameTextStyle: {
          color: '#fff'
        },
        axisLabel: {
          color: '#fff'
        },
        axisLine: {
          lineStyle: {
            color: '#fff'
          }
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255,255,255,0.1)'
          }
        }
      },
      series
    };
  };

  const getComparisonTableData = () => {
    return selectedRuns.map(runId => {
      const run = availableRuns.find(r => r.id === runId);
      if (!run) return null;

      const runData = comparisonData.filter(d => d.runs[runId]);
      const rewards = runData.map(d => d.runs[runId].reward);
      const avgReward = rewards.length > 0 ? rewards.reduce((a, b) => a + b, 0) / rewards.length : 0;
      const finalReward = rewards.length > 0 ? rewards[rewards.length - 1] : 0;

      return {
        key: runId,
        name: run.name,
        taskName: run.taskName,
        iterations: run.iterations,
        bestReward: run.bestReward,
        avgReward: avgReward,
        finalReward: finalReward,
        status: run.status,
        color: run.color
      };
    }).filter(Boolean);
  };

  const comparisonColumns = [
    {
      title: 'Run Name',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: any) => (
        <Space>
          <div
            style={{
              width: 12,
              height: 12,
              backgroundColor: record.color,
              borderRadius: '50%'
            }}
          />
          <span>{text}</span>
        </Space>
      )
    },
    {
      title: 'Task',
      dataIndex: 'taskName',
      key: 'taskName'
    },
    {
      title: 'Iterations',
      dataIndex: 'iterations',
      key: 'iterations'
    },
    {
      title: 'Best Reward',
      dataIndex: 'bestReward',
      key: 'bestReward',
      render: (value: number) => value.toFixed(2)
    },
    {
      title: 'Avg Reward',
      dataIndex: 'avgReward',
      key: 'avgReward',
      render: (value: number) => value.toFixed(2)
    },
    {
      title: 'Final Reward',
      dataIndex: 'finalReward',
      key: 'finalReward',
      render: (value: number) => value.toFixed(2)
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'completed' ? 'green' : status === 'running' ? 'blue' : 'orange'}>
          {status.toUpperCase()}
        </Tag>
      )
    }
  ];

  if (!visible) return null;

  return (
    <div style={{ marginTop: '24px' }}>
      <Card
        title={
          <Space>
            <SwapOutlined />
            <span>Training Run Comparison</span>
          </Space>
        }
        extra={
          <Button
            type="primary"
            icon={<BarChartOutlined />}
            onClick={handleLoadHistoricalData}
            loading={loading}
            disabled={selectedRuns.length === 0}
          >
            Load Data
          </Button>
        }
      >
        {/* Run Selection */}
        <div style={{ marginBottom: '16px' }}>
          <Space>
            <span style={{ color: '#fff' }}>Select runs to compare:</span>
            <Select
              mode="multiple"
              style={{ minWidth: 300 }}
              placeholder="Select training runs"
              value={selectedRuns}
              onChange={handleRunSelection}
            >
              {availableRuns.map(run => (
                <Option key={run.id} value={run.id}>
                  <Space>
                    <div
                      style={{
                        width: 8,
                        height: 8,
                        backgroundColor: run.color,
                        borderRadius: '50%',
                        display: 'inline-block'
                      }}
                    />
                    {run.name} ({run.taskName})
                  </Space>
                </Option>
              ))}
            </Select>
          </Space>
        </div>

        {selectedRuns.length > 0 && (
          <>
            {/* Comparison Chart */}
            <div style={{ marginBottom: '24px' }}>
              <ReactECharts
                option={getComparisonChart()}
                style={{ height: '400px', width: '100%' }}
                notMerge={true}
              />
            </div>

            <Divider />

            {/* Comparison Table */}
            <div>
              <h4 style={{ color: '#fff', marginBottom: '16px' }}>Comparison Summary</h4>
              <Table
                columns={comparisonColumns}
                dataSource={getComparisonTableData()}
                pagination={false}
                size="small"
              />
            </div>
          </>
        )}

        {selectedRuns.length === 0 && (
          <div style={{ textAlign: 'center', padding: '40px', color: '#8c8c8c' }}>
            <BarChartOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
            <div>Select training runs to compare their performance</div>
          </div>
        )}
      </Card>
    </div>
  );
};

export default TrainingComparison;