import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Form, Input, Select, Button, Slider, Typography, Space, Switch, Divider, InputNumber, Tabs, Table, Tag, Modal, notification } from 'antd';
import {
  EnvironmentOutlined,
  SettingOutlined,
  SaveOutlined,
  LoadingOutlined,
  ExperimentOutlined,
  PlayCircleOutlined,
  EyeOutlined,
  CopyOutlined,
  DeleteOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';
import { useZenohStore } from '../../stores/zenoh-store';
import { ZENOH_TOPICS } from '../../types/zenoh-types';

const { Title, Text } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;
const { confirm } = Modal;

interface SimulationConfigurationProps {}

interface TerrainConfig {
  type: string;
  length: number;
  width: number;
  height: number;
  roughness: number;
  slope_angle?: number;
  stair_height?: number;
  stair_width?: number;
  obstacle_density?: number;
  seed?: number;
}

interface PhysicsConfig {
  gravity: number;
  time_step: number;
  substeps: number;
  solver_iterations: number;
  friction_coefficient: number;
  restitution: number;
  air_density: number;
  wind_velocity: [number, number, number];
  enable_gpu_physics: boolean;
}

interface SimulationPreset {
  id: string;
  name: string;
  description: string;
  terrain: TerrainConfig;
  physics: PhysicsConfig;
  created_at: string;
}

interface SimulationMetrics {
  fps: number;
  physics_time: number;
  render_time: number;
  memory_usage: number;
  gpu_utilization: number;
}

const SimulationConfiguration: React.FC<SimulationConfigurationProps> = () => {
  const { client, simulationMetrics, sendCommand } = useZenohStore();
  const [configForm] = Form.useForm();
  const [presetForm] = Form.useForm();
  
  const [activeTab, setActiveTab] = useState<string>('terrain');
  const [presets, setPresets] = useState<SimulationPreset[]>([]);
  const [selectedPreset, setSelectedPreset] = useState<string | null>(null);
  const [isApplying, setIsApplying] = useState(false);
  const [showPresetModal, setShowPresetModal] = useState(false);
  const [realTimeMetrics, setRealTimeMetrics] = useState<SimulationMetrics>({
    fps: 60,
    physics_time: 0.8,
    render_time: 12.5,
    memory_usage: 2048,
    gpu_utilization: 75
  });

  // Default configurations
  const defaultTerrain: TerrainConfig = {
    type: 'rough',
    length: 20.0,
    width: 20.0,
    height: 2.0,
    roughness: 0.1,
    seed: 42
  };

  const defaultPhysics: PhysicsConfig = {
    gravity: -9.81,
    time_step: 0.005,
    substeps: 4,
    solver_iterations: 32,
    friction_coefficient: 1.0,
    restitution: 0.0,
    air_density: 1.225,
    wind_velocity: [0, 0, 0],
    enable_gpu_physics: true
  };

  // Load preset configurations
  useEffect(() => {
    const mockPresets: SimulationPreset[] = [
      {
        id: 'preset_flat',
        name: 'Flat Ground',
        description: 'Simple flat terrain for basic testing',
        terrain: { ...defaultTerrain, type: 'flat', roughness: 0 },
        physics: defaultPhysics,
        created_at: '2024-01-15T10:00:00Z'
      },
      {
        id: 'preset_rough',
        name: 'Rough Terrain',
        description: 'Challenging rough terrain with obstacles',
        terrain: { ...defaultTerrain, type: 'rough', roughness: 0.2, obstacle_density: 0.3 },
        physics: { ...defaultPhysics, friction_coefficient: 0.8 },
        created_at: '2024-01-15T14:30:00Z'
      },
      {
        id: 'preset_stairs',
        name: 'Stairs Environment',
        description: 'Stair climbing challenge',
        terrain: { ...defaultTerrain, type: 'stairs', stair_height: 0.15, stair_width: 0.3 },
        physics: defaultPhysics,
        created_at: '2024-01-16T09:15:00Z'
      },
      {
        id: 'preset_slope',
        name: 'Slope Challenge',
        description: 'Inclined terrain for stability testing',
        terrain: { ...defaultTerrain, type: 'slope', slope_angle: 15 },
        physics: { ...defaultPhysics, friction_coefficient: 0.9 },
        created_at: '2024-01-16T16:45:00Z'
      },
      {
        id: 'preset_low_gravity',
        name: 'Low Gravity',
        description: 'Moon-like low gravity environment',
        terrain: { ...defaultTerrain, type: 'flat' },
        physics: { ...defaultPhysics, gravity: -1.62 },
        created_at: '2024-01-17T11:20:00Z'
      }
    ];

    setPresets(mockPresets);
    
    // Set initial form values
    configForm.setFieldsValue({
      terrain: defaultTerrain,
      physics: defaultPhysics
    });
  }, []);

  // Simulate real-time metrics updates
  useEffect(() => {
    const interval = setInterval(() => {
      setRealTimeMetrics(prev => ({
        fps: 58 + Math.random() * 4,
        physics_time: 0.7 + Math.random() * 0.3,
        render_time: 11 + Math.random() * 3,
        memory_usage: 2000 + Math.random() * 200,
        gpu_utilization: 70 + Math.random() * 15
      }));
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const handleApplyConfiguration = async (values: any) => {
    setIsApplying(true);
    
    try {
      if (client) {
        // Send terrain configuration
        sendCommand(ZENOH_TOPICS.TERRAIN_CONFIG, 'update_terrain', values.terrain);
        
        // Send physics configuration
        sendCommand(ZENOH_TOPICS.PHYSICS_CONFIG, 'update_physics', values.physics);
        
        notification.success({
          message: 'Configuration Applied',
          description: 'Simulation configuration has been successfully updated.',
        });
      }
    } catch (error) {
      notification.error({
        message: 'Configuration Error',
        description: 'Failed to apply simulation configuration.',
      });
    } finally {
      setIsApplying(false);
    }
  };

  const handleLoadPreset = (preset: SimulationPreset) => {
    configForm.setFieldsValue({
      terrain: preset.terrain,
      physics: preset.physics
    });
    setSelectedPreset(preset.id);
    
    notification.info({
      message: 'Preset Loaded',
      description: `Configuration "${preset.name}" has been loaded.`,
    });
  };

  const handleSavePreset = async (values: any) => {
    const currentConfig = configForm.getFieldsValue();
    
    const newPreset: SimulationPreset = {
      id: `preset_${Date.now()}`,
      name: values.name,
      description: values.description,
      terrain: currentConfig.terrain,
      physics: currentConfig.physics,
      created_at: new Date().toISOString()
    };

    setPresets(prev => [...prev, newPreset]);
    setShowPresetModal(false);
    presetForm.resetFields();
    
    notification.success({
      message: 'Preset Saved',
      description: `Configuration "${values.name}" has been saved.`,
    });
  };

  const handleDeletePreset = (presetId: string) => {
    const preset = presets.find(p => p.id === presetId);
    
    confirm({
      title: 'Delete Preset',
      content: `Are you sure you want to delete "${preset?.name}"?`,
      okText: 'Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk() {
        setPresets(prev => prev.filter(p => p.id !== presetId));
        if (selectedPreset === presetId) {
          setSelectedPreset(null);
        }
        
        notification.success({
          message: 'Preset Deleted',
          description: `Configuration "${preset?.name}" has been deleted.`,
        });
      },
    });
  };

  const getPerformanceChart = () => {
    return {
      backgroundColor: 'transparent',
      title: {
        text: 'Real-time Performance',
        textStyle: { color: '#fff', fontSize: 14 }
      },
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(0,0,0,0.8)',
        borderColor: '#333',
        textStyle: { color: '#fff' }
      },
      series: [{
        name: 'Performance Metrics',
        type: 'gauge',
        radius: '80%',
        data: [{ 
          value: realTimeMetrics.fps, 
          name: 'FPS',
          title: { color: '#fff' },
          detail: { color: '#fff' }
        }],
        axisLabel: { color: '#fff' },
        axisTick: { lineStyle: { color: '#fff' } },
        axisLine: {
          lineStyle: {
            color: [[0.3, '#f5222d'], [0.7, '#faad14'], [1, '#52c41a']]
          }
        },
        pointer: {
          itemStyle: { color: '#1890ff' }
        },
        title: {
          fontSize: 12,
          color: '#fff'
        },
        detail: {
          fontSize: 20,
          color: '#fff'
        },
        min: 0,
        max: 120
      }]
    };
  };

  const presetColumns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: SimulationPreset) => (
        <Space>
          <Text strong>{text}</Text>
          {selectedPreset === record.id && <Tag color="blue">Active</Tag>}
        </Space>
      )
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: 'Terrain',
      dataIndex: 'terrain',
      key: 'terrain',
      render: (terrain: TerrainConfig) => (
        <Tag color="green">{terrain.type}</Tag>
      )
    },
    {
      title: 'Created',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => new Date(date).toLocaleDateString()
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: any, record: SimulationPreset) => (
        <Space>
          <Button
            type="text"
            icon={<LoadingOutlined />}
            onClick={() => handleLoadPreset(record)}
            size="small"
          >
            Load
          </Button>
          <Button
            type="text"
            icon={<CopyOutlined />}
            size="small"
          >
            Clone
          </Button>
          <Button
            type="text"
            icon={<DeleteOutlined />}
            onClick={() => handleDeletePreset(record.id)}
            danger
            size="small"
          >
            Delete
          </Button>
        </Space>
      )
    }
  ];

  return (
    <div>
      <Title level={2} style={{ marginBottom: '24px', color: 'white' }}>
        Simulation Configuration
      </Title>

      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        {/* Real-time Metrics */}
        <Col xs={24} lg={8}>
          <Card title="Performance Monitor">
            <ReactECharts
              option={getPerformanceChart()}
              style={{ height: '200px' }}
              notMerge={true}
            />
            <Divider />
            <Row gutter={[16, 8]}>
              <Col span={12}>
                <Text type="secondary">Physics Time:</Text>
                <div>{realTimeMetrics.physics_time.toFixed(1)} ms</div>
              </Col>
              <Col span={12}>
                <Text type="secondary">Render Time:</Text>
                <div>{realTimeMetrics.render_time.toFixed(1)} ms</div>
              </Col>
              <Col span={12}>
                <Text type="secondary">Memory:</Text>
                <div>{(realTimeMetrics.memory_usage / 1024).toFixed(1)} GB</div>
              </Col>
              <Col span={12}>
                <Text type="secondary">GPU:</Text>
                <div>{realTimeMetrics.gpu_utilization.toFixed(0)}%</div>
              </Col>
            </Row>
          </Card>
        </Col>

        {/* Quick Actions */}
        <Col xs={24} lg={8}>
          <Card title="Quick Actions">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Button
                type="primary"
                icon={<PlayCircleOutlined />}
                onClick={() => handleApplyConfiguration(configForm.getFieldsValue())}
                loading={isApplying}
                block
              >
                Apply Configuration
              </Button>
              <Button
                icon={<SaveOutlined />}
                onClick={() => setShowPresetModal(true)}
                block
              >
                Save as Preset
              </Button>
              <Button
                icon={<EyeOutlined />}
                block
              >
                Preview Environment
              </Button>
              <Button
                icon={<SettingOutlined />}
                onClick={() => configForm.resetFields()}
                block
              >
                Reset to Defaults
              </Button>
            </Space>
          </Card>
        </Col>

        {/* Current Configuration Summary */}
        <Col xs={24} lg={8}>
          <Card title="Current Configuration">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong>Terrain:</Text>
                <Text style={{ marginLeft: '8px' }}>
                  {configForm.getFieldValue(['terrain', 'type']) || 'rough'}
                </Text>
              </div>
              <div>
                <Text strong>Size:</Text>
                <Text style={{ marginLeft: '8px' }}>
                  {configForm.getFieldValue(['terrain', 'length']) || 20} × {configForm.getFieldValue(['terrain', 'width']) || 20} m
                </Text>
              </div>
              <div>
                <Text strong>Gravity:</Text>
                <Text style={{ marginLeft: '8px' }}>
                  {configForm.getFieldValue(['physics', 'gravity']) || -9.81} m/s²
                </Text>
              </div>
              <div>
                <Text strong>Time Step:</Text>
                <Text style={{ marginLeft: '8px' }}>
                  {configForm.getFieldValue(['physics', 'time_step']) || 0.005} s
                </Text>
              </div>
              <div>
                <Text strong>GPU Physics:</Text>
                <Text style={{ marginLeft: '8px' }}>
                  {configForm.getFieldValue(['physics', 'enable_gpu_physics']) ? 'Enabled' : 'Disabled'}
                </Text>
              </div>
            </Space>
          </Card>
        </Col>
      </Row>

      {/* Configuration Tabs */}
      <Row>
        <Col span={24}>
          <Card>
            <Form
              form={configForm}
              layout="vertical"
              onFinish={handleApplyConfiguration}
              initialValues={{
                terrain: defaultTerrain,
                physics: defaultPhysics
              }}
            >
              <Tabs
                activeKey={activeTab}
                onChange={setActiveTab}
                type="card"
              >
                {/* Terrain Configuration */}
                <TabPane
                  tab={
                    <span>
                      <EnvironmentOutlined />
                      Terrain Configuration
                    </span>
                  }
                  key="terrain"
                >
                  <Row gutter={[24, 16]}>
                    <Col xs={24} lg={12}>
                      <Form.Item label="Terrain Type" name={['terrain', 'type']}>
                        <Select>
                          <Option value="flat">Flat Ground</Option>
                          <Option value="rough">Rough Terrain</Option>
                          <Option value="stairs">Stairs</Option>
                          <Option value="slope">Slope</Option>
                          <Option value="random">Random Obstacles</Option>
                          <Option value="heightmap">Custom Heightmap</Option>
                        </Select>
                      </Form.Item>

                      <Row gutter={16}>
                        <Col span={8}>
                          <Form.Item label="Length (m)" name={['terrain', 'length']}>
                            <InputNumber min={5} max={100} step={1} style={{ width: '100%' }} />
                          </Form.Item>
                        </Col>
                        <Col span={8}>
                          <Form.Item label="Width (m)" name={['terrain', 'width']}>
                            <InputNumber min={5} max={100} step={1} style={{ width: '100%' }} />
                          </Form.Item>
                        </Col>
                        <Col span={8}>
                          <Form.Item label="Height (m)" name={['terrain', 'height']}>
                            <InputNumber min={0.1} max={10} step={0.1} style={{ width: '100%' }} />
                          </Form.Item>
                        </Col>
                      </Row>

                      <Form.Item label="Roughness" name={['terrain', 'roughness']}>
                        <Slider
                          min={0}
                          max={1}
                          step={0.05}
                          marks={{
                            0: 'Smooth',
                            0.5: 'Medium',
                            1: 'Very Rough'
                          }}
                        />
                      </Form.Item>

                      <Form.Item label="Random Seed" name={['terrain', 'seed']}>
                        <InputNumber min={1} max={99999} style={{ width: '100%' }} />
                      </Form.Item>
                    </Col>

                    <Col xs={24} lg={12}>
                      {/* Terrain-specific parameters */}
                      <Form.Item shouldUpdate>
                        {({ getFieldValue }) => {
                          const terrainType = getFieldValue(['terrain', 'type']);
                          
                          if (terrainType === 'slope') {
                            return (
                              <Form.Item label="Slope Angle (degrees)" name={['terrain', 'slope_angle']}>
                                <Slider min={0} max={45} step={1} marks={{ 0: '0°', 15: '15°', 30: '30°', 45: '45°' }} />
                              </Form.Item>
                            );
                          }
                          
                          if (terrainType === 'stairs') {
                            return (
                              <>
                                <Form.Item label="Stair Height (m)" name={['terrain', 'stair_height']}>
                                  <InputNumber min={0.05} max={0.5} step={0.01} style={{ width: '100%' }} />
                                </Form.Item>
                                <Form.Item label="Stair Width (m)" name={['terrain', 'stair_width']}>
                                  <InputNumber min={0.1} max={1.0} step={0.05} style={{ width: '100%' }} />
                                </Form.Item>
                              </>
                            );
                          }
                          
                          if (terrainType === 'random') {
                            return (
                              <Form.Item label="Obstacle Density" name={['terrain', 'obstacle_density']}>
                                <Slider min={0} max={1} step={0.05} marks={{ 0: 'Sparse', 0.5: 'Medium', 1: 'Dense' }} />
                              </Form.Item>
                            );
                          }
                          
                          return null;
                        }}
                      </Form.Item>

                      <div style={{ padding: '16px', backgroundColor: 'rgba(255,255,255,0.05)', borderRadius: '4px' }}>
                        <Text strong>Terrain Preview</Text>
                        <div style={{ marginTop: '8px', height: '150px', background: 'linear-gradient(45deg, #1f4037 0%, #99f2c8 100%)', borderRadius: '4px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                          <Text style={{ color: 'white', fontSize: '16px' }}>3D Preview Coming Soon</Text>
                        </div>
                      </div>
                    </Col>
                  </Row>
                </TabPane>

                {/* Physics Configuration */}
                <TabPane
                  tab={
                    <span>
                      <ExperimentOutlined />
                      Physics Parameters
                    </span>
                  }
                  key="physics"
                >
                  <Row gutter={[24, 16]}>
                    <Col xs={24} lg={12}>
                      <Form.Item label="Gravity (m/s²)" name={['physics', 'gravity']}>
                        <Slider
                          min={-20}
                          max={0}
                          step={0.1}
                          marks={{
                            '-20': '-20',
                            '-9.81': 'Earth',
                            '-3.71': 'Mars',
                            '-1.62': 'Moon',
                            '0': 'Zero G'
                          }}
                        />
                      </Form.Item>

                      <Row gutter={16}>
                        <Col span={12}>
                          <Form.Item label="Time Step (s)" name={['physics', 'time_step']}>
                            <InputNumber
                              min={0.001}
                              max={0.02}
                              step={0.001}
                              style={{ width: '100%' }}
                            />
                          </Form.Item>
                        </Col>
                        <Col span={12}>
                          <Form.Item label="Substeps" name={['physics', 'substeps']}>
                            <InputNumber min={1} max={10} style={{ width: '100%' }} />
                          </Form.Item>
                        </Col>
                      </Row>

                      <Form.Item label="Solver Iterations" name={['physics', 'solver_iterations']}>
                        <Slider min={4} max={64} step={4} marks={{ 4: '4', 16: '16', 32: '32', 64: '64' }} />
                      </Form.Item>

                      <Row gutter={16}>
                        <Col span={12}>
                          <Form.Item label="Friction Coefficient" name={['physics', 'friction_coefficient']}>
                            <InputNumber min={0} max={2} step={0.1} style={{ width: '100%' }} />
                          </Form.Item>
                        </Col>
                        <Col span={12}>
                          <Form.Item label="Restitution" name={['physics', 'restitution']}>
                            <InputNumber min={0} max={1} step={0.1} style={{ width: '100%' }} />
                          </Form.Item>
                        </Col>
                      </Row>
                    </Col>

                    <Col xs={24} lg={12}>
                      <Form.Item label="Air Density (kg/m³)" name={['physics', 'air_density']}>
                        <InputNumber min={0} max={10} step={0.1} style={{ width: '100%' }} />
                      </Form.Item>

                      <div>
                        <Text strong>Wind Velocity (m/s):</Text>
                        <Row gutter={8} style={{ marginTop: '8px' }}>
                          <Col span={8}>
                            <Form.Item label="X" name={['physics', 'wind_velocity', 0]}>
                              <InputNumber min={-20} max={20} step={0.5} style={{ width: '100%' }} />
                            </Form.Item>
                          </Col>
                          <Col span={8}>
                            <Form.Item label="Y" name={['physics', 'wind_velocity', 1]}>
                              <InputNumber min={-20} max={20} step={0.5} style={{ width: '100%' }} />
                            </Form.Item>
                          </Col>
                          <Col span={8}>
                            <Form.Item label="Z" name={['physics', 'wind_velocity', 2]}>
                              <InputNumber min={-20} max={20} step={0.5} style={{ width: '100%' }} />
                            </Form.Item>
                          </Col>
                        </Row>
                      </div>

                      <Form.Item label="Enable GPU Physics" name={['physics', 'enable_gpu_physics']} valuePropName="checked">
                        <Switch />
                      </Form.Item>

                      <div style={{ padding: '16px', backgroundColor: 'rgba(255,255,255,0.05)', borderRadius: '4px' }}>
                        <Text strong>Performance Impact</Text>
                        <div style={{ marginTop: '8px' }}>
                          <Text type="secondary">Time Step: </Text>
                          <Tag color={configForm.getFieldValue(['physics', 'time_step']) < 0.01 ? 'green' : 'orange'}>
                            {configForm.getFieldValue(['physics', 'time_step']) < 0.01 ? 'Fast' : 'Accurate'}
                          </Tag>
                        </div>
                        <div>
                          <Text type="secondary">GPU Physics: </Text>
                          <Tag color={configForm.getFieldValue(['physics', 'enable_gpu_physics']) ? 'blue' : 'default'}>
                            {configForm.getFieldValue(['physics', 'enable_gpu_physics']) ? 'Enabled' : 'Disabled'}
                          </Tag>
                        </div>
                      </div>
                    </Col>
                  </Row>
                </TabPane>

                {/* Presets Management */}
                <TabPane
                  tab={
                    <span>
                      <SaveOutlined />
                      Presets Management
                    </span>
                  }
                  key="presets"
                >
                  <div style={{ marginBottom: '16px' }}>
                    <Button
                      type="primary"
                      icon={<PlusOutlined />}
                      onClick={() => setShowPresetModal(true)}
                    >
                      Create New Preset
                    </Button>
                  </div>

                  <Table
                    columns={presetColumns}
                    dataSource={presets}
                    rowKey="id"
                    pagination={{ pageSize: 10 }}
                    size="small"
                  />
                </TabPane>
              </Tabs>

              <div style={{ textAlign: 'center', marginTop: '24px' }}>
                <Space>
                  <Button onClick={() => configForm.resetFields()}>
                    Reset
                  </Button>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={isApplying}
                    icon={<PlayCircleOutlined />}
                  >
                    Apply Configuration
                  </Button>
                </Space>
              </div>
            </Form>
          </Card>
        </Col>
      </Row>

      {/* Save Preset Modal */}
      <Modal
        title="Save Configuration Preset"
        open={showPresetModal}
        onCancel={() => {
          setShowPresetModal(false);
          presetForm.resetFields();
        }}
        footer={null}
      >
        <Form
          form={presetForm}
          layout="vertical"
          onFinish={handleSavePreset}
        >
          <Form.Item
            label="Preset Name"
            name="name"
            rules={[{ required: true, message: 'Please enter preset name!' }]}
          >
            <Input placeholder="Enter preset name" />
          </Form.Item>
          
          <Form.Item
            label="Description"
            name="description"
          >
            <Input.TextArea rows={3} placeholder="Describe this configuration preset" />
          </Form.Item>
          
          <div style={{ textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setShowPresetModal(false)}>
                Cancel
              </Button>
              <Button type="primary" htmlType="submit">
                Save Preset
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default SimulationConfiguration;