import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Typography, Space, Progress, Tag, Statistic, Divider } from 'antd';
import { RobotOutlined, DashboardOutlined, ThunderboltOutlined, HeartOutlined } from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';
import { useZenohStore } from '../../stores/zenoh-store';

const { Title, Text } = Typography;

interface RobotStateDisplayProps {
  showCharts?: boolean;
}

interface JointData {
  position: number;
  velocity: number;
  torque: number;
  temperature?: number;
  current?: number;
}

const RobotStateDisplay: React.FC<RobotStateDisplayProps> = ({ showCharts = true }) => {
  const { robotState, playStatus } = useZenohStore();
  const [historicalData, setHistoricalData] = useState<{
    timestamps: number[];
    positions: number[][];
    velocities: number[][];
    torques: number[][];
  }>({
    timestamps: [],
    positions: Array(12).fill(0).map(() => []),
    velocities: Array(12).fill(0).map(() => []),
    torques: Array(12).fill(0).map(() => [])
  });

  // Joint names for display
  const jointNames = [
    'FL_hip', 'FL_thigh', 'FL_calf',
    'FR_hip', 'FR_thigh', 'FR_calf', 
    'RL_hip', 'RL_thigh', 'RL_calf',
    'RR_hip', 'RR_thigh', 'RR_calf'
  ];

  const legNames = ['Front Left', 'Front Right', 'Rear Left', 'Rear Right'];

  // Update historical data when robot state changes
  useEffect(() => {
    if (robotState && robotState.joint_positions?.length === 12) {
      const now = Date.now();
      
      setHistoricalData(prev => {
        const newTimestamps = [...prev.timestamps.slice(-99), now];
        const newPositions = prev.positions.map((posHistory, i) => [
          ...posHistory.slice(-99),
          robotState.joint_positions[i]
        ]);
        const newVelocities = prev.velocities.map((velHistory, i) => [
          ...velHistory.slice(-99),
          robotState.joint_velocities[i]
        ]);
        const newTorques = prev.torques.map((torqueHistory, i) => [
          ...torqueHistory.slice(-99),
          robotState.joint_torques[i]
        ]);

        return {
          timestamps: newTimestamps,
          positions: newPositions,
          velocities: newVelocities,
          torques: newTorques
        };
      });
    }
  }, [robotState]);

  // Get robot connection status
  const getConnectionStatus = () => {
    if (!robotState) return { status: 'disconnected', color: '#ff4d4f' };
    
    const timeSinceUpdate = Date.now() - (robotState.timestamp * 1000);
    if (timeSinceUpdate > 5000) return { status: 'timeout', color: '#faad14' };
    if (timeSinceUpdate > 1000) return { status: 'delayed', color: '#faad14' };
    
    return { status: 'connected', color: '#52c41a' };
  };

  const connectionStatus = getConnectionStatus();

  // Calculate some derived metrics
  const getTotalPower = () => {
    if (!robotState?.joint_torques || !robotState?.joint_velocities) return 0;
    return robotState.joint_torques.reduce((sum, torque, i) => 
      sum + Math.abs(torque * robotState.joint_velocities[i]), 0
    );
  };

  const getBaseSpeed = () => {
    if (!robotState?.base_linear_velocity) return 0;
    const [x, y, z] = robotState.base_linear_velocity;
    return Math.sqrt(x*x + y*y + z*z);
  };

  // Chart options for joint data
  const getJointChart = (jointIndex: number, dataType: 'positions' | 'velocities' | 'torques') => {
    const data = historicalData[dataType][jointIndex] || [];
    const timestamps = historicalData.timestamps;
    
    const units = {
      positions: 'rad',
      velocities: 'rad/s', 
      torques: 'Nm'
    };

    const colors = {
      positions: '#1890ff',
      velocities: '#52c41a',
      torques: '#faad14'
    };

    return {
      backgroundColor: 'transparent',
      title: {
        text: `${jointNames[jointIndex]} - ${dataType.charAt(0).toUpperCase() + dataType.slice(1)}`,
        textStyle: {
          color: '#fff',
          fontSize: 12
        }
      },
      grid: {
        top: 40,
        left: 50,
        right: 20,
        bottom: 30
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0,0,0,0.8)',
        borderColor: '#333',
        textStyle: { color: '#fff' }
      },
      xAxis: {
        type: 'category',
        data: timestamps.map(t => new Date(t).toLocaleTimeString()),
        axisLabel: { color: '#fff', fontSize: 10 },
        axisLine: { lineStyle: { color: '#fff' } }
      },
      yAxis: {
        type: 'value',
        name: units[dataType],
        nameTextStyle: { color: '#fff' },
        axisLabel: { color: '#fff', fontSize: 10 },
        axisLine: { lineStyle: { color: '#fff' } },
        splitLine: { lineStyle: { color: 'rgba(255,255,255,0.1)' } }
      },
      series: [{
        type: 'line',
        data: data,
        smooth: true,
        lineStyle: { color: colors[dataType], width: 2 },
        itemStyle: { color: colors[dataType] },
        symbol: 'none',
        animation: false
      }]
    };
  };

  return (
    <div>
      <Title level={2} style={{ marginBottom: '24px', color: 'white' }}>
        Robot State Monitor
      </Title>

      {/* Connection Status and Overview */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} lg={6}>
          <Card>
            <Space direction="vertical" style={{ width: '100%', textAlign: 'center' }}>
              <RobotOutlined 
                style={{ 
                  fontSize: '32px', 
                  color: connectionStatus.color
                }} 
              />
              <Text strong style={{ color: connectionStatus.color }}>
                {connectionStatus.status.toUpperCase()}
              </Text>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                Last Update: {robotState ? new Date(robotState.timestamp * 1000).toLocaleTimeString() : 'N/A'}
              </Text>
            </Space>
          </Card>
        </Col>

        <Col xs={24} lg={6}>
          <Card>
            <Statistic
              title="Base Speed"
              value={getBaseSpeed()}
              precision={2}
              suffix="m/s"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>

        <Col xs={24} lg={6}>
          <Card>
            <Statistic
              title="Total Power"
              value={getTotalPower()}
              precision={1}
              suffix="W"
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>

        <Col xs={24} lg={6}>
          <Card>
            <Statistic
              title="Active Joints"
              value={robotState?.joint_positions?.length || 0}
              suffix="/ 12"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Base State */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} lg={12}>
          <Card title={
            <Space>
              <DashboardOutlined />
              <span>Base Position & Orientation</span>
            </Space>
          }>
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Text strong>Position (m):</Text>
                <div style={{ marginTop: '8px' }}>
                  <Text>X: {robotState?.base_position?.[0]?.toFixed(3) || 'N/A'}</Text><br/>
                  <Text>Y: {robotState?.base_position?.[1]?.toFixed(3) || 'N/A'}</Text><br/>
                  <Text>Z: {robotState?.base_position?.[2]?.toFixed(3) || 'N/A'}</Text>
                </div>
              </Col>
              <Col span={12}>
                <Text strong>Orientation (rad):</Text>
                <div style={{ marginTop: '8px' }}>
                  <Text>Roll: {robotState?.base_orientation?.[0]?.toFixed(3) || 'N/A'}</Text><br/>
                  <Text>Pitch: {robotState?.base_orientation?.[1]?.toFixed(3) || 'N/A'}</Text><br/>
                  <Text>Yaw: {robotState?.base_orientation?.[2]?.toFixed(3) || 'N/A'}</Text>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card title={
            <Space>
              <ThunderboltOutlined />
              <span>Base Velocity</span>
            </Space>
          }>
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Text strong>Linear (m/s):</Text>
                <div style={{ marginTop: '8px' }}>
                  <Text>X: {robotState?.base_linear_velocity?.[0]?.toFixed(3) || 'N/A'}</Text><br/>
                  <Text>Y: {robotState?.base_linear_velocity?.[1]?.toFixed(3) || 'N/A'}</Text><br/>
                  <Text>Z: {robotState?.base_linear_velocity?.[2]?.toFixed(3) || 'N/A'}</Text>
                </div>
              </Col>
              <Col span={12}>
                <Text strong>Angular (rad/s):</Text>
                <div style={{ marginTop: '8px' }}>
                  <Text>X: {robotState?.base_angular_velocity?.[0]?.toFixed(3) || 'N/A'}</Text><br/>
                  <Text>Y: {robotState?.base_angular_velocity?.[1]?.toFixed(3) || 'N/A'}</Text><br/>
                  <Text>Z: {robotState?.base_angular_velocity?.[2]?.toFixed(3) || 'N/A'}</Text>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      {/* Joint States Table */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col span={24}>
          <Card title="Joint States">
            <Row gutter={[16, 16]}>
              {legNames.map((legName, legIndex) => (
                <Col key={legName} xs={24} lg={12} xl={6}>
                  <Card size="small" title={legName} style={{ height: '100%' }}>
                    {Array.from({ length: 3 }, (_, jointIndex) => {
                      const globalIndex = legIndex * 3 + jointIndex;
                      const jointName = jointNames[globalIndex];
                      const position = robotState?.joint_positions?.[globalIndex];
                      const velocity = robotState?.joint_velocities?.[globalIndex];
                      const torque = robotState?.joint_torques?.[globalIndex];
                      
                      return (
                        <div key={globalIndex} style={{ marginBottom: '12px', borderBottom: '1px solid #303030', paddingBottom: '8px' }}>
                          <Text strong style={{ fontSize: '12px' }}>{jointName}:</Text>
                          <div style={{ marginTop: '4px', fontSize: '11px' }}>
                            <div>Pos: {position?.toFixed(2) || 'N/A'} rad</div>
                            <div>Vel: {velocity?.toFixed(2) || 'N/A'} rad/s</div>
                            <div>Torque: {torque?.toFixed(1) || 'N/A'} Nm</div>
                          </div>
                          
                          {/* Joint load indicator */}
                          {torque !== undefined && (
                            <Progress
                              percent={Math.min(Math.abs(torque) / 50 * 100, 100)}
                              size="small"
                              strokeColor={Math.abs(torque) > 40 ? '#ff4d4f' : Math.abs(torque) > 20 ? '#faad14' : '#52c41a'}
                              showInfo={false}
                              style={{ marginTop: '4px' }}
                            />
                          )}
                        </div>
                      );
                    })}
                  </Card>
                </Col>
              ))}
            </Row>
          </Card>
        </Col>
      </Row>

      {/* Contact Forces */}
      {robotState?.contact_forces && robotState.contact_forces.length >= 4 && (
        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col span={24}>
            <Card title={
              <Space>
                <HeartOutlined />
                <span>Contact Forces</span>
              </Space>
            }>
              <Row gutter={[16, 16]}>
                {['Front Left', 'Front Right', 'Rear Left', 'Rear Right'].map((legName, index) => (
                  <Col key={legName} xs={12} lg={6}>
                    <div style={{ textAlign: 'center' }}>
                      <Text strong>{legName}</Text>
                      <div style={{ fontSize: '20px', margin: '8px 0', color: '#1890ff' }}>
                        {robotState.contact_forces[index]?.toFixed(1) || '0.0'} N
                      </div>
                      <Progress
                        percent={Math.min((robotState.contact_forces[index] || 0) / 200 * 100, 100)}
                        size="small"
                        strokeColor={(robotState.contact_forces[index] || 0) > 150 ? '#ff4d4f' : '#1890ff'}
                        showInfo={false}
                      />
                    </div>
                  </Col>
                ))}
              </Row>
            </Card>
          </Col>
        </Row>
      )}

      {/* Real-time Charts */}
      {showCharts && historicalData.timestamps.length > 0 && (
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Card title="Joint Data Visualization">
              <Row gutter={[16, 16]}>
                {/* Show charts for first few joints as examples */}
                {[0, 3, 6, 9].map((jointIndex) => (
                  <Col key={jointIndex} xs={24} lg={12}>
                    <ReactECharts
                      option={getJointChart(jointIndex, 'positions')}
                      style={{ height: '200px' }}
                      notMerge={true}
                    />
                  </Col>
                ))}
              </Row>
            </Card>
          </Col>
        </Row>
      )}
    </div>
  );
};

export default RobotStateDisplay;