import React, { useState } from 'react';
import { Row, Col, Card, Form, Input, Select, Button, Slider, Typography, Space, Switch, Divider, Tabs } from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  SettingOutlined,
  ExperimentOutlined,
  EnvironmentOutlined,
  RobotOutlined,
  Bar<PERSON>hartOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import RobotControlPanel from './RobotControlPanel';
import RobotStateDisplay from './RobotStateDisplay';
import SimulationConfiguration from './SimulationConfiguration';

const { Title, Text } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;

interface SimulationViewProps {}

const SimulationView: React.FC<SimulationViewProps> = () => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [isSimulating, setIsSimulating] = useState(false);
  const [showAdvanced, setShowAdvanced] = useState(false);

  const handleStartSimulation = async (values: any) => {
    setIsSimulating(true);
    console.log('Starting simulation with config:', values);
  };

  const handleStopSimulation = () => {
    setIsSimulating(false);
  };

  return (
    <div>
      <Title level={2} style={{ marginBottom: '24px', color: 'white' }}>
        {t('simulation.simulationRobotControl')}
      </Title>

      <Tabs defaultActiveKey="control" type="card">
        {/* Robot Control Tab */}
        <TabPane
          tab={
            <span>
              <RobotOutlined />
              {t('simulation.robotControl')}
            </span>
          }
          key="control"
        >
          <RobotControlPanel />
        </TabPane>

        {/* Robot State Tab */}
        <TabPane
          tab={
            <span>
              <BarChartOutlined />
              {t('simulation.robotState')}
            </span>
          }
          key="state"
        >
          <RobotStateDisplay showCharts={true} />
        </TabPane>

        {/* Advanced Configuration Tab */}
        <TabPane
          tab={
            <span>
              <SettingOutlined />
              {t('simulation.advancedConfig')}
            </span>
          }
          key="configuration"
        >
          <SimulationConfiguration />
        </TabPane>

        {/* Environment Configuration Tab */}
        <TabPane
          tab={
            <span>
              <EnvironmentOutlined />
              {t('simulation.environment')}
            </span>
          }
          key="environment"
        >
          <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
            {/* Environment Configuration */}
            <Col xs={24} lg={16}>
              <Card title={
                <Space>
                  <EnvironmentOutlined />
                  <span>{t('simulation.environmentConfiguration')}</span>
                </Space>
              }>
                <Form
                  form={form}
                  layout="vertical"
                  initialValues={{
                    terrainType: 'rough',
                    terrainLength: 8.0,
                    terrainWidth: 8.0,
                    gravity: -9.81,
                    timeStep: 0.005,
                  }}
                >
                  <Form.Item label={t('simulation.terrainType')} name="terrainType">
                    <Select>
                      <Option value="flat">{t('simulation.flatGround')}</Option>
                      <Option value="rough">{t('simulation.roughTerrain')}</Option>
                      <Option value="stairs">{t('simulation.stairs')}</Option>
                      <Option value="slope">{t('simulation.slope')}</Option>
                      <Option value="random">{t('simulation.randomObstacles')}</Option>
                    </Select>
                  </Form.Item>

                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item label={`${t('simulation.terrainLength')} (m)`} name="terrainLength">
                        <Input type="number" step="0.1" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item label={`${t('simulation.terrainWidth')} (m)`} name="terrainWidth">
                        <Input type="number" step="0.1" />
                      </Form.Item>
                    </Col>
                  </Row>

                  <div>
                    <Text strong>{t('training.advancedSettings')}</Text>
                    <Switch
                      checked={showAdvanced}
                      onChange={setShowAdvanced}
                      style={{ marginLeft: '16px' }}
                    />
                  </div>

                  {showAdvanced && (
                    <div style={{ marginTop: '16px' }}>
                      <Form.Item label={`${t('simulation.gravity')} (m/s²)`} name="gravity">
                        <Slider
                          min={-15}
                          max={0}
                          step={0.1}
                          marks={{
                            '-15': '-15',
                            '-9.81': 'Earth',
                            '-3.71': 'Mars',
                            '-1.62': 'Moon',
                            '0': '0'
                          }}
                        />
                      </Form.Item>

                      <Form.Item label={`${t('simulation.timeStep')} (s)`} name="timeStep">
                        <Slider
                          min={0.001}
                          max={0.02}
                          step={0.001}
                          marks={{
                            0.001: '0.001',
                            0.005: '0.005',
                            0.01: '0.01',
                            0.02: '0.02'
                          }}
                        />
                      </Form.Item>

                      <Row gutter={16}>
                        <Col span={12}>
                          <Form.Item label={`${t('simulation.windSpeed')} (m/s)`} name="windSpeed">
                            <Input type="number" step="0.1" defaultValue={0} />
                          </Form.Item>
                        </Col>
                        <Col span={12}>
                          <Form.Item label={t('simulation.frictionCoefficient')} name="friction">
                            <Input type="number" step="0.01" defaultValue={1.0} />
                          </Form.Item>
                        </Col>
                      </Row>
                    </div>
                  )}

                  <div style={{ marginTop: '24px' }}>
                    <Space>
                      <Button 
                        type="primary" 
                        icon={<PlayCircleOutlined />}
                        onClick={() => handleStartSimulation(form.getFieldsValue())}
                        loading={isSimulating}
                        disabled={isSimulating}
                      >
                        Apply Configuration
                      </Button>
                      <Button 
                        icon={<SettingOutlined />}
                        onClick={() => form.resetFields()}
                      >
                        Reset to Defaults
                      </Button>
                    </Space>
                  </div>
                </Form>
              </Card>
            </Col>

            {/* Simulation Status */}
            <Col xs={24} lg={8}>
              <Card title={t('simulation.simulationStatus')}>
                <Space direction="vertical" style={{ width: '100%' }}>
                  <div>
                    <Text strong>{t('status.currentStatus')}:</Text>
                    <Text style={{ marginLeft: '8px', color: isSimulating ? '#52c41a' : '#8c8c8c' }}>
                      {isSimulating ? t('status.running') : t('status.stopped')}
                    </Text>
                  </div>
                  <div>
                    <Text strong>{t('simulation.physicsEngine')}:</Text>
                    <Text style={{ marginLeft: '8px' }}>{t('simulation.isaacGym')}</Text>
                  </div>
                  <div>
                    <Text strong>{t('simulation.renderer')}:</Text>
                    <Text style={{ marginLeft: '8px' }}>{t('simulation.openGL')}</Text>
                  </div>
                  
                  <Divider />
                  
                  <Space>
                    <Button
                      type="primary"
                      icon={<PlayCircleOutlined />}
                      onClick={() => handleStartSimulation(form.getFieldsValue())}
                      loading={isSimulating}
                      disabled={isSimulating}
                    >
                      {t('simulation.startSimulation')}
                    </Button>
                    <Button
                      danger
                      icon={<StopOutlined />}
                      onClick={handleStopSimulation}
                      disabled={!isSimulating}
                    >
                      {t('common.stop')}
                    </Button>
                  </Space>
                </Space>
              </Card>

              {/* Quick Metrics */}
              <Card title={t('dashboard.performanceMetrics')} style={{ marginTop: '16px' }}>
                <Row gutter={[16, 16]}>
                  <Col span={12}>
                    <div style={{ textAlign: 'center' }}>
                      <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#52c41a' }}>
                        {isSimulating ? '1247' : '0'}
                      </div>
                      <Text type="secondary" style={{ fontSize: '12px' }}>{t('simulation.steps')}</Text>
                    </div>
                  </Col>
                  <Col span={12}>
                    <div style={{ textAlign: 'center' }}>
                      <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#1890ff' }}>
                        {isSimulating ? '62.4s' : '0.0s'}
                      </div>
                      <Text type="secondary" style={{ fontSize: '12px' }}>{t('simulation.simTime')}</Text>
                    </div>
                  </Col>
                  <Col span={12}>
                    <div style={{ textAlign: 'center' }}>
                      <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#722ed1' }}>
                        {isSimulating ? '156.8' : '0.0'}
                      </div>
                      <Text type="secondary" style={{ fontSize: '12px' }}>{t('simulation.reward')}</Text>
                    </div>
                  </Col>
                  <Col span={12}>
                    <div style={{ textAlign: 'center' }}>
                      <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#fa8c16' }}>
                        {isSimulating ? '98%' : '0%'}
                      </div>
                      <Text type="secondary" style={{ fontSize: '12px' }}>{t('simulation.success')}</Text>
                    </div>
                  </Col>
                </Row>
              </Card>
            </Col>
          </Row>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default SimulationView;