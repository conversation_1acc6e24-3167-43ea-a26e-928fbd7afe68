{"name": "web_gui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@types/node": "^24.0.15", "@types/ws": "^8.18.1", "antd": "^5.26.6", "dayjs": "^1.11.10", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "i18next": "^25.3.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-i18next": "^15.6.1", "ws": "^8.18.3", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.30.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.12", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "ts-jest": "^29.2.5", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}