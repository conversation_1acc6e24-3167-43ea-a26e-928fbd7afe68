#!/usr/bin/env python3
"""
UI测试脚本 - 验证新的PC浏览器界面
"""

import time
import webbrowser
import subprocess
import sys
import os
from pathlib import Path

def check_dependencies():
    """检查必要的依赖"""
    print("🔍 检查依赖...")
    
    # 检查Node.js
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        print(f"✅ Node.js: {result.stdout.strip()}")
    except FileNotFoundError:
        print("❌ Node.js 未安装")
        return False
    
    # 检查npm
    try:
        result = subprocess.run(['npm', '--version'], capture_output=True, text=True)
        print(f"✅ npm: {result.stdout.strip()}")
    except FileNotFoundError:
        print("❌ npm 未安装")
        return False
    
    # 检查Python websockets
    try:
        import websockets
        print(f"✅ websockets: {websockets.__version__}")
    except ImportError:
        print("❌ websockets 未安装，请运行: pip install websockets")
        return False
    
    return True

def start_backend():
    """启动后端WebSocket服务器"""
    print("🚀 启动后端服务器...")
    
    backend_script = Path(__file__).parent.parent.parent / "start_web_system.py"
    if not backend_script.exists():
        print(f"❌ 后端脚本不存在: {backend_script}")
        return None
    
    try:
        process = subprocess.Popen([
            sys.executable, str(backend_script)
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # 等待服务器启动
        time.sleep(3)
        
        if process.poll() is None:
            print("✅ 后端服务器启动成功")
            return process
        else:
            stdout, stderr = process.communicate()
            print(f"❌ 后端服务器启动失败:")
            print(f"stdout: {stdout}")
            print(f"stderr: {stderr}")
            return None
    except Exception as e:
        print(f"❌ 启动后端服务器时出错: {e}")
        return None

def start_frontend():
    """启动前端开发服务器"""
    print("🎨 启动前端服务器...")
    
    web_gui_dir = Path(__file__).parent
    
    try:
        # 检查node_modules是否存在
        if not (web_gui_dir / "node_modules").exists():
            print("📦 安装依赖...")
            subprocess.run(['npm', 'install'], cwd=web_gui_dir, check=True)
        
        # 启动开发服务器
        process = subprocess.Popen([
            'npm', 'run', 'dev'
        ], cwd=web_gui_dir, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # 等待服务器启动
        time.sleep(5)
        
        if process.poll() is None:
            print("✅ 前端服务器启动成功")
            return process
        else:
            stdout, stderr = process.communicate()
            print(f"❌ 前端服务器启动失败:")
            print(f"stdout: {stdout}")
            print(f"stderr: {stderr}")
            return None
    except Exception as e:
        print(f"❌ 启动前端服务器时出错: {e}")
        return None

def open_browser():
    """打开浏览器"""
    print("🌐 打开浏览器...")
    
    url = "http://localhost:5173"
    try:
        webbrowser.open(url)
        print(f"✅ 浏览器已打开: {url}")
        return True
    except Exception as e:
        print(f"❌ 打开浏览器失败: {e}")
        print(f"请手动访问: {url}")
        return False

def run_tests():
    """运行UI测试"""
    print("\n" + "="*60)
    print("🧪 EngineAI Legged Gym - PC界面测试")
    print("="*60)
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请安装必要的依赖")
        return False
    
    print("\n" + "-"*40)
    
    # 启动后端
    backend_process = start_backend()
    if not backend_process:
        print("\n❌ 后端启动失败")
        return False
    
    print("\n" + "-"*40)
    
    # 启动前端
    frontend_process = start_frontend()
    if not frontend_process:
        print("\n❌ 前端启动失败")
        if backend_process:
            backend_process.terminate()
        return False
    
    print("\n" + "-"*40)
    
    # 打开浏览器
    open_browser()
    
    print("\n" + "="*60)
    print("🎉 测试完成！")
    print("="*60)
    print()
    print("📋 测试项目:")
    print("  ✅ 后端WebSocket服务器运行正常")
    print("  ✅ 前端开发服务器运行正常")
    print("  ✅ 浏览器已打开界面")
    print()
    print("🔍 请在浏览器中验证以下功能:")
    print("  • 界面加载正常，无空白页面")
    print("  • 侧边栏导航工作正常")
    print("  • Dashboard显示数据和图表")
    print("  • 卡片悬停效果正常")
    print("  • 响应式布局适配屏幕")
    print("  • 实时数据更新正常")
    print()
    print("⚠️  按 Ctrl+C 停止所有服务")
    
    try:
        # 保持服务运行
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n\n🛑 正在停止服务...")
        
        if frontend_process:
            frontend_process.terminate()
            print("✅ 前端服务器已停止")
        
        if backend_process:
            backend_process.terminate()
            print("✅ 后端服务器已停止")
        
        print("👋 测试结束")
        return True

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
