"""
Enhanced Zenoh message format standards and validation.
"""

import time
import uuid
from dataclasses import dataclass, field
from typing import Any, Dict, Optional, Union, Type, TypeVar
from enum import Enum

from .data_models import SerializationMixin
from .serialization import Serializer, SerializationFormat

T = TypeVar('T', bound=SerializationMixin)

class MessageType(Enum):
    """Standard message types"""
    COMMAND = "command"
    RESPONSE = "response" 
    STATUS = "status"
    DATA = "data"
    ERROR = "error"
    HEARTBEAT = "heartbeat"

class Priority(Enum):
    """Message priority levels"""
    LOW = 0
    NORMAL = 1
    HIGH = 2
    CRITICAL = 3

@dataclass
class MessageHeader(SerializationMixin):
    """Standard message header"""
    message_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: float = field(default_factory=time.time)
    message_type: MessageType = MessageType.DATA
    priority: Priority = Priority.NORMAL
    source_service: str = ""
    destination_service: str = ""
    correlation_id: str = ""  # For request-response correlation
    ttl: float = 300.0  # Time to live in seconds
    
    def is_expired(self) -> bool:
        """Check if message has expired"""
        return time.time() - self.timestamp > self.ttl
    
    def validate(self) -> bool:
        """Validate message header"""
        return (
            len(self.message_id.strip()) > 0 and
            self.timestamp > 0 and
            isinstance(self.message_type, MessageType) and
            isinstance(self.priority, Priority) and
            self.ttl > 0
        )

@dataclass
class StandardZenohMessage(SerializationMixin):
    """Standard Zenoh message format with validation"""
    header: MessageHeader
    payload: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Post-initialization validation"""
        if not isinstance(self.header, MessageHeader):
            raise ValueError("Header must be a MessageHeader instance")
    
    def validate(self) -> bool:
        """Validate complete message"""
        return (
            self.header.validate() and
            not self.header.is_expired()
        )
    
    def set_response_to(self, original_message: 'StandardZenohMessage') -> None:
        """Set this message as a response to another message"""
        self.header.correlation_id = original_message.header.message_id
        self.header.destination_service = original_message.header.source_service
        self.header.message_type = MessageType.RESPONSE
    
    def add_metadata(self, key: str, value: Any) -> None:
        """Add metadata to the message"""
        self.metadata[key] = value
    
    def get_metadata(self, key: str, default: Any = None) -> Any:
        """Get metadata value"""
        return self.metadata.get(key, default)

class MessageValidator:
    """Advanced message validation"""
    
    @staticmethod
    def validate_message_structure(message: StandardZenohMessage) -> bool:
        """Validate basic message structure"""
        try:
            return (
                isinstance(message, StandardZenohMessage) and
                isinstance(message.header, MessageHeader) and
                isinstance(message.payload, dict) and
                isinstance(message.metadata, dict)
            )
        except:
            return False
    
    @staticmethod
    def validate_message_content(message: StandardZenohMessage) -> bool:
        """Validate message content"""
        try:
            return message.validate()
        except:
            return False
    
    @staticmethod
    def validate_payload_schema(message: StandardZenohMessage, expected_keys: list) -> bool:
        """Validate payload contains expected keys"""
        try:
            return all(key in message.payload for key in expected_keys)
        except:
            return False
    
    @staticmethod
    def validate_message_type(message: StandardZenohMessage, expected_type: MessageType) -> bool:
        """Validate message type"""
        return message.header.message_type == expected_type

class MessageBuilder:
    """Builder pattern for creating standard messages"""
    
    def __init__(self, message_type: MessageType = MessageType.DATA):
        self.message = StandardZenohMessage(
            header=MessageHeader(message_type=message_type)
        )
    
    def with_source(self, service_name: str) -> 'MessageBuilder':
        """Set source service"""
        self.message.header.source_service = service_name
        return self
    
    def with_destination(self, service_name: str) -> 'MessageBuilder':
        """Set destination service"""
        self.message.header.destination_service = service_name
        return self
    
    def with_priority(self, priority: Priority) -> 'MessageBuilder':
        """Set message priority"""
        self.message.header.priority = priority
        return self
    
    def with_ttl(self, ttl: float) -> 'MessageBuilder':
        """Set time to live"""
        self.message.header.ttl = ttl
        return self
    
    def with_correlation_id(self, correlation_id: str) -> 'MessageBuilder':
        """Set correlation ID for request-response"""
        self.message.header.correlation_id = correlation_id
        return self
    
    def with_payload(self, payload: Dict[str, Any]) -> 'MessageBuilder':
        """Set message payload"""
        self.message.payload = payload
        return self
    
    def with_payload_data(self, key: str, value: Any) -> 'MessageBuilder':
        """Add data to payload"""
        self.message.payload[key] = value
        return self
    
    def with_metadata(self, key: str, value: Any) -> 'MessageBuilder':
        """Add metadata"""
        self.message.metadata[key] = value
        return self
    
    def build(self) -> StandardZenohMessage:
        """Build the final message"""
        if not self.message.validate():
            raise ValueError("Invalid message configuration")
        return self.message

class MessageFactory:
    """Factory for creating common message types"""
    
    @staticmethod
    def create_command(command: str, parameters: Dict[str, Any] = None, 
                      source: str = "", destination: str = "") -> StandardZenohMessage:
        """Create a command message"""
        return MessageBuilder(MessageType.COMMAND) \
            .with_source(source) \
            .with_destination(destination) \
            .with_payload_data("command", command) \
            .with_payload_data("parameters", parameters or {}) \
            .build()
    
    @staticmethod
    def create_response(success: bool, message: str, data: Dict[str, Any] = None,
                       original_message: StandardZenohMessage = None) -> StandardZenohMessage:
        """Create a response message"""
        builder = MessageBuilder(MessageType.RESPONSE) \
            .with_payload_data("success", success) \
            .with_payload_data("message", message) \
            .with_payload_data("data", data or {})
        
        if original_message:
            builder.with_correlation_id(original_message.header.message_id) \
                   .with_destination(original_message.header.source_service)
        
        return builder.build()
    
    @staticmethod
    def create_status(status_data: Dict[str, Any], source: str = "") -> StandardZenohMessage:
        """Create a status message"""
        return MessageBuilder(MessageType.STATUS) \
            .with_source(source) \
            .with_payload(status_data) \
            .build()
    
    @staticmethod
    def create_data(data: Dict[str, Any], source: str = "") -> StandardZenohMessage:
        """Create a data message"""
        return MessageBuilder(MessageType.DATA) \
            .with_source(source) \
            .with_payload(data) \
            .build()
    
    @staticmethod
    def create_error(error_type: str, error_message: str, context: Dict[str, Any] = None,
                    source: str = "") -> StandardZenohMessage:
        """Create an error message"""
        return MessageBuilder(MessageType.ERROR) \
            .with_source(source) \
            .with_priority(Priority.HIGH) \
            .with_payload_data("error_type", error_type) \
            .with_payload_data("error_message", error_message) \
            .with_payload_data("context", context or {}) \
            .build()
    
    @staticmethod
    def create_heartbeat(service_name: str, status: Dict[str, Any] = None) -> StandardZenohMessage:
        """Create a heartbeat message"""
        return MessageBuilder(MessageType.HEARTBEAT) \
            .with_source(service_name) \
            .with_ttl(60.0) \
            .with_payload_data("status", status or {}) \
            .with_payload_data("timestamp", time.time()) \
            .build()

class MessageProcessor:
    """Process and route messages"""
    
    @staticmethod
    def should_process_message(message: StandardZenohMessage, service_name: str) -> bool:
        """Check if message should be processed by this service"""
        return (
            message.validate() and
            (not message.header.destination_service or 
             message.header.destination_service == service_name)
        )
    
    @staticmethod
    def extract_payload_data(message: StandardZenohMessage, target_class: Type[T]) -> Optional[T]:
        """Extract and deserialize payload data to target class"""
        try:
            if issubclass(target_class, SerializationMixin):
                return target_class.from_dict(message.payload)
            return None
        except:
            return None
    
    @staticmethod
    def create_ack_response(original_message: StandardZenohMessage) -> StandardZenohMessage:
        """Create acknowledgment response"""
        return MessageFactory.create_response(
            success=True,
            message="Message acknowledged",
            original_message=original_message
        )