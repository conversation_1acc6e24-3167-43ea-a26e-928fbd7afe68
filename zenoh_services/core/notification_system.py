"""
Error Notification and User Prompt System
Provides real-time notifications and user interaction for error handling.
"""

import asyncio
import logging
import time
import json
from typing import Dict, Any, Optional, Callable, List, Set, Union
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime

from .error_handler import <PERSON><PERSON>rInfo, ErrorSeverity, ErrorCategory
from .fault_detection import FailureType
from zenoh_services.core.enhanced_session_manager import EnhancedZenohSessionManager
from zenoh_services.core.topic_manager import TopicManager
from zenoh_services.core.message_format import MessageFactory, MessageType, Priority
from zenoh_services.core import topics


class NotificationType(Enum):
    """Types of notifications"""
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"
    SUCCESS = "success"
    RECOVERY = "recovery"
    CRITICAL = "critical"


class NotificationPriority(Enum):
    """Notification priority levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


@dataclass
class NotificationAction:
    """Action that can be taken from notification"""
    action_id: str
    label: str
    action_type: str  # "button", "input", "select"
    callback: Optional[Callable] = None
    options: Optional[Dict[str, Any]] = None  # For select/input types
    requires_confirmation: bool = False
    timeout: Optional[float] = None  # Auto-dismiss after timeout


@dataclass
class Notification:
    """Notification message"""
    notification_id: str
    type: NotificationType
    priority: NotificationPriority
    title: str
    message: str
    timestamp: float
    service_name: Optional[str] = None
    error_id: Optional[str] = None
    actions: List[NotificationAction] = field(default_factory=list)
    auto_dismiss: bool = True
    dismiss_timeout: float = 30.0  # seconds
    persistent: bool = False
    tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            "notification_id": self.notification_id,
            "type": self.type.value,
            "priority": self.priority.value,
            "title": self.title,
            "message": self.message,
            "timestamp": self.timestamp,
            "service_name": self.service_name,
            "error_id": self.error_id,
            "actions": [
                {
                    "action_id": action.action_id,
                    "label": action.label,
                    "action_type": action.action_type,
                    "options": action.options,
                    "requires_confirmation": action.requires_confirmation,
                    "timeout": action.timeout
                }
                for action in self.actions
            ],
            "auto_dismiss": self.auto_dismiss,
            "dismiss_timeout": self.dismiss_timeout,
            "persistent": self.persistent,
            "tags": self.tags,
            "metadata": self.metadata
        }


@dataclass
class UserPrompt:
    """User prompt for manual intervention"""
    prompt_id: str
    title: str
    message: str
    prompt_type: str  # "confirm", "input", "select", "multiselect"
    options: Optional[Dict[str, Any]] = None
    default_value: Optional[Any] = None
    timeout: Optional[float] = 300.0  # 5 minutes default
    required: bool = True
    validation_regex: Optional[str] = None
    callback: Optional[Callable] = None
    timestamp: float = field(default_factory=time.time)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            "prompt_id": self.prompt_id,
            "title": self.title,
            "message": self.message,
            "prompt_type": self.prompt_type,
            "options": self.options,
            "default_value": self.default_value,
            "timeout": self.timeout,
            "required": self.required,
            "validation_regex": self.validation_regex,
            "timestamp": self.timestamp
        }


class NotificationManager:
    """Manages notifications and user prompts"""
    
    def __init__(self, session_manager: EnhancedZenohSessionManager):
        self.session_manager = session_manager
        self.topic_manager = TopicManager(session_manager)
        self.logger = logging.getLogger("notification_manager")
        
        # Notification state
        self.active_notifications: Dict[str, Notification] = {}
        self.notification_history: List[Notification] = []
        self.pending_prompts: Dict[str, UserPrompt] = {}
        self.prompt_responses: Dict[str, Any] = {}
        
        # Callbacks
        self.notification_callbacks: List[Callable[[Notification], None]] = []
        self.prompt_callbacks: List[Callable[[UserPrompt], None]] = []
        self.response_callbacks: Dict[str, Callable] = {}
        
        # Configuration
        self.max_history_size = 1000
        self.enable_auto_dismiss = True
        
        # Tasks
        self.auto_dismiss_task: Optional[asyncio.Task] = None
        self.is_initialized = False
    
    async def initialize(self):
        """Initialize notification manager"""
        try:
            # Register notification topics
            await self._register_topics()
            
            # Setup subscribers for responses
            await self._setup_response_subscribers()
            
            # Start auto-dismiss task
            if self.enable_auto_dismiss:
                self.auto_dismiss_task = asyncio.create_task(self._auto_dismiss_loop())
            
            self.is_initialized = True
            self.logger.info("Notification manager initialized")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize notification manager: {e}")
            raise
    
    async def _register_topics(self):
        """Register notification and prompt topics"""
        notification_topics = [
            "system/notifications",
            "system/user_prompts",
            "system/prompt_responses",
            "system/notification_actions"
        ]
        
        for topic in notification_topics:
            await self.topic_manager.register_topic(topic)
            await self.topic_manager.create_publisher(topic)
    
    async def _setup_response_subscribers(self):
        """Setup subscribers for user responses"""
        await self.topic_manager.create_subscriber(
            "system/prompt_responses",
            self._handle_prompt_response
        )
        
        await self.topic_manager.create_subscriber(
            "system/notification_actions", 
            self._handle_notification_action
        )
    
    async def send_notification(self, notification: Notification):
        """Send notification"""
        if not self.is_initialized:
            self.logger.error("Notification manager not initialized")
            return
        
        # Store notification
        self.active_notifications[notification.notification_id] = notification
        self.notification_history.append(notification)
        
        # Keep history size manageable
        if len(self.notification_history) > self.max_history_size:
            self.notification_history = self.notification_history[-self.max_history_size:]
        
        try:
            # Publish notification
            message = MessageFactory.create_notification(
                notification.to_dict(),
                "notification_manager"
            )
            await self.topic_manager.publish_message("system/notifications", message)
            
            # Call notification callbacks
            for callback in self.notification_callbacks:
                try:
                    callback(notification)
                except Exception as e:
                    self.logger.error(f"Error in notification callback: {e}")
            
            self.logger.info(f"Sent notification: {notification.title}")
            
        except Exception as e:
            self.logger.error(f"Failed to send notification: {e}")
    
    async def create_error_notification(self, error_info: ErrorInfo) -> Notification:
        """Create notification from error info"""
        # Determine notification type and priority based on error severity
        if error_info.severity == ErrorSeverity.CRITICAL:
            notification_type = NotificationType.CRITICAL
            priority = NotificationPriority.URGENT
        elif error_info.severity == ErrorSeverity.HIGH:
            notification_type = NotificationType.ERROR
            priority = NotificationPriority.HIGH
        elif error_info.severity == ErrorSeverity.MEDIUM:
            notification_type = NotificationType.WARNING
            priority = NotificationPriority.MEDIUM
        else:
            notification_type = NotificationType.INFO
            priority = NotificationPriority.LOW
        
        # Create actions based on error category
        actions = self._create_error_actions(error_info)
        
        notification = Notification(
            notification_id=f"error_{error_info.error_id}",
            type=notification_type,
            priority=priority,
            title=f"{error_info.category.value.title()} Error in {error_info.context.service_name}",
            message=error_info.message,
            timestamp=error_info.timestamp,
            service_name=error_info.context.service_name,
            error_id=error_info.error_id,
            actions=actions,
            auto_dismiss=error_info.severity not in [ErrorSeverity.CRITICAL, ErrorSeverity.HIGH],
            dismiss_timeout=60.0 if error_info.severity == ErrorSeverity.HIGH else 30.0,
            persistent=error_info.severity == ErrorSeverity.CRITICAL,
            tags=[error_info.category.value, error_info.severity.value],
            metadata={
                "exception_type": error_info.exception_type,
                "function_name": error_info.context.function_name
            }
        )
        
        await self.send_notification(notification)
        return notification
    
    def _create_error_actions(self, error_info: ErrorInfo) -> List[NotificationAction]:
        """Create appropriate actions for error notification"""
        actions = []
        
        # Always add dismiss action
        actions.append(NotificationAction(
            action_id="dismiss",
            label="Dismiss",
            action_type="button"
        ))
        
        # Add retry action for retryable errors
        if error_info.category in [ErrorCategory.NETWORK, ErrorCategory.ZENOH]:
            actions.append(NotificationAction(
                action_id="retry",
                label="Retry",
                action_type="button",
                requires_confirmation=error_info.severity == ErrorSeverity.CRITICAL
            ))
        
        # Add restart service action for service errors
        if error_info.category == ErrorCategory.SERVICE:
            actions.append(NotificationAction(
                action_id="restart_service",
                label="Restart Service",
                action_type="button",
                requires_confirmation=True
            ))
        
        # Add view details action
        actions.append(NotificationAction(
            action_id="view_details",
            label="View Details",
            action_type="button"
        ))
        
        return actions
    
    async def create_recovery_notification(self, service_name: str, recovery_action: str, success: bool):
        """Create notification for recovery attempt"""
        notification_type = NotificationType.SUCCESS if success else NotificationType.ERROR
        priority = NotificationPriority.MEDIUM
        
        title = f"Recovery {'Successful' if success else 'Failed'}"
        message = f"Recovery action '{recovery_action}' for service '{service_name}' {'completed successfully' if success else 'failed'}"
        
        notification = Notification(
            notification_id=f"recovery_{service_name}_{int(time.time())}",
            type=notification_type,
            priority=priority,
            title=title,
            message=message,
            timestamp=time.time(),
            service_name=service_name,
            actions=[
                NotificationAction(
                    action_id="dismiss",
                    label="Dismiss",
                    action_type="button"
                )
            ],
            auto_dismiss=True,
            dismiss_timeout=15.0,
            tags=["recovery", "success" if success else "failure"]
        )
        
        await self.send_notification(notification)
        return notification
    
    async def create_user_prompt(self, prompt: UserPrompt):
        """Create user prompt requiring manual input"""
        if not self.is_initialized:
            self.logger.error("Notification manager not initialized")
            return
        
        # Store prompt
        self.pending_prompts[prompt.prompt_id] = prompt
        
        try:
            # Publish prompt
            message = MessageFactory.create_request(
                prompt.to_dict(),
                "notification_manager"
            )
            await self.topic_manager.publish_message("system/user_prompts", message)
            
            # Call prompt callbacks
            for callback in self.prompt_callbacks:
                try:
                    callback(prompt)
                except Exception as e:
                    self.logger.error(f"Error in prompt callback: {e}")
            
            self.logger.info(f"Created user prompt: {prompt.title}")
            
        except Exception as e:
            self.logger.error(f"Failed to create user prompt: {e}")
    
    async def wait_for_prompt_response(self, prompt_id: str, timeout: Optional[float] = None) -> Any:
        """Wait for user response to prompt"""
        if prompt_id not in self.pending_prompts:
            raise ValueError(f"Prompt {prompt_id} not found")
        
        prompt = self.pending_prompts[prompt_id]
        actual_timeout = timeout or prompt.timeout or 300.0
        
        start_time = time.time()
        while time.time() - start_time < actual_timeout:
            if prompt_id in self.prompt_responses:
                response = self.prompt_responses.pop(prompt_id)
                self.pending_prompts.pop(prompt_id, None)
                return response
            
            await asyncio.sleep(0.1)
        
        # Timeout - use default value if available
        self.pending_prompts.pop(prompt_id, None)
        if prompt.default_value is not None:
            self.logger.warning(f"Prompt {prompt_id} timed out, using default value")
            return prompt.default_value
        
        raise TimeoutError(f"Prompt {prompt_id} timed out")
    
    async def _handle_prompt_response(self, message):
        """Handle user response to prompt"""
        try:
            if hasattr(message, 'payload'):
                response_data = message.payload
            else:
                response_data = message
            
            prompt_id = response_data.get("prompt_id")
            response_value = response_data.get("value")
            
            if prompt_id and prompt_id in self.pending_prompts:
                self.prompt_responses[prompt_id] = response_value
                self.logger.info(f"Received response for prompt: {prompt_id}")
                
                # Call response callback if registered
                if prompt_id in self.response_callbacks:
                    callback = self.response_callbacks.pop(prompt_id)
                    try:
                        if asyncio.iscoroutinefunction(callback):
                            await callback(response_value)
                        else:
                            callback(response_value)
                    except Exception as e:
                        self.logger.error(f"Error in response callback: {e}")
        
        except Exception as e:
            self.logger.error(f"Error handling prompt response: {e}")
    
    async def _handle_notification_action(self, message):
        """Handle action taken on notification"""
        try:
            if hasattr(message, 'payload'):
                action_data = message.payload
            else:
                action_data = message
            
            notification_id = action_data.get("notification_id")
            action_id = action_data.get("action_id")
            
            if notification_id in self.active_notifications:
                notification = self.active_notifications[notification_id]
                
                # Find the action
                action = None
                for a in notification.actions:
                    if a.action_id == action_id:
                        action = a
                        break
                
                if action and action.callback:
                    try:
                        if asyncio.iscoroutinefunction(action.callback):
                            await action.callback()
                        else:
                            action.callback()
                    except Exception as e:
                        self.logger.error(f"Error in action callback: {e}")
                
                # Handle standard actions
                if action_id == "dismiss":
                    await self.dismiss_notification(notification_id)
                
                self.logger.info(f"Handled action {action_id} for notification {notification_id}")
        
        except Exception as e:
            self.logger.error(f"Error handling notification action: {e}")
    
    async def dismiss_notification(self, notification_id: str):
        """Dismiss notification"""
        if notification_id in self.active_notifications:
            self.active_notifications.pop(notification_id)
            self.logger.debug(f"Dismissed notification: {notification_id}")
    
    async def _auto_dismiss_loop(self):
        """Auto-dismiss notifications after timeout"""
        try:
            while True:
                current_time = time.time()
                to_dismiss = []
                
                for notification_id, notification in self.active_notifications.items():
                    if (notification.auto_dismiss and not notification.persistent and
                        current_time - notification.timestamp > notification.dismiss_timeout):
                        to_dismiss.append(notification_id)
                
                for notification_id in to_dismiss:
                    await self.dismiss_notification(notification_id)
                
                await asyncio.sleep(10.0)  # Check every 10 seconds
                
        except asyncio.CancelledError:
            self.logger.info("Auto-dismiss loop cancelled")
        except Exception as e:
            self.logger.error(f"Error in auto-dismiss loop: {e}")
    
    def add_notification_callback(self, callback: Callable[[Notification], None]):
        """Add notification callback"""
        self.notification_callbacks.append(callback)
    
    def add_prompt_callback(self, callback: Callable[[UserPrompt], None]):
        """Add prompt callback"""
        self.prompt_callbacks.append(callback)
    
    def register_response_callback(self, prompt_id: str, callback: Callable):
        """Register callback for specific prompt response"""
        self.response_callbacks[prompt_id] = callback
    
    def get_active_notifications(self) -> List[Notification]:
        """Get all active notifications"""
        return list(self.active_notifications.values())
    
    def get_notification_history(self, limit: int = 100) -> List[Notification]:
        """Get notification history"""
        return self.notification_history[-limit:]
    
    def get_pending_prompts(self) -> List[UserPrompt]:
        """Get all pending prompts"""
        return list(self.pending_prompts.values())
    
    async def shutdown(self):
        """Shutdown notification manager"""
        self.logger.info("Shutting down notification manager...")
        
        if self.auto_dismiss_task:
            self.auto_dismiss_task.cancel()
            try:
                await self.auto_dismiss_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("Notification manager shutdown complete")


# Convenience functions for creating common notifications
async def notify_error(session_manager: EnhancedZenohSessionManager, 
                      error_info: ErrorInfo) -> Notification:
    """Convenience function to notify about error"""
    notification_manager = NotificationManager(session_manager)
    await notification_manager.initialize()
    return await notification_manager.create_error_notification(error_info)


async def notify_recovery(session_manager: EnhancedZenohSessionManager,
                         service_name: str, recovery_action: str, success: bool) -> Notification:
    """Convenience function to notify about recovery"""
    notification_manager = NotificationManager(session_manager)
    await notification_manager.initialize()
    return await notification_manager.create_recovery_notification(service_name, recovery_action, success)


async def prompt_user(session_manager: EnhancedZenohSessionManager,
                     title: str, message: str, prompt_type: str = "confirm",
                     timeout: float = 300.0) -> Any:
    """Convenience function to prompt user"""
    notification_manager = NotificationManager(session_manager)
    await notification_manager.initialize()
    
    prompt = UserPrompt(
        prompt_id=f"prompt_{int(time.time())}",
        title=title,
        message=message,
        prompt_type=prompt_type,
        timeout=timeout
    )
    
    await notification_manager.create_user_prompt(prompt)
    return await notification_manager.wait_for_prompt_response(prompt.prompt_id, timeout)