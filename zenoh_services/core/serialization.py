"""
Serialization utilities for Zenoh services.
Provides high-level serialization and deserialization functionality.
"""

import json
import msgpack
import logging
from typing import Any, Dict, Union, Type, TypeVar
from dataclasses import asdict, is_dataclass
from enum import Enum

from .data_models import SerializationMixin

logger = logging.getLogger(__name__)

T = TypeVar('T', bound=SerializationMixin)

class SerializationFormat(Enum):
    """Supported serialization formats"""
    JSON = "json"
    MSGPACK = "msgpack"

class SerializationError(Exception):
    """Exception raised during serialization/deserialization"""
    pass

class Serializer:
    """High-level serialization manager"""
    
    @staticmethod
    def serialize(data: Any, format: SerializationFormat = SerializationFormat.MSGPACK) -> Union[str, bytes]:
        """
        Serialize data to specified format
        
        Args:
            data: Data to serialize (must be SerializationMixin or basic types)
            format: Target serialization format
            
        Returns:
            Serialized data (str for JSON, bytes for MessagePack)
            
        Raises:
            SerializationError: If serialization fails
        """
        try:
            if isinstance(data, SerializationMixin):
                if format == SerializationFormat.JSON:
                    return data.to_json()
                elif format == SerializationFormat.MSGPACK:
                    return data.to_msgpack()
            
            elif is_dataclass(data):
                dict_data = asdict(data)
                return Serializer._serialize_dict(dict_data, format)
            
            elif isinstance(data, dict):
                return Serializer._serialize_dict(data, format)
            
            elif isinstance(data, (list, tuple)):
                return Serializer._serialize_list(data, format)
            
            else:
                return Serializer._serialize_primitive(data, format)
                
        except Exception as e:
            raise SerializationError(f"Failed to serialize data: {str(e)}")
    
    @staticmethod
    def deserialize(data: Union[str, bytes], target_class: Type[T], format: SerializationFormat = None) -> T:
        """
        Deserialize data to target class
        
        Args:
            data: Serialized data (str for JSON, bytes for MessagePack)
            target_class: Target class (must inherit from SerializationMixin)
            format: Source format (auto-detected if None)
            
        Returns:
            Deserialized object instance
            
        Raises:
            SerializationError: If deserialization fails
        """
        try:
            # Auto-detect format if not specified
            if format is None:
                format = Serializer._detect_format(data)
            
            # Validate target class
            if not issubclass(target_class, SerializationMixin):
                raise SerializationError(f"Target class {target_class} must inherit from SerializationMixin")
            
            # Deserialize based on format
            if format == SerializationFormat.JSON:
                if isinstance(data, bytes):
                    data = data.decode('utf-8')
                return target_class.from_json(data)
            
            elif format == SerializationFormat.MSGPACK:
                if isinstance(data, str):
                    data = data.encode('utf-8')
                return target_class.from_msgpack(data)
            
            else:
                raise SerializationError(f"Unsupported format: {format}")
                
        except Exception as e:
            raise SerializationError(f"Failed to deserialize data to {target_class}: {str(e)}")
    
    @staticmethod
    def _serialize_dict(data: Dict[str, Any], format: SerializationFormat) -> Union[str, bytes]:
        """Serialize dictionary data"""
        # Handle nested objects
        serializable_data = {}
        for key, value in data.items():
            if isinstance(value, SerializationMixin):
                serializable_data[key] = value.to_dict()
            elif isinstance(value, Enum):
                serializable_data[key] = value.value
            elif isinstance(value, (list, tuple)):
                serializable_data[key] = [
                    item.to_dict() if isinstance(item, SerializationMixin) else item
                    for item in value
                ]
            else:
                serializable_data[key] = value
        
        if format == SerializationFormat.JSON:
            return json.dumps(serializable_data)
        elif format == SerializationFormat.MSGPACK:
            return msgpack.packb(serializable_data)
    
    @staticmethod
    def _serialize_list(data: Union[list, tuple], format: SerializationFormat) -> Union[str, bytes]:
        """Serialize list/tuple data"""
        serializable_data = []
        for item in data:
            if isinstance(item, SerializationMixin):
                serializable_data.append(item.to_dict())
            elif isinstance(item, Enum):
                serializable_data.append(item.value)
            else:
                serializable_data.append(item)
        
        if format == SerializationFormat.JSON:
            return json.dumps(serializable_data)
        elif format == SerializationFormat.MSGPACK:
            return msgpack.packb(serializable_data)
    
    @staticmethod
    def _serialize_primitive(data: Any, format: SerializationFormat) -> Union[str, bytes]:
        """Serialize primitive data types"""
        if isinstance(data, Enum):
            data = data.value
        
        if format == SerializationFormat.JSON:
            return json.dumps(data)
        elif format == SerializationFormat.MSGPACK:
            return msgpack.packb(data)
    
    @staticmethod
    def _detect_format(data: Union[str, bytes]) -> SerializationFormat:
        """Auto-detect serialization format"""
        if isinstance(data, bytes):
            # Try to detect MessagePack magic bytes or fallback to MessagePack
            try:
                msgpack.unpackb(data, raw=False)
                return SerializationFormat.MSGPACK
            except:
                # If it's bytes but not valid MessagePack, assume JSON
                try:
                    json.loads(data.decode('utf-8'))
                    return SerializationFormat.JSON
                except:
                    return SerializationFormat.MSGPACK  # Default fallback
        
        elif isinstance(data, str):
            # String data is assumed to be JSON
            return SerializationFormat.JSON
        
        else:
            return SerializationFormat.JSON  # Default fallback

class MessageValidator:
    """Validator for serialized messages"""
    
    @staticmethod
    def validate_json(data: str) -> bool:
        """Validate JSON format"""
        try:
            json.loads(data)
            return True
        except:
            return False
    
    @staticmethod
    def validate_msgpack(data: bytes) -> bool:
        """Validate MessagePack format"""
        try:
            msgpack.unpackb(data, raw=False)
            return True
        except:
            return False
    
    @staticmethod
    def validate_data_model(instance: SerializationMixin) -> bool:
        """Validate data model instance"""
        try:
            # Check if instance has validate method and call it
            if hasattr(instance, 'validate') and callable(getattr(instance, 'validate')):
                return instance.validate()
            return True
        except:
            return False
    
    @staticmethod
    def validate_round_trip(original: SerializationMixin, format: SerializationFormat = SerializationFormat.MSGPACK) -> bool:
        """Validate serialization round-trip"""
        try:
            # Serialize
            serialized = Serializer.serialize(original, format)
            
            # Deserialize
            deserialized = Serializer.deserialize(serialized, type(original), format)
            
            # Compare (basic comparison)
            return original.to_dict() == deserialized.to_dict()
        except:
            return False

# Convenience functions
def serialize_to_json(data: Any) -> str:
    """Serialize data to JSON string"""
    return Serializer.serialize(data, SerializationFormat.JSON)

def serialize_to_msgpack(data: Any) -> bytes:
    """Serialize data to MessagePack bytes"""
    return Serializer.serialize(data, SerializationFormat.MSGPACK)

def deserialize_from_json(data: str, target_class: Type[T]) -> T:
    """Deserialize JSON string to target class"""
    return Serializer.deserialize(data, target_class, SerializationFormat.JSON)

def deserialize_from_msgpack(data: bytes, target_class: Type[T]) -> T:
    """Deserialize MessagePack bytes to target class"""
    return Serializer.deserialize(data, target_class, SerializationFormat.MSGPACK)