from dataclasses import dataclass, field
from typing import List, Dict, Tuple, Optional, Any, Union
import time
import json
import msgpack
from abc import ABC, abstractmethod
from enum import Enum

class SerializationMixin(ABC):
    """Mixin class providing serialization capabilities"""
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        if hasattr(self, '__dataclass_fields__'):
            result = {}
            for field_name, field_def in self.__dataclass_fields__.items():
                value = getattr(self, field_name)
                if isinstance(value, SerializationMixin):
                    result[field_name] = value.to_dict()
                elif isinstance(value, list) and value and isinstance(value[0], SerializationMixin):
                    result[field_name] = [item.to_dict() for item in value]
                elif isinstance(value, Enum):
                    result[field_name] = value.value
                else:
                    result[field_name] = value
            return result
        return self.__dict__
    
    def to_json(self) -> str:
        """Convert to JSON string"""
        return json.dumps(self.to_dict())
    
    def to_msgpack(self) -> bytes:
        """Convert to MessagePack bytes"""
        return msgpack.packb(self.to_dict())
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]):
        """Create instance from dictionary"""
        if hasattr(cls, '__dataclass_fields__'):
            kwargs = {}
            for field_name, field_def in cls.__dataclass_fields__.items():
                if field_name in data:
                    field_type = field_def.type
                    value = data[field_name]
                    
                    # Handle Union types (like Optional)
                    if hasattr(field_type, '__origin__') and field_type.__origin__ is Union:
                        # Get the non-None type from Optional
                        actual_types = [t for t in field_type.__args__ if t is not type(None)]
                        if actual_types:
                            field_type = actual_types[0]
                    
                    # Handle Enum types
                    if hasattr(field_type, '__bases__') and Enum in field_type.__bases__:
                        # Convert string value to enum
                        try:
                            kwargs[field_name] = field_type(value)
                        except ValueError:
                            kwargs[field_name] = value
                    # Handle nested SerializationMixin objects
                    elif hasattr(field_type, '__bases__') and SerializationMixin in field_type.__bases__:
                        kwargs[field_name] = field_type.from_dict(value)
                    else:
                        kwargs[field_name] = value
            return cls(**kwargs)
        return cls(**data)
    
    @classmethod
    def from_json(cls, json_str: str):
        """Create instance from JSON string"""
        return cls.from_dict(json.loads(json_str))
    
    @classmethod
    def from_msgpack(cls, data: bytes):
        """Create instance from MessagePack bytes"""
        return cls.from_dict(msgpack.unpackb(data, raw=False))

class TrainingState(Enum):
    """Training state enumeration"""
    IDLE = "idle"
    STARTING = "starting"
    TRAINING = "training"
    PAUSED = "paused"
    STOPPING = "stopping"
    COMPLETED = "completed"
    ERROR = "error"

class PlayState(Enum):
    """Play/test state enumeration"""
    IDLE = "idle"
    LOADING = "loading"
    RUNNING = "running"
    RECORDING = "recording"
    STOPPED = "stopped"
    ERROR = "error"

class DeploymentState(Enum):
    """Deployment state enumeration"""
    IDLE = "idle"
    EXPORTING = "exporting"
    VALIDATING = "validating"
    DEPLOYING = "deploying"
    COMPLETED = "completed"
    ERROR = "error"

# Training related data models

@dataclass
class TrainingConfig(SerializationMixin):
    """Configuration for training tasks"""
    task_name: str = "zqsa01"
    num_envs: int = 4096
    max_iterations: int = 1000
    learning_rate: float = 3e-4
    batch_size: int = 24576
    experiment_name: str = "default"
    run_name: str = "run_001"
    resume: bool = False
    checkpoint: int = -1
    
    def validate(self) -> bool:
        """Validate training configuration"""
        return (
            self.num_envs > 0 and
            self.max_iterations > 0 and
            self.learning_rate > 0 and
            self.batch_size > 0 and
            len(self.task_name.strip()) > 0 and
            len(self.experiment_name.strip()) > 0
        )
    
@dataclass
class TrainingMetrics(SerializationMixin):
    """Training metrics data"""
    iteration: int
    mean_reward: float
    std_reward: float
    mean_episode_length: float
    learning_rate: float
    entropy: float
    kl_divergence: float
    policy_loss: float
    value_loss: float
    timestamp: float
    additional_metrics: Dict[str, float] = field(default_factory=dict)

@dataclass
class TrainingStatus(SerializationMixin):
    """Current training status"""
    state: TrainingState
    current_iteration: int
    total_iterations: int
    start_time: float
    last_update: float
    status_message: str
    progress_percentage: float = 0.0
    estimated_time_remaining: Optional[float] = None
    
    @property
    def is_active(self) -> bool:
        """Check if training is currently active"""
        return self.state in [TrainingState.TRAINING, TrainingState.STARTING]
    
    @property
    def is_paused(self) -> bool:
        """Check if training is paused"""
        return self.state == TrainingState.PAUSED
    
    @property
    def elapsed_time(self) -> float:
        """Get elapsed training time"""
        return time.time() - self.start_time

# Robot and simulation data models

@dataclass
class RobotCommand(SerializationMixin):
    """Command to control the robot"""
    linear_velocity: Tuple[float, float, float]  # x, y, z
    angular_velocity: Tuple[float, float, float]  # roll, pitch, yaw
    timestamp: float
    command_id: str = ""
    
    def validate(self) -> bool:
        """Validate robot command"""
        return (
            len(self.linear_velocity) == 3 and
            len(self.angular_velocity) == 3 and
            all(isinstance(v, (int, float)) for v in self.linear_velocity) and
            all(isinstance(v, (int, float)) for v in self.angular_velocity)
        )

@dataclass
class RobotState(SerializationMixin):
    """Current state of the robot"""
    joint_positions: List[float]  # 12 joint positions
    joint_velocities: List[float]  # 12 joint velocities
    joint_torques: List[float]    # 12 joint torques
    base_position: Tuple[float, float, float]
    base_orientation: Tuple[float, float, float, float]  # quaternion
    base_linear_velocity: Tuple[float, float, float]
    base_angular_velocity: Tuple[float, float, float]
    contact_forces: List[float]   # foot contact forces
    timestamp: float
    robot_id: str = "robot_0"
    
    def validate(self) -> bool:
        """Validate robot state"""
        return (
            len(self.joint_positions) == 12 and
            len(self.joint_velocities) == 12 and
            len(self.joint_torques) == 12 and
            len(self.base_position) == 3 and
            len(self.base_orientation) == 4 and
            len(self.base_linear_velocity) == 3 and
            len(self.base_angular_velocity) == 3
        )

@dataclass
class PlayConfig(SerializationMixin):
    """Configuration for play/test mode"""
    task_name: str = "zqsa01"
    model_path: str = ""
    num_envs: int = 1
    record_video: bool = False
    video_path: str = ""
    use_joystick: bool = False
    fixed_commands: bool = False
    max_episode_length: int = 1000
    
    def validate(self) -> bool:
        """Validate play configuration"""
        return (
            self.num_envs > 0 and
            len(self.task_name.strip()) > 0 and
            self.max_episode_length > 0
        )

@dataclass
class PlayStatus(SerializationMixin):
    """Current play/test status"""
    state: PlayState
    current_episode: int
    current_step: int
    start_time: float
    last_update: float
    status_message: str
    is_recording: bool = False
    
    @property
    def is_active(self) -> bool:
        """Check if play session is active"""
        return self.state in [PlayState.RUNNING, PlayState.RECORDING]

# Simulation configuration data models

@dataclass
class TerrainConfig(SerializationMixin):
    """Terrain configuration for simulation"""
    mesh_type: str = "plane"  # plane, trimesh
    curriculum: bool = False
    static_friction: float = 0.6
    dynamic_friction: float = 0.6
    restitution: float = 0.0
    terrain_length: float = 8.0
    terrain_width: float = 8.0
    horizontal_scale: float = 0.1
    vertical_scale: float = 0.005
    border_size: float = 0.0
    slope_threshold: float = 0.75
    num_rows: int = 5
    num_cols: int = 5
    max_init_terrain_level: int = 5
    
    def validate(self) -> bool:
        """Validate terrain configuration"""
        return (
            self.mesh_type in ["plane", "trimesh"] and
            self.static_friction >= 0 and
            self.dynamic_friction >= 0 and
            self.restitution >= 0 and
            self.terrain_length > 0 and
            self.terrain_width > 0 and
            self.num_rows > 0 and
            self.num_cols > 0
        )

@dataclass
class PhysicsConfig(SerializationMixin):
    """Physics simulation configuration"""
    dt: float = 0.001
    substeps: int = 1
    num_threads: int = 10
    gravity: List[float] = field(default_factory=lambda: [0.0, 0.0, -9.81])
    solver_type: int = 1  # 0=PGS, 1=TGS
    num_position_iterations: int = 4
    num_velocity_iterations: int = 1
    contact_offset: float = 0.02
    rest_offset: float = 0.0
    bounce_threshold_velocity: float = 0.2
    max_depenetration_velocity: float = 1.0
    
    def validate(self) -> bool:
        """Validate physics configuration"""
        return (
            self.dt > 0 and
            self.substeps > 0 and
            self.num_threads > 0 and
            self.solver_type in [0, 1] and
            self.num_position_iterations >= 0 and
            self.num_velocity_iterations >= 0 and
            len(self.gravity) == 3
        )

@dataclass
class RobotConfig(SerializationMixin):
    """Robot configuration parameters"""
    name: str = "anymal_c"
    urdf_path: str = ""
    base_mass: float = 35.0
    joint_stiffness: float = 80.0
    joint_damping: float = 2.0
    contact_friction: float = 1.0
    control_frequency: float = 50.0
    control_type: str = "P"  # P, PD, torque
    action_scale: float = 0.5
    decimation: int = 10
    stiffness: Dict[str, float] = field(default_factory=dict)
    damping: Dict[str, float] = field(default_factory=dict)
    
    def validate(self) -> bool:
        """Validate robot configuration"""
        return (
            len(self.name.strip()) > 0 and
            self.base_mass > 0 and
            self.joint_stiffness >= 0 and
            self.joint_damping >= 0 and
            self.contact_friction >= 0 and
            self.control_frequency > 0 and
            self.control_type in ["P", "PD", "torque"] and
            self.action_scale > 0 and
            self.decimation > 0
        )

@dataclass
class SimulationConfig(SerializationMixin):
    """Complete simulation configuration"""
    terrain: TerrainConfig = field(default_factory=TerrainConfig)
    physics: PhysicsConfig = field(default_factory=PhysicsConfig)
    robot: RobotConfig = field(default_factory=RobotConfig)
    num_envs: int = 4096
    episode_length: int = 1000
    simulation_name: str = "default_simulation"
    version: str = "1.0.0"
    
    def validate(self) -> bool:
        """Validate complete simulation configuration"""
        return (
            self.terrain.validate() and
            self.physics.validate() and
            self.robot.validate()
        )

@dataclass
class SimulationMetrics(SerializationMixin):
    """Simulation performance metrics"""
    fps: float
    step_time: float
    physics_time: float
    render_time: float
    memory_usage: float
    timestamp: float
    additional_metrics: Dict[str, float] = field(default_factory=dict)

# Deployment data models

@dataclass
class ModelInfo(SerializationMixin):
    """Information about a trained model"""
    model_path: str
    model_name: str
    training_config: TrainingConfig
    performance_metrics: Dict[str, float] = field(default_factory=dict)
    creation_time: float = 0.0
    file_size: int = 0
    model_format: str = "pt"  # pt, onnx, jit
    
    def validate(self) -> bool:
        """Validate model information"""
        return (
            len(self.model_path.strip()) > 0 and
            len(self.model_name.strip()) > 0 and
            self.model_format in ["pt", "onnx", "jit"] and
            self.file_size >= 0
        )

@dataclass
class DeploymentConfig(SerializationMixin):
    """Configuration for model deployment"""
    model_path: str
    export_format: str = "jit"  # jit, onnx
    export_path: str = ""
    target_platform: str = "cpu"  # cpu, gpu, robot
    optimization_level: str = "default"  # default, fast, accurate
    
    def validate(self) -> bool:
        """Validate deployment configuration"""
        return (
            len(self.model_path.strip()) > 0 and
            self.export_format in ["jit", "onnx"] and
            self.target_platform in ["cpu", "gpu", "robot"] and
            self.optimization_level in ["default", "fast", "accurate"]
        )

@dataclass
class DeploymentStatus(SerializationMixin):
    """Status of deployment process"""
    state: DeploymentState
    progress: float  # 0.0 to 1.0
    status_message: str
    last_update: float
    start_time: float = 0.0
    exported_model_path: str = ""
    
    @property
    def is_active(self) -> bool:
        """Check if deployment is active"""
        return self.state in [DeploymentState.EXPORTING, DeploymentState.VALIDATING, DeploymentState.DEPLOYING]

# System data models

@dataclass
class SystemStatus(SerializationMixin):
    """Overall system status"""
    is_healthy: bool
    active_services: List[str] = field(default_factory=list)
    system_load: Dict[str, float] = field(default_factory=dict)  # cpu, memory, gpu
    error_count: int = 0
    last_update: float = 0.0
    uptime: float = 0.0

@dataclass
class ErrorInfo(SerializationMixin):
    """System error information"""
    error_type: str
    error_message: str
    context: Dict[str, Any] = field(default_factory=dict)
    timestamp: float = 0.0
    severity: str = "error"  # info, warning, error, critical
    resolved: bool = False
    
    def validate(self) -> bool:
        """Validate error information"""
        return (
            len(self.error_type.strip()) > 0 and
            len(self.error_message.strip()) > 0 and
            self.severity in ["info", "warning", "error", "critical"]
        )

# Command and response data models

@dataclass
class CommandMessage(SerializationMixin):
    """Generic command message"""
    command: str
    parameters: Dict[str, Any] = field(default_factory=dict)
    command_id: str = ""
    timestamp: float = field(default_factory=time.time)
    
    def validate(self) -> bool:
        """Validate command message"""
        return len(self.command.strip()) > 0

@dataclass
class TrainingCommand(SerializationMixin):
    """Training command message"""
    command: str  # start, stop, pause, resume, save
    config: Optional['TrainingConfig'] = None
    command_id: str = ""
    timestamp: float = field(default_factory=time.time)
    
    def validate(self) -> bool:
        """Validate training command"""
        valid_commands = ["start", "stop", "pause", "resume", "save"]
        return self.command in valid_commands

@dataclass
class RobotControlCommand(SerializationMixin):
    """Robot control command message"""
    command_type: str  # velocity, position, action
    target_values: Dict[str, float] = field(default_factory=dict)
    command_id: str = ""
    timestamp: float = field(default_factory=time.time)

    def validate(self) -> bool:
        """Validate robot command"""
        valid_types = ["velocity", "position", "action"]
        return self.command_type in valid_types

@dataclass
class ResponseMessage(SerializationMixin):
    """Generic response message"""
    success: bool
    message: str
    data: Dict[str, Any] = field(default_factory=dict)
    command_id: str = ""
    timestamp: float = field(default_factory=time.time)

@dataclass
class ConfigurationRequest(SerializationMixin):
    """Configuration request message"""
    config_type: str  # training, simulation, deployment, etc.
    action: str  # get, set, validate, reset
    config_data: Dict[str, Any] = field(default_factory=dict)
    request_id: str = ""
    timestamp: float = field(default_factory=time.time)

@dataclass
class ConfigurationResponse(SerializationMixin):
    """Configuration response message"""
    success: bool
    config_type: str
    config_data: Dict[str, Any] = field(default_factory=dict)
    error_message: str = ""
    request_id: str = ""
    timestamp: float = field(default_factory=time.time)