"""
Quality of Service (QoS) configuration and message persistence strategies for Zenoh services.
"""

import asyncio
import logging
import time
import json
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field, asdict
from enum import Enum
from collections import defaultdict, deque
import threading
import pickle
import os

from .enhanced_session_manager import QoSLevel, TopicConfig
from .message_format import StandardZenohMessage, MessageType, Priority
from .data_models import SerializationMixin

logger = logging.getLogger(__name__)

class PersistenceStrategy(Enum):
    """Message persistence strategies"""
    NONE = "none"                    # No persistence
    MEMORY = "memory"                # In-memory storage
    FILE = "file"                    # File-based storage
    DATABASE = "database"            # Database storage (future)
    HYBRID = "hybrid"                # Memory + File for backup

class QoSProfile(Enum):
    """Predefined QoS profiles for different use cases"""
    SENSOR_DATA = "sensor_data"      # High frequency, best effort, volatile
    COMMAND = "command"              # Reliable delivery, persistent
    STATUS = "status"                # Reliable, persistent, with history
    CRITICAL = "critical"            # Reliable, persistent, high priority
    HEARTBEAT = "heartbeat"          # Best effort, volatile, regular interval

@dataclass
class QoSConfiguration:
    """Comprehensive QoS configuration"""
    # Basic QoS
    reliability: QoSLevel = QoSLevel.RELIABLE
    durability: QoSLevel = QoSLevel.PERSISTENT
    
    # Message handling
    max_message_size: int = 1024 * 1024  # 1MB
    message_ttl: float = 300.0           # 5 minutes
    max_queue_size: int = 1000
    
    # Delivery guarantees
    enable_acknowledgment: bool = True
    ack_timeout: float = 5.0
    max_retries: int = 3
    retry_backoff: float = 1.0
    
    # Ordering and filtering
    preserve_order: bool = True
    enable_deduplication: bool = True
    content_filter: Optional[str] = None
    
    # Performance
    batch_size: int = 10
    batch_timeout: float = 0.1
    compression_enabled: bool = False
    
    # Priority handling
    priority_levels: int = 4
    priority_queue_sizes: Dict[Priority, int] = field(default_factory=lambda: {
        Priority.LOW: 100,
        Priority.NORMAL: 500,
        Priority.HIGH: 1000,
        Priority.CRITICAL: 2000
    })

@dataclass
class PersistenceConfiguration:
    """Message persistence configuration"""
    strategy: PersistenceStrategy = PersistenceStrategy.MEMORY
    
    # Memory settings
    max_memory_messages: int = 10000
    memory_cleanup_threshold: float = 0.8  # Cleanup when 80% full
    
    # File settings
    file_path: str = "./zenoh_persistence"
    max_file_size: int = 100 * 1024 * 1024  # 100MB
    file_rotation_count: int = 5
    sync_interval: float = 10.0  # Sync to disk every 10 seconds
    
    # Retention settings
    message_retention_time: float = 86400.0  # 24 hours
    max_total_size: int = 1024 * 1024 * 1024  # 1GB
    
    # Recovery settings
    enable_recovery: bool = True
    recovery_timeout: float = 30.0

class QoSManager:
    """
    Manages Quality of Service parameters and enforcement.
    """
    
    def __init__(self):
        self.qos_configs: Dict[str, QoSConfiguration] = {}
        self.topic_profiles: Dict[str, QoSProfile] = {}
        self.message_queues: Dict[str, Dict[Priority, deque]] = defaultdict(
            lambda: {
                Priority.LOW: deque(),
                Priority.NORMAL: deque(),
                Priority.HIGH: deque(),
                Priority.CRITICAL: deque()
            }
        )
        self.pending_acks: Dict[str, Dict[str, float]] = defaultdict(dict)  # topic -> {msg_id: timestamp}
        self.delivery_stats: Dict[str, Dict[str, Any]] = defaultdict(lambda: {
            'sent': 0, 'delivered': 0, 'failed': 0, 'retries': 0
        })
        
        # Initialize standard profiles
        self._initialize_standard_profiles()
    
    def _initialize_standard_profiles(self):
        """Initialize standard QoS profiles"""
        profiles = {
            QoSProfile.SENSOR_DATA: QoSConfiguration(
                reliability=QoSLevel.BEST_EFFORT,
                durability=QoSLevel.PERSISTENT,
                max_queue_size=100,
                enable_acknowledgment=False,
                preserve_order=False,
                batch_size=50,
                batch_timeout=0.05
            ),
            
            QoSProfile.COMMAND: QoSConfiguration(
                reliability=QoSLevel.RELIABLE,
                durability=QoSLevel.PERSISTENT,
                enable_acknowledgment=True,
                max_retries=5,
                preserve_order=True,
                message_ttl=60.0
            ),
            
            QoSProfile.STATUS: QoSConfiguration(
                reliability=QoSLevel.RELIABLE,
                durability=QoSLevel.PERSISTENT,
                enable_acknowledgment=True,
                max_retries=3,
                preserve_order=True,
                message_ttl=300.0
            ),
            
            QoSProfile.CRITICAL: QoSConfiguration(
                reliability=QoSLevel.RELIABLE,
                durability=QoSLevel.PERSISTENT,
                enable_acknowledgment=True,
                max_retries=10,
                retry_backoff=0.5,
                preserve_order=True,
                message_ttl=600.0,
                priority_queue_sizes={
                    Priority.CRITICAL: 5000,
                    Priority.HIGH: 2000,
                    Priority.NORMAL: 500,
                    Priority.LOW: 100
                }
            ),
            
            QoSProfile.HEARTBEAT: QoSConfiguration(
                reliability=QoSLevel.BEST_EFFORT,
                durability=QoSLevel.PERSISTENT,
                enable_acknowledgment=False,
                max_queue_size=10,  # Keep only recent heartbeats
                preserve_order=False,
                enable_deduplication=False,
                message_ttl=60.0
            )
        }
        
        for profile, config in profiles.items():
            self.qos_configs[profile.value] = config
        
        logger.info(f"Initialized {len(profiles)} QoS profiles")
    
    def set_topic_profile(self, topic: str, profile: QoSProfile):
        """Set QoS profile for a topic"""
        self.topic_profiles[topic] = profile
        logger.debug(f"Set QoS profile for topic {topic}: {profile.value}")
    
    def get_topic_qos(self, topic: str) -> QoSConfiguration:
        """Get QoS configuration for a topic"""
        if topic in self.topic_profiles:
            profile = self.topic_profiles[topic]
            return self.qos_configs[profile.value]
        
        # Default configuration
        return self.qos_configs[QoSProfile.STATUS.value]
    
    def configure_topic_qos(self, topic: str, qos_config: QoSConfiguration):
        """Set custom QoS configuration for a topic"""
        self.qos_configs[f"topic_{topic}"] = qos_config
        self.topic_profiles[topic] = f"topic_{topic}"
    
    async def enqueue_message(self, topic: str, message: StandardZenohMessage) -> bool:
        """Enqueue message with QoS enforcement"""
        try:
            qos = self.get_topic_qos(topic)
            priority = message.header.priority
            
            # Check queue size limits
            queue = self.message_queues[topic][priority]
            max_size = qos.priority_queue_sizes.get(priority, qos.max_queue_size)
            
            if len(queue) >= max_size:
                # Remove oldest message if queue is full
                if qos.reliability == QoSLevel.BEST_EFFORT:
                    queue.popleft()
                else:
                    # For reliable delivery, reject new message
                    logger.warning(f"Queue full for topic {topic}, priority {priority.value}")
                    return False
            
            # Add message to queue
            queue.append({
                'message': message,
                'timestamp': time.time(),
                'retry_count': 0
            })
            
            return True
            
        except Exception as e:
            logger.error(f"Error enqueueing message for topic {topic}: {e}")
            return False
    
    async def dequeue_message(self, topic: str) -> Optional[StandardZenohMessage]:
        """Dequeue next message based on priority"""
        try:
            # Check queues in priority order
            for priority in [Priority.CRITICAL, Priority.HIGH, Priority.NORMAL, Priority.LOW]:
                queue = self.message_queues[topic][priority]
                if queue:
                    message_entry = queue.popleft()
                    
                    # Check message TTL
                    qos = self.get_topic_qos(topic)
                    if time.time() - message_entry['timestamp'] > qos.message_ttl:
                        logger.debug(f"Message expired for topic {topic}")
                        continue
                    
                    return message_entry['message']
            
            return None
            
        except Exception as e:
            logger.error(f"Error dequeuing message for topic {topic}: {e}")
            return None
    
    async def handle_acknowledgment(self, topic: str, message_id: str, success: bool):
        """Handle message acknowledgment"""
        try:
            if topic in self.pending_acks and message_id in self.pending_acks[topic]:
                # Remove from pending
                del self.pending_acks[topic][message_id]
                
                # Update stats
                if success:
                    self.delivery_stats[topic]['delivered'] += 1
                else:
                    self.delivery_stats[topic]['failed'] += 1
                    
                logger.debug(f"Handled ack for {topic}/{message_id}: {success}")
                
        except Exception as e:
            logger.error(f"Error handling acknowledgment: {e}")
    
    async def cleanup_expired_messages(self):
        """Clean up expired messages and acknowledgments"""
        current_time = time.time()
        
        for topic in list(self.message_queues.keys()):
            qos = self.get_topic_qos(topic)
            
            # Clean up message queues
            for priority in Priority:
                queue = self.message_queues[topic][priority]
                expired_count = 0
                
                # Remove expired messages from front of queue
                while queue and (current_time - queue[0]['timestamp']) > qos.message_ttl:
                    queue.popleft()
                    expired_count += 1
                
                if expired_count > 0:
                    logger.debug(f"Cleaned up {expired_count} expired messages from {topic}/{priority.value}")
            
            # Clean up pending acknowledgments
            if topic in self.pending_acks:
                expired_acks = []
                for msg_id, timestamp in self.pending_acks[topic].items():
                    if current_time - timestamp > qos.ack_timeout:
                        expired_acks.append(msg_id)
                
                for msg_id in expired_acks:
                    del self.pending_acks[topic][msg_id]
                    self.delivery_stats[topic]['failed'] += 1

class MessagePersistenceManager:
    """
    Manages message persistence across different storage strategies.
    """
    
    def __init__(self, config: PersistenceConfiguration = None):
        self.config = config or PersistenceConfiguration()
        
        # Storage backends
        self.memory_store: Dict[str, deque] = defaultdict(lambda: deque(maxlen=self.config.max_memory_messages))
        self.file_handles: Dict[str, Any] = {}
        
        # Synchronization
        self.lock = threading.RLock()
        self.background_tasks = []
        
        # Initialize storage
        self._initialize_storage()
    
    def _initialize_storage(self):
        """Initialize storage backend"""
        if self.config.strategy in [PersistenceStrategy.FILE, PersistenceStrategy.HYBRID]:
            # Ensure persistence directory exists
            os.makedirs(self.config.file_path, exist_ok=True)
            logger.info(f"Initialized file persistence at {self.config.file_path}")
    
    async def store_message(self, topic: str, message: StandardZenohMessage):
        """Store message according to persistence strategy"""
        try:
            with self.lock:
                if self.config.strategy == PersistenceStrategy.NONE:
                    return
                
                elif self.config.strategy == PersistenceStrategy.MEMORY:
                    self._store_in_memory(topic, message)
                
                elif self.config.strategy == PersistenceStrategy.FILE:
                    await self._store_in_file(topic, message)
                
                elif self.config.strategy == PersistenceStrategy.HYBRID:
                    self._store_in_memory(topic, message)
                    await self._store_in_file(topic, message)
                    
        except Exception as e:
            logger.error(f"Error storing message for topic {topic}: {e}")
    
    def _store_in_memory(self, topic: str, message: StandardZenohMessage):
        """Store message in memory"""
        self.memory_store[topic].append({
            'timestamp': time.time(),
            'message': message,
            'size': len(message.to_msgpack())
        })
    
    async def _store_in_file(self, topic: str, message: StandardZenohMessage):
        """Store message in file"""
        try:
            # Create topic-specific file if needed
            file_path = os.path.join(self.config.file_path, f"{topic.replace('/', '_')}.jsonl")
            
            # Prepare message data
            message_data = {
                'timestamp': time.time(),
                'message': message.to_dict()
            }
            
            # Write to file
            with open(file_path, 'a', encoding='utf-8') as f:
                f.write(json.dumps(message_data) + '\n')
                
        except Exception as e:
            logger.error(f"Error writing to file for topic {topic}: {e}")
    
    async def retrieve_messages(self, topic: str, count: int = None, since: float = None) -> List[StandardZenohMessage]:
        """Retrieve messages from persistence storage"""
        try:
            messages = []
            
            # Retrieve from memory first
            if self.config.strategy in [PersistenceStrategy.MEMORY, PersistenceStrategy.HYBRID]:
                memory_messages = self._retrieve_from_memory(topic, count, since)
                messages.extend(memory_messages)
            
            # Retrieve from file if needed
            if self.config.strategy in [PersistenceStrategy.FILE, PersistenceStrategy.HYBRID] and len(messages) < (count or float('inf')):
                file_messages = await self._retrieve_from_file(topic, count, since)
                messages.extend(file_messages)
            
            # Sort by timestamp and limit
            messages.sort(key=lambda m: m.header.timestamp)
            
            if count:
                messages = messages[-count:]
            
            return messages
            
        except Exception as e:
            logger.error(f"Error retrieving messages for topic {topic}: {e}")
            return []
    
    def _retrieve_from_memory(self, topic: str, count: int = None, since: float = None) -> List[StandardZenohMessage]:
        """Retrieve messages from memory storage"""
        messages = []
        
        if topic in self.memory_store:
            for entry in self.memory_store[topic]:
                if since and entry['timestamp'] < since:
                    continue
                messages.append(entry['message'])
        
        return messages[-count:] if count else messages
    
    async def _retrieve_from_file(self, topic: str, count: int = None, since: float = None) -> List[StandardZenohMessage]:
        """Retrieve messages from file storage"""
        messages = []
        
        try:
            file_path = os.path.join(self.config.file_path, f"{topic.replace('/', '_')}.jsonl")
            
            if not os.path.exists(file_path):
                return messages
            
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        data = json.loads(line.strip())
                        
                        if since and data['timestamp'] < since:
                            continue
                        
                        message = StandardZenohMessage.from_dict(data['message'])
                        messages.append(message)
                        
                    except Exception as e:
                        logger.warning(f"Error parsing message from file: {e}")
                        continue
            
        except Exception as e:
            logger.error(f"Error reading from file for topic {topic}: {e}")
        
        return messages[-count:] if count else messages
    
    async def cleanup_old_messages(self):
        """Clean up old messages based on retention policy"""
        try:
            current_time = time.time()
            retention_threshold = current_time - self.config.message_retention_time
            
            # Clean up memory storage
            for topic in list(self.memory_store.keys()):
                store = self.memory_store[topic]
                original_size = len(store)
                
                # Remove old messages
                while store and store[0]['timestamp'] < retention_threshold:
                    store.popleft()
                
                cleaned_count = original_size - len(store)
                if cleaned_count > 0:
                    logger.debug(f"Cleaned up {cleaned_count} old messages from memory for topic {topic}")
            
            # Clean up file storage
            if self.config.strategy in [PersistenceStrategy.FILE, PersistenceStrategy.HYBRID]:
                await self._cleanup_old_files(retention_threshold)
                
        except Exception as e:
            logger.error(f"Error during message cleanup: {e}")
    
    async def _cleanup_old_files(self, retention_threshold: float):
        """Clean up old messages from files"""
        try:
            if not os.path.exists(self.config.file_path):
                return
            
            for filename in os.listdir(self.config.file_path):
                if not filename.endswith('.jsonl'):
                    continue
                
                file_path = os.path.join(self.config.file_path, filename)
                temp_path = file_path + '.tmp'
                
                kept_lines = 0
                removed_lines = 0
                
                # Rewrite file without old messages
                with open(file_path, 'r', encoding='utf-8') as infile, \
                     open(temp_path, 'w', encoding='utf-8') as outfile:
                    
                    for line in infile:
                        try:
                            data = json.loads(line.strip())
                            if data['timestamp'] >= retention_threshold:
                                outfile.write(line)
                                kept_lines += 1
                            else:
                                removed_lines += 1
                        except:
                            # Keep malformed lines
                            outfile.write(line)
                
                # Replace original file if we removed any lines
                if removed_lines > 0:
                    os.replace(temp_path, file_path)
                    logger.debug(f"Cleaned up {removed_lines} old messages from file {filename}")
                else:
                    os.remove(temp_path)
                    
        except Exception as e:
            logger.error(f"Error cleaning up old files: {e}")
    
    async def get_storage_stats(self) -> Dict[str, Any]:
        """Get storage statistics"""
        stats = {
            "strategy": self.config.strategy.value,
            "memory": {
                "topics": len(self.memory_store),
                "total_messages": sum(len(store) for store in self.memory_store.values()),
                "total_size_bytes": sum(
                    sum(msg.get('size', 0) for msg in store) 
                    for store in self.memory_store.values()
                )
            }
        }
        
        # Add file stats if applicable
        if self.config.strategy in [PersistenceStrategy.FILE, PersistenceStrategy.HYBRID]:
            file_stats = await self._get_file_stats()
            stats["file"] = file_stats
        
        return stats
    
    async def _get_file_stats(self) -> Dict[str, Any]:
        """Get file storage statistics"""
        try:
            if not os.path.exists(self.config.file_path):
                return {"total_size_bytes": 0, "file_count": 0}
            
            total_size = 0
            file_count = 0
            
            for filename in os.listdir(self.config.file_path):
                if filename.endswith('.jsonl'):
                    file_path = os.path.join(self.config.file_path, filename)
                    total_size += os.path.getsize(file_path)
                    file_count += 1
            
            return {
                "total_size_bytes": total_size,
                "file_count": file_count,
                "path": self.config.file_path
            }
            
        except Exception as e:
            logger.error(f"Error getting file stats: {e}")
            return {"error": str(e)}

class IntegratedQoSPersistenceManager:
    """
    Integrated manager combining QoS and persistence functionality.
    """
    
    def __init__(self, qos_config: QoSConfiguration = None, persistence_config: PersistenceConfiguration = None):
        self.qos_manager = QoSManager()
        self.persistence_manager = MessagePersistenceManager(persistence_config)
        
        # Background tasks
        self.cleanup_task = None
        self.sync_task = None
        self.running = False
    
    async def start(self):
        """Start background management tasks"""
        if self.running:
            return
        
        self.running = True
        
        # Start cleanup task
        self.cleanup_task = asyncio.create_task(self._cleanup_loop())
        
        # Start sync task if file persistence is enabled
        if self.persistence_manager.config.strategy in [PersistenceStrategy.FILE, PersistenceStrategy.HYBRID]:
            self.sync_task = asyncio.create_task(self._sync_loop())
        
        logger.info("Started integrated QoS and persistence management")
    
    async def stop(self):
        """Stop background management tasks"""
        self.running = False
        
        if self.cleanup_task:
            self.cleanup_task.cancel()
        if self.sync_task:
            self.sync_task.cancel()
        
        logger.info("Stopped integrated QoS and persistence management")
    
    async def _cleanup_loop(self):
        """Background cleanup task"""
        while self.running:
            try:
                await self.qos_manager.cleanup_expired_messages()
                await self.persistence_manager.cleanup_old_messages()
                await asyncio.sleep(60)  # Run every minute
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")
                await asyncio.sleep(60)
    
    async def _sync_loop(self):
        """Background sync task for file persistence"""
        while self.running:
            try:
                # File sync is handled automatically by the OS, but we could add explicit fsync here
                await asyncio.sleep(self.persistence_manager.config.sync_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in sync loop: {e}")
                await asyncio.sleep(self.persistence_manager.config.sync_interval)
    
    async def process_message(self, topic: str, message: StandardZenohMessage) -> bool:
        """Process message through QoS and persistence layers"""
        try:
            # Apply QoS processing
            if not await self.qos_manager.enqueue_message(topic, message):
                return False
            
            # Store for persistence
            await self.persistence_manager.store_message(topic, message)
            
            return True
            
        except Exception as e:
            logger.error(f"Error processing message for topic {topic}: {e}")
            return False
    
    async def get_comprehensive_stats(self) -> Dict[str, Any]:
        """Get comprehensive statistics"""
        qos_stats = {
            "queues": {
                topic: {
                    priority.value: len(queue)
                    for priority, queue in queues.items()
                }
                for topic, queues in self.qos_manager.message_queues.items()
            },
            "delivery_stats": dict(self.qos_manager.delivery_stats)
        }
        
        persistence_stats = await self.persistence_manager.get_storage_stats()
        
        return {
            "qos": qos_stats,
            "persistence": persistence_stats,
            "running": self.running
        }