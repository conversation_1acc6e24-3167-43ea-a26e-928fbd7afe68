#!/usr/bin/env python3
"""
Zenoh Router for EngineAI Legged Gym
Simple router implementation for local development and testing
"""

import asyncio
import logging
import signal
import sys
from pathlib import Path

import zenoh

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ZenohRouter:
    """Simple Zenoh router for local development"""
    
    def __init__(self, listen_port: int = 7447):
        self.listen_port = listen_port
        self.session = None
        self.running = False
        
    async def start(self):
        """Start the Zenoh router"""
        try:
            logger.info(f"Starting Zenoh router on port {self.listen_port}")

            # Create Zenoh configuration
            config = zenoh.Config()

            # Try to configure as router if possible
            try:
                config.insert_json5("mode", '"router"')
                config.insert_json5("listen/endpoints", f'["tcp/0.0.0.0:{self.listen_port}"]')
                logger.info(f"   - Configured as router on port {self.listen_port}")
            except Exception as e:
                logger.warning(f"Could not configure as router, using default: {e}")

            # Open session
            self.session = zenoh.open(config)
            self.running = True

            logger.info("✅ Zenoh router started successfully")
            logger.info(f"   - Session opened")
            logger.info(f"   - Ready to route messages")

            # Keep the router running
            while self.running:
                await asyncio.sleep(1)

        except Exception as e:
            logger.error(f"❌ Failed to start Zenoh router: {e}")
            raise
            
    async def stop(self):
        """Stop the Zenoh router"""
        logger.info("Stopping Zenoh router...")
        self.running = False
        
        if self.session:
            try:
                self.session.close()
                logger.info("✅ Zenoh router stopped")
            except Exception as e:
                logger.error(f"Error stopping Zenoh session: {e}")

def signal_handler(router):
    """Handle shutdown signals"""
    def handler(signum, frame):
        logger.info(f"Received signal {signum}, shutting down...")
        asyncio.create_task(router.stop())
    return handler

async def main():
    """Main entry point"""
    router = ZenohRouter()
    
    # Setup signal handlers
    signal.signal(signal.SIGINT, signal_handler(router))
    signal.signal(signal.SIGTERM, signal_handler(router))
    
    try:
        await router.start()
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Router error: {e}")
        return 1
    finally:
        await router.stop()
    
    return 0

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
