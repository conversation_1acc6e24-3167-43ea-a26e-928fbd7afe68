# Error Handling System Configuration
# Configuration file for Task 14 Error Handling and Recovery System

# Error Handler Configuration
error_handler:
  # Enable/disable automatic recovery
  enable_recovery: true
  
  # Log file path for error handler
  log_file: "logs/error_handler.log"
  
  # Maximum number of errors to keep in history
  max_error_history: 1000
  
  # Recovery retry limits
  default_max_retry_attempts: 3
  default_retry_delay: 1.0  # seconds
  default_timeout: 30.0  # seconds

# Fault Detection System Configuration
fault_detection:
  # Global health checks configuration
  global_health_checks:
    system_cpu:
      interval: 30.0
      threshold: 90.0  # percentage
      failure_threshold: 3
      
    system_memory:
      interval: 30.0
      threshold: 95.0  # percentage
      failure_threshold: 2
      
    system_disk:
      interval: 60.0
      threshold: 95.0  # percentage
      failure_threshold: 2
  
  # System monitoring interval
  system_monitoring_interval: 30.0  # seconds
  
  # Service monitoring interval
  service_monitoring_interval: 5.0  # seconds

# Recovery Strategies Configuration
recovery_strategies:
  # Network error recovery
  network_errors:
    error_pattern: ".*connection.*failed.*|.*timeout.*|.*network.*unreachable.*"
    category: "network"
    severity: "high"
    action: "reconnect"
    max_retry_attempts: 5
    retry_delay: 2.0
    timeout: 30.0
  
  # Zenoh session errors
  zenoh_errors:
    error_pattern: ".*zenoh.*session.*|.*router.*unavailable.*"
    category: "zenoh"
    severity: "high"
    action: "restart_service"
    max_retry_attempts: 3
    retry_delay: 5.0
    timeout: 60.0
  
  # Resource exhaustion
  resource_errors:
    error_pattern: ".*out of memory.*|.*disk.*full.*|.*resource.*unavailable.*"
    category: "resource"
    severity: "critical"
    action: "manual_intervention"
    requires_manual_approval: true
  
  # Training errors
  training_errors:
    error_pattern: ".*cuda.*error.*|.*gpu.*|.*training.*failed.*"
    category: "training"
    severity: "high"
    action: "restart_service"
    max_retry_attempts: 2
    retry_delay: 10.0
  
  # Service crashes
  service_crashes:
    error_pattern: ".*service.*crash.*|.*unexpected.*exit.*"
    category: "service"
    severity: "critical"
    action: "restart_service"
    max_retry_attempts: 3
    retry_delay: 5.0

# Notification System Configuration
notifications:
  # Enable/disable notifications
  enabled: true
  
  # Auto-dismiss configuration
  enable_auto_dismiss: true
  default_dismiss_timeout: 30.0  # seconds
  
  # Maximum number of notifications to keep in history
  max_notification_history: 1000
  
  # Notification priorities
  priority_thresholds:
    low: ["low"]
    medium: ["medium"]
    high: ["high"]
    urgent: ["critical"]
  
  # UI notification settings
  ui_notifications:
    show_low_severity: false
    show_medium_severity: true
    show_high_severity: true
    show_critical_severity: true
    
    # Notification durations (0 = no auto-dismiss)
    duration_low: 5
    duration_medium: 10
    duration_high: 15
    duration_critical: 0

# Service Health Monitoring Configuration
service_health:
  # Default health check intervals
  default_health_check_interval: 30.0  # seconds
  default_health_check_timeout: 10.0   # seconds
  default_failure_threshold: 3
  default_success_threshold: 2
  
  # Health status thresholds
  degraded_error_threshold: 1
  unhealthy_error_threshold: 3
  critical_error_threshold: 5
  
  # Recovery action delays
  recovery_action_delay: 5.0  # seconds
  max_recovery_attempts: 3

# Fault Simulation Configuration (for testing)
fault_simulation:
  # Enable/disable fault simulation
  enabled: false
  
  # Default simulation parameters
  default_duration: 60.0  # seconds
  default_intensity: 1.0  # 0.0 to 1.0
  
  # Simulation modes
  simulation_modes:
    - immediate
    - gradual
    - intermittent
    - cascading
  
  # Fault types
  fault_types:
    - network_failure
    - memory_leak
    - cpu_spike
    - disk_full
    - service_crash
    - database_connection
    - zenoh_session_loss
    - timeout
    - deadlock

# Logging Configuration
logging:
  # Log levels
  error_handler_level: "INFO"
  fault_detection_level: "INFO"
  notification_system_level: "INFO"
  
  # Log file paths
  error_handler_log: "logs/error_handler.log"
  fault_detection_log: "logs/fault_detection.log"
  notification_system_log: "logs/notifications.log"
  
  # Log rotation
  max_log_size: "10MB"
  backup_count: 5
  
  # Log format
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  date_format: "%Y-%m-%d %H:%M:%S"

# Web GUI Integration
web_gui:
  # Error monitoring refresh intervals
  error_refresh_interval: 5000   # milliseconds
  health_refresh_interval: 10000 # milliseconds
  stats_refresh_interval: 30000  # milliseconds
  
  # Table pagination
  error_table_page_size: 20
  max_error_display: 1000
  
  # Chart configuration
  error_timeline_max_points: 100
  error_stats_chart_refresh: 30000  # milliseconds
  
  # Real-time updates
  enable_realtime_updates: true
  websocket_reconnect_interval: 5000  # milliseconds

# Performance Configuration
performance:
  # Error processing
  error_processing_batch_size: 100
  error_processing_interval: 1000  # milliseconds
  
  # Memory management
  enable_memory_cleanup: true
  memory_cleanup_interval: 300000  # milliseconds (5 minutes)
  
  # Thread pool configuration
  max_worker_threads: 10
  thread_timeout: 60.0  # seconds

# Security Configuration
security:
  # Authentication (if required)
  require_authentication: false
  
  # Access control
  admin_actions_require_confirmation: true
  
  # Data sanitization
  sanitize_error_messages: true
  max_message_length: 1000
  
  # Rate limiting
  enable_rate_limiting: true
  max_errors_per_minute: 100

# Integration Configuration
integration:
  # External monitoring systems
  external_monitoring:
    enabled: false
    # prometheus:
    #   endpoint: "http://localhost:9090"
    #   push_gateway: "http://localhost:9091"
    # grafana:
    #   endpoint: "http://localhost:3000"
  
  # Alerting systems
  external_alerting:
    enabled: false
    # slack:
    #   webhook_url: "https://hooks.slack.com/..."
    #   channel: "#alerts"
    # email:
    #   smtp_server: "smtp.gmail.com"
    #   smtp_port: 587
    #   username: "<EMAIL>"
    #   password: "password"