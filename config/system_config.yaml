# EngineAI Legged Gym System Configuration

# System Information
system:
  name: "EngineAI Legged Gym"
  version: "1.0.0"
  environment: "production"  # development, staging, production
  description: "Isaac Gym environments for Legged Robots with Zenoh integration"

# Zenoh Configuration
zenoh:
  mode: "peer"  # peer, client, router
  listen:
    - "tcp/0.0.0.0:7447"
  connect:
    - "tcp/127.0.0.1:7447"
  config_path: "zenoh_services/config/"
  session_timeout: 30.0
  connect_timeout: 10.0
  enable_auto_reconnect: true
  reconnect_interval: 5.0
  max_reconnect_attempts: 10

# Training Configuration
training:
  algorithm: "ppo"
  device: "cuda"  # cuda, cpu
  num_envs: 4096
  max_iterations: 1500
  save_interval: 100
  log_interval: 10
  experiment_name: "default_experiment"
  run_name: "default_run"
  
  # Performance settings
  thread_pool_size: 4
  message_buffer_size: 1000
  
  # Checkpointing
  checkpoint_frequency: 100
  keep_checkpoints: 5

# Simulation Configuration
simulation:
  headless: false
  physics_engine: "physx"
  dt: 0.0083  # 120 Hz
  substeps: 1
  up_axis: "z"
  
  # Isaac Gym specific
  use_gpu_pipeline: true
  num_threads: 0  # 0 = auto
  
  # Environment settings
  episode_length_s: 20
  terrain_type: "trimesh"  # plane, heightfield, trimesh
  curriculum: true

# Web GUI Configuration
web_gui:
  host: "0.0.0.0"
  port: 3000
  api_base_url: "http://localhost:8000"
  enable_cors: true
  
  # WebSocket settings
  websocket_port: 8765
  max_connections: 100

# Monitoring Configuration
monitoring:
  enabled: true
  metrics_interval: 1.0  # seconds
  log_level: "info"  # debug, info, warning, error
  export_prometheus: false
  
  # Performance monitoring
  track_gpu_usage: true
  track_memory_usage: true
  track_network_usage: true
  
  # Alerting thresholds
  alert_thresholds:
    cpu_usage: 80
    memory_usage: 85
    gpu_utilization: 90
    disk_usage: 90

# Logging Configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  # File logging
  file_logging:
    enabled: true
    log_dir: "logs"
    max_file_size: "10MB"
    backup_count: 5
    
  # Console logging
  console_logging:
    enabled: true
    colored: true

# Security Configuration
security:
  enable_authentication: false
  api_key_required: false
  cors_origins:
    - "http://localhost:3000"
    - "http://127.0.0.1:3000"

# Paths Configuration
paths:
  log_dir: "logs"
  model_dir: "models"
  config_dir: "config"
  data_dir: "data"
  temp_dir: "/tmp/engineai_legged_gym"

# Resource Limits
resources:
  max_memory_gb: 32
  max_gpu_memory_gb: 12
  max_cpu_cores: 16
  max_disk_space_gb: 100

# Feature Flags
features:
  enable_web_gui: true
  enable_monitoring: true
  enable_distributed_training: true
  enable_model_export: true
  enable_sim2real: true
