# Development dependencies for EngineAI Legged Gym
-r requirements.txt

# Code formatting and linting
black>=22.0.0
isort>=5.10.0
flake8>=5.0.0
mypy>=0.991
pylint>=2.15.0

# Testing
pytest>=7.0.0
pytest-cov>=4.0.0
pytest-asyncio>=0.20.0
pytest-mock>=3.8.0
coverage>=6.5.0

# Documentation
sphinx>=5.0.0
sphinx-rtd-theme>=1.0.0
myst-parser>=0.18.0

# Development tools
pre-commit>=2.20.0
jupyter>=1.0.0
ipykernel>=6.15.0
ipywidgets>=7.6.0

# Profiling and debugging
line-profiler>=3.5.0
memory-profiler>=0.60.0
py-spy>=0.3.0

# Build tools
build>=0.8.0
twine>=4.0.0
wheel>=0.37.0

# Type checking
types-PyYAML>=6.0.0
types-requests>=2.28.0
