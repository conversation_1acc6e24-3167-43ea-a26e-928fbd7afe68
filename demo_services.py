#!/usr/bin/env python3
"""
EngineAI Legged Gym Service Manager
Manages all services for the legged robot training system
"""

import asyncio
import argparse
import logging
import signal
import sys
import subprocess
import time
from pathlib import Path
from typing import Dict, List, Optional

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ServiceManager:
    """Manages all EngineAI services"""
    
    def __init__(self):
        self.services: Dict[str, subprocess.Popen] = {}
        self.running = False
        
    def start_service(self, name: str, command: List[str], cwd: Optional[str] = None) -> bool:
        """Start a service"""
        try:
            logger.info(f"🚀 Starting {name}...")
            
            process = subprocess.Popen(
                command,
                cwd=cwd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Give the service a moment to start
            time.sleep(2)
            
            if process.poll() is None:
                self.services[name] = process
                logger.info(f"✅ {name} started successfully (PID: {process.pid})")
                return True
            else:
                stdout, stderr = process.communicate()
                logger.error(f"❌ {name} failed to start:")
                if stdout:
                    logger.error(f"stdout: {stdout}")
                if stderr:
                    logger.error(f"stderr: {stderr}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error starting {name}: {e}")
            return False
    
    def stop_service(self, name: str) -> bool:
        """Stop a service"""
        if name not in self.services:
            logger.warning(f"Service {name} not found")
            return False
            
        try:
            process = self.services[name]
            logger.info(f"🛑 Stopping {name}...")
            
            process.terminate()
            
            # Wait for graceful shutdown
            try:
                process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                logger.warning(f"Force killing {name}...")
                process.kill()
                process.wait()
            
            del self.services[name]
            logger.info(f"✅ {name} stopped")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error stopping {name}: {e}")
            return False
    
    def stop_all(self):
        """Stop all services"""
        logger.info("🛑 Stopping all services...")
        for name in list(self.services.keys()):
            self.stop_service(name)
    
    def status(self):
        """Show status of all services"""
        logger.info("📊 Service Status:")
        if not self.services:
            logger.info("   No services running")
            return
            
        for name, process in self.services.items():
            if process.poll() is None:
                logger.info(f"   ✅ {name}: Running (PID: {process.pid})")
            else:
                logger.info(f"   ❌ {name}: Stopped")
    
    def start_all(self):
        """Start all core services"""
        logger.info("🚀 Starting all EngineAI services...")

        # 1. Start Zenoh router
        if self.start_service(
            "zenoh_router",
            [sys.executable, "zenoh_services/zenoh_router.py"],
            cwd="."
        ):
            time.sleep(3)  # Wait for router to be ready

        # 2. Start training service (simplified)
        self.start_service(
            "training_service",
            [sys.executable, "start_training_service.py"],
            cwd="."
        )

        # 3. Start web backend
        self.start_service(
            "web_backend",
            [sys.executable, "start_web_backend.py"],
            cwd="."
        )

        logger.info("🎉 Core services startup completed!")
        logger.info("")
        logger.info("📋 Next steps:")
        logger.info("   1. Web backend: ws://localhost:8080")
        logger.info("   2. Start Web GUI: cd zenoh_services/web_gui && npm run dev")
        logger.info("   3. Access UI: http://localhost:5173")
        logger.info("")
        self.status()
    
    def verify_installation(self):
        """Verify system installation"""
        logger.info("🔍 Verifying installation...")
        
        # Check Python imports
        try:
            import torch
            logger.info(f"✅ PyTorch: {torch.__version__}")
            logger.info(f"   CUDA available: {torch.cuda.is_available()}")
            if torch.cuda.is_available():
                logger.info(f"   CUDA devices: {torch.cuda.device_count()}")
        except ImportError:
            logger.error("❌ PyTorch not installed")
            return False
        
        try:
            import zenoh
            logger.info("✅ Zenoh installed")
        except ImportError:
            logger.error("❌ Zenoh not installed")
            return False
        
        try:
            from isaacgym import gymapi
            logger.info("✅ Isaac Gym installed")
        except ImportError:
            logger.error("❌ Isaac Gym not installed")
            return False
        
        # Check Web GUI
        web_gui_path = Path("zenoh_services/web_gui")
        if web_gui_path.exists():
            logger.info("✅ Web GUI directory found")
            if (web_gui_path / "dist").exists():
                logger.info("✅ Web GUI built")
            else:
                logger.warning("⚠️  Web GUI not built, run: cd zenoh_services/web_gui && npm run build")
        else:
            logger.error("❌ Web GUI directory not found")
        
        logger.info("✅ Installation verification completed")
        return True

def signal_handler(manager):
    """Handle shutdown signals"""
    def handler(signum, frame):
        logger.info(f"Received signal {signum}, shutting down...")
        manager.stop_all()
        sys.exit(0)
    return handler

def main():
    parser = argparse.ArgumentParser(description="EngineAI Service Manager")
    parser.add_argument("--start-all", action="store_true", help="Start all services")
    parser.add_argument("--stop-all", action="store_true", help="Stop all services")
    parser.add_argument("--status", action="store_true", help="Show service status")
    parser.add_argument("--verify-only", action="store_true", help="Only verify installation")
    parser.add_argument("--start", nargs="+", help="Start specific services")
    parser.add_argument("--stop", nargs="+", help="Stop specific services")
    
    args = parser.parse_args()
    
    manager = ServiceManager()
    
    # Setup signal handlers
    signal.signal(signal.SIGINT, signal_handler(manager))
    signal.signal(signal.SIGTERM, signal_handler(manager))
    
    try:
        if args.verify_only:
            manager.verify_installation()
        elif args.start_all:
            manager.start_all()
            # Keep running until interrupted
            while True:
                time.sleep(1)
        elif args.stop_all:
            manager.stop_all()
        elif args.status:
            manager.status()
        elif args.start:
            for service in args.start:
                logger.info(f"Starting {service} not implemented yet")
        elif args.stop:
            for service in args.stop:
                manager.stop_service(service)
        else:
            parser.print_help()
            
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
        manager.stop_all()
    except Exception as e:
        logger.error(f"Error: {e}")
        manager.stop_all()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
