[flake8]
max-line-length = 100
max-complexity = 10
select = E,W,F,C
ignore = 
    # E203: whitespace before ':' (conflicts with black)
    E203,
    # E501: line too long (handled by black)
    E501,
    # W503: line break before binary operator (conflicts with black)
    W503,
    # F401: imported but unused (handled by isort)
    F401,
    # E402: module level import not at top of file
    E402

exclude = 
    .git,
    __pycache__,
    .venv,
    venv,
    build,
    dist,
    *.egg-info,
    .pytest_cache,
    .mypy_cache,
    node_modules,
    # Isaac Gym related
    isaacgym,
    # Generated files
    *_pb2.py,
    # Test files with specific patterns
    test_*.py,
    *_test.py

per-file-ignores =
    # Allow unused imports in __init__.py files
    __init__.py:F401,F403
    # Allow longer lines in configuration files
    *config*.py:E501
    # Allow complex imports in setup files
    setup.py:E402,F401
    # Allow star imports in test files
    test_*.py:F403,F405
    # Allow unused variables in test files (fixtures)
    test_*.py:F841

# Specific rules for different file types
filename = *.py

# Error codes to always report
enable-extensions = 
    # pycodestyle errors
    E,
    # pycodestyle warnings  
    W,
    # pyflakes
    F,
    # mccabe complexity
    C

# Additional checks
doctests = True
statistics = True
count = True
show-source = True
