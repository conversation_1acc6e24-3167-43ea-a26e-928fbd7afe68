# Pre-commit configuration for EngineAI Legged Gym
repos:
  # Basic file checks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
        exclude: '\.md$'
      - id: end-of-file-fixer
        exclude: '\.md$'
      - id: check-yaml
        args: ['--unsafe']
      - id: check-json
      - id: check-toml
      - id: check-merge-conflict
      - id: check-added-large-files
        args: ['--maxkb=1000']
      - id: debug-statements
      - id: check-docstring-first
      - id: check-executables-have-shebangs
      - id: check-shebang-scripts-are-executable

  # Python code formatting
  - repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
      - id: black
        language_version: python3
        args: ['--line-length=100']

  # Import sorting
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: ['--profile', 'black', '--line-length=100']

  # Linting
  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        additional_dependencies: [
          flake8-docstrings,
          flake8-bugbear,
          flake8-comprehensions,
          flake8-simplify,
        ]

  # Type checking (optional, can be slow)
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.3.0
    hooks:
      - id: mypy
        additional_dependencies: [types-PyYAML, types-requests]
        args: [--ignore-missing-imports]
        exclude: ^(tests/|docs/|build/|dist/)

  # Security checks
  - repo: https://github.com/PyCQA/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        args: ['-r', '.', '-f', 'json', '-o', 'bandit-report.json']
        exclude: ^tests/

  # Documentation checks
  - repo: https://github.com/pycqa/pydocstyle
    rev: 6.3.0
    hooks:
      - id: pydocstyle
        args: ['--convention=google']
        exclude: ^(tests/|docs/|build/|dist/|setup.py)

  # YAML formatting
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.0.0-alpha.9-for-vscode
    hooks:
      - id: prettier
        types: [yaml]
        exclude: '^\.github/'

# Configuration
default_stages: [commit]
fail_fast: false

# Exclude patterns
exclude: |
  (?x)^(
    \.git/.*|
    \.venv/.*|
    venv/.*|
    build/.*|
    dist/.*|
    \.pytest_cache/.*|
    \.mypy_cache/.*|
    __pycache__/.*|
    .*\.egg-info/.*|
    node_modules/.*|
    isaacgym/.*|
    .*_pb2\.py$|
    .*\.log$
  )$
