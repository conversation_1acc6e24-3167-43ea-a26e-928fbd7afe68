# EngineAI Legged Gym Docker Image
FROM nvidia/cuda:11.8-devel-ubuntu20.04

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1
ENV CUDA_VISIBLE_DEVICES=0

# Install system dependencies
RUN apt-get update && apt-get install -y \
    python3.9 \
    python3.9-pip \
    python3.9-dev \
    python3.9-venv \
    git \
    wget \
    curl \
    build-essential \
    cmake \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*

# Install Node.js for web interface
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \
    && apt-get install -y nodejs

# Create app directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt requirements-dev.txt ./

# Create virtual environment and install Python dependencies
RUN python3.9 -m venv venv \
    && . venv/bin/activate \
    && pip install --upgrade pip \
    && pip install -r requirements.txt

# Copy application code
COPY . .

# Install the application
RUN . venv/bin/activate && pip install -e .

# Build web interface
RUN cd zenoh_services/web_gui \
    && npm install \
    && npm run build

# Create necessary directories
RUN mkdir -p logs data config

# Set permissions
RUN chmod +x start_system.py \
    && chmod +x scripts/*.py

# Expose ports
EXPOSE 3000 8080 7447

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3000/ || exit 1

# Default command
CMD ["./venv/bin/python", "start_system.py", "--web-port", "3000"]
