# Include documentation
include README.md
include LICENSE
include CHANGELOG.md
recursive-include docs *.md *.rst *.txt

# Include configuration files
include requirements.txt
include requirements-dev.txt
include .env.template
recursive-include config *.yaml *.yml *.json

# Include resource files
recursive-include resources *
recursive-include legged_gym/envs */config/*.yaml
recursive-include legged_gym/envs */urdf/*
recursive-include legged_gym/envs */meshes/*

# Include Zenoh service configurations
recursive-include zenoh_services/config *.yaml *.yml *.json

# Include Web GUI build files (if built)
recursive-include zenoh_services/web_gui/dist *
recursive-include zenoh_services/web_gui/public *

# Include test files
recursive-include tests *.py
recursive-include zenoh_services/tests *.py
recursive-include legged_gym/tests *.py

# Include scripts
recursive-include scripts *.py *.sh

# Exclude development and build files
global-exclude *.pyc
global-exclude *.pyo
global-exclude *.pyd
global-exclude __pycache__
global-exclude .git*
global-exclude .DS_Store
global-exclude *.so
global-exclude .pytest_cache
global-exclude .coverage
global-exclude .mypy_cache
global-exclude node_modules
global-exclude *.log

# Exclude temporary files
global-exclude *~
global-exclude *.tmp
global-exclude *.bak
