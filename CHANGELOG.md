# Changelog

All notable changes to the EngineAI Legged Gym project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.0] - 2024-07-24

### Added
- Complete project restructuring and organization
- Comprehensive requirements.txt with all dependencies
- System configuration files (config/system_config.yaml)
- Environment variable template (.env.template)
- Enhanced setup.py with proper metadata and entry points
- MANIFEST.in for proper package distribution
- Comprehensive README.md with installation and usage guides
- Development requirements (requirements-dev.txt)
- Project documentation structure

### Enhanced
- Web interface with React + TypeScript
- Zenoh-based distributed service architecture
- Real-time monitoring and metrics collection
- Model export capabilities (PyTorch and ONNX)
- Training service with enhanced PPO runner
- Simulation service with Isaac Gym integration
- Deployment service for model management
- Configuration service for centralized settings

### Fixed
- Dependency management and version conflicts
- Import path issues across modules
- Configuration file loading and validation
- Service initialization and error handling

### Documentation
- Complete API reference documentation
- Installation and setup guides
- Usage examples and tutorials
- Troubleshooting guides
- Development guidelines

## [0.9.0] - 2024-09-10

### Added
- Basic RL training functionality
- Isaac Gym environment integration
- PPO algorithm implementation
- Model export to ONNX format
- Sim2real deployment pipeline
- ZQSA01 robot configuration

### Features
- Training script with command-line interface
- Policy evaluation and testing
- Terrain generation and curriculum learning
- Domain randomization for robust policies

## [0.1.0] - Initial Release

### Added
- Basic project structure
- Isaac Gym integration
- Simple training pipeline
- Robot environment definitions

---

## Planned Features

### [1.1.0] - Upcoming
- [ ] Docker containerization support
- [ ] Kubernetes deployment manifests
- [ ] Enhanced monitoring dashboard
- [ ] Multi-robot training support
- [ ] Advanced terrain generation
- [ ] Improved documentation with video tutorials

### [1.2.0] - Future
- [ ] Cloud training support
- [ ] Distributed multi-node training
- [ ] Advanced visualization tools
- [ ] Mobile app for monitoring
- [ ] Integration with ROS2
- [ ] Support for additional robot platforms

---

## Migration Guide

### From 0.9.0 to 1.0.0

1. **Update Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Update Configuration**
   ```bash
   cp .env.template .env
   # Edit .env with your settings
   ```

3. **Migrate Training Scripts**
   - Old: `python train.py --task=zqsa01`
   - New: `python legged_gym/scripts/train.py --task=zqsa01`

4. **Update Service Usage**
   - Use `demo_services.py` for integrated service management
   - Web interface available at http://localhost:3000

### Breaking Changes

- Configuration file format changed from Python to YAML
- Service architecture moved to Zenoh-based messaging
- Web interface completely rewritten in React
- Training script location and parameters updated

---

## Contributors

- EngineAI Team - Core development and maintenance
- Community contributors - Bug reports and feature requests

---

## Support

For questions, issues, or contributions:
- GitHub Issues: https://github.com/engineai-robotics/engineai_legged_gym/issues
- Email: <EMAIL>
- Documentation: See docs/ directory
