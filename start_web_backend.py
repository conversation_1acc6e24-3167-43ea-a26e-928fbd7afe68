#!/usr/bin/env python3
"""
Zenoh-WebSocket Bridge for EngineAI Legged Gym
Provides WebSocket interface for the React frontend with real Zenoh integration
"""

import asyncio
import json
import logging
import websockets
import signal
import sys
import time
from datetime import datetime
from typing import Dict, Set, Optional

# Import Zenoh components
try:
    import zenoh
    from zenoh_services.core.enhanced_session_manager import EnhancedZenohConfig, EnhancedZenohSessionManager
    from zenoh_services.core.topic_manager import TopicManager
    from zenoh_services.core import topics
    from zenoh_services.core.data_models import TrainingCommand, TrainingConfig
    from zenoh_services.core.message_format import MessageFactory, MessageType
    ZENOH_AVAILABLE = True
except ImportError as e:
    logger.warning(f"Zenoh components not available: {e}")
    ZENOH_AVAILABLE = False

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ZenohWebSocketBridge:
    """Zenoh-WebSocket bridge for real-time communication with training services"""

    def __init__(self, host="localhost", port=8080):
        self.host = host
        self.port = port
        self.clients: Set[websockets.WebSocketServerProtocol] = set()
        self.running = False

        # Zenoh integration
        self.zenoh_session_manager: Optional[EnhancedZenohSessionManager] = None
        self.topic_manager: Optional[TopicManager] = None
        self.zenoh_initialized = False

        # Real-time data from Zenoh
        self.system_status = {
            "is_healthy": True,
            "active_services": ["zenoh_router", "training_service"],
            "system_load": {"cpu": 45.2, "memory": 62.1, "gpu": 78.5},
            "error_count": 0,
            "last_update": datetime.now().timestamp(),
            "uptime": 3600
        }

        self.training_status = {
            "state": "idle",
            "current_iteration": 0,
            "total_iterations": 1500,
            "progress_percentage": 0.0,
            "current_reward": 0.0,
            "best_reward": 0.0,
            "learning_rate": 0.0003,
            "experiment_name": "demo_experiment",
            "run_name": "demo_run",
            "elapsed_time": 0.0,
            "status_message": "Ready to start training"
        }

        self.training_metrics = {
            "iteration": 0,
            "mean_reward": 0.0,
            "std_reward": 0.0,
            "mean_episode_length": 0.0,
            "learning_rate": 0.0003,
            "entropy": 0.0,
            "kl_divergence": 0.0,
            "policy_loss": 0.0,
            "value_loss": 0.0,
            "timestamp": time.time(),
            "additional_metrics": {}
        }
    
    async def initialize_zenoh(self):
        """Initialize Zenoh session and subscriptions"""
        if not ZENOH_AVAILABLE:
            logger.warning("Zenoh not available, running in mock mode")
            return False

        try:
            # Create Zenoh session manager
            zenoh_config = EnhancedZenohConfig(
                router_endpoints=["tcp/127.0.0.1:7447"],
                enable_heartbeat=True,
                enable_metrics=True
            )

            self.zenoh_session_manager = EnhancedZenohSessionManager(zenoh_config, "web_bridge")
            await self.zenoh_session_manager.initialize()

            self.topic_manager = TopicManager(self.zenoh_session_manager)

            # Subscribe to training topics
            await self._setup_zenoh_subscriptions()

            self.zenoh_initialized = True
            logger.info("✅ Zenoh integration initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize Zenoh: {e}")
            return False

    async def _setup_zenoh_subscriptions(self):
        """Setup Zenoh topic subscriptions"""
        if not self.topic_manager:
            return

        # Subscribe to training status updates
        await self.topic_manager.create_subscriber(
            topics.TRAINING_STATUS,
            self._handle_training_status_update
        )

        # Subscribe to training metrics
        await self.topic_manager.create_subscriber(
            topics.TRAINING_METRICS,
            self._handle_training_metrics_update
        )

        # Subscribe to system status
        await self.topic_manager.create_subscriber(
            topics.SYSTEM_STATUS,
            self._handle_system_status_update
        )

        logger.info("Zenoh subscriptions setup complete")

    async def _handle_training_status_update(self, message):
        """Handle training status updates from Zenoh"""
        try:
            if hasattr(message, 'payload'):
                data = message.payload
            else:
                data = message

            # Update training status
            self.training_status.update(data)

            # Broadcast to all WebSocket clients
            await self.broadcast({
                "type": "training_status",
                "data": self.training_status
            })

        except Exception as e:
            logger.error(f"Error handling training status update: {e}")

    async def _handle_training_metrics_update(self, message):
        """Handle training metrics updates from Zenoh"""
        try:
            if hasattr(message, 'payload'):
                data = message.payload
            else:
                data = message

            # Update training metrics
            self.training_metrics.update(data)

            # Broadcast to all WebSocket clients
            await self.broadcast({
                "type": "training_metrics",
                "data": self.training_metrics
            })

        except Exception as e:
            logger.error(f"Error handling training metrics update: {e}")

    async def _handle_system_status_update(self, message):
        """Handle system status updates from Zenoh"""
        try:
            if hasattr(message, 'payload'):
                data = message.payload
            else:
                data = message

            # Update system status
            self.system_status.update(data)

            # Broadcast to all WebSocket clients
            await self.broadcast({
                "type": "system_status",
                "data": self.system_status
            })

        except Exception as e:
            logger.error(f"Error handling system status update: {e}")

    async def register_client(self, websocket):
        """Register a new client"""
        self.clients.add(websocket)
        logger.info(f"Client connected: {websocket.remote_address}")

        # Send initial data
        await self.send_to_client(websocket, {
            "type": "system_status",
            "data": self.system_status
        })

        await self.send_to_client(websocket, {
            "type": "training_status",
            "data": self.training_status
        })

        await self.send_to_client(websocket, {
            "type": "training_metrics",
            "data": self.training_metrics
        })
    
    async def unregister_client(self, websocket):
        """Unregister a client"""
        self.clients.discard(websocket)
        logger.info(f"Client disconnected: {websocket.remote_address}")
    
    async def send_to_client(self, websocket, message):
        """Send message to a specific client"""
        try:
            await websocket.send(json.dumps(message))
        except websockets.exceptions.ConnectionClosed:
            pass
    
    async def broadcast(self, message):
        """Broadcast message to all clients"""
        if self.clients:
            await asyncio.gather(
                *[self.send_to_client(client, message) for client in self.clients],
                return_exceptions=True
            )
    
    async def handle_client(self, websocket, path):
        """Handle client connection"""
        logger.info(f"New WebSocket connection from {websocket.remote_address} on path: {path}")
        await self.register_client(websocket)
        try:
            async for message in websocket:
                try:
                    data = json.loads(message)
                    await self.handle_message(websocket, data)
                except json.JSONDecodeError:
                    logger.error(f"Invalid JSON from client: {message}")
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"WebSocket connection closed: {websocket.remote_address}")
        except Exception as e:
            logger.error(f"WebSocket error: {e}")
        finally:
            await self.unregister_client(websocket)
    
    async def handle_message(self, websocket, data):
        """Handle incoming message from client"""
        message_type = data.get("type")

        if message_type == "training_command":
            await self._handle_training_command(data)
        elif message_type == "get_status":
            await self.send_to_client(websocket, {
                "type": "system_status",
                "data": self.system_status
            })
        elif message_type == "subscribe":
            # Handle subscription requests
            topic = data.get("topic")
            logger.info(f"Client subscribing to topic: {topic}")
        elif message_type == "unsubscribe":
            # Handle unsubscription requests
            topic = data.get("topic")
            logger.info(f"Client unsubscribing from topic: {topic}")

    async def _handle_training_command(self, data):
        """Handle training commands and forward to Zenoh"""
        command = data.get("command")
        parameters = data.get("parameters", {})

        logger.info(f"Received training command: {command} with parameters: {parameters}")

        if not self.zenoh_initialized:
            logger.warning("Zenoh not initialized, using mock behavior")
            await self._handle_mock_training_command(command, parameters)
            return

        try:
            # Create training command for Zenoh
            if command == "start":
                config_data = parameters.get("config", {})
                training_config = TrainingConfig(
                    task_name=config_data.get("task_name", "anymal_c_flat"),
                    num_envs=config_data.get("num_envs", 4096),
                    max_iterations=config_data.get("max_iterations", 1500),
                    learning_rate=config_data.get("learning_rate", 0.0003),
                    experiment_name=config_data.get("experiment_name", "web_training"),
                    run_name=config_data.get("run_name", f"run_{int(time.time())}"),
                    resume=config_data.get("resume", False),
                    checkpoint=config_data.get("checkpoint", 0)
                )

                training_command = TrainingCommand(
                    command="start",
                    config=training_config
                )
            else:
                training_command = TrainingCommand(
                    command=command,
                    config=None
                )

            # Send command via Zenoh
            message = MessageFactory.create_command(
                training_command.to_dict(),
                "web_bridge"
            )

            await self.topic_manager.publish_message(topics.TRAINING_COMMAND, message)
            logger.info(f"Training command '{command}' sent via Zenoh")

        except Exception as e:
            logger.error(f"Error sending training command via Zenoh: {e}")
            await self._handle_mock_training_command(command, parameters)

    async def _handle_mock_training_command(self, command, parameters):
        """Handle training commands in mock mode"""
        if command == "start":
            self.training_status["state"] = "training"
            self.training_status["status_message"] = "Training started (mock mode)"
        elif command == "stop":
            self.training_status["state"] = "idle"
            self.training_status["status_message"] = "Training stopped"
        elif command == "pause":
            self.training_status["state"] = "paused"
            self.training_status["status_message"] = "Training paused"
        elif command == "resume":
            self.training_status["state"] = "training"
            self.training_status["status_message"] = "Training resumed"

        await self.broadcast({
            "type": "training_status",
            "data": self.training_status
        })
    
    async def update_loop(self):
        """Periodic update loop for mock data when Zenoh is not available"""
        while self.running:
            # Update system status
            self.system_status["last_update"] = datetime.now().timestamp()
            self.system_status["uptime"] += 5

            # Only update mock training data if Zenoh is not available
            if not self.zenoh_initialized:
                if self.training_status["state"] == "training":
                    self.training_status["current_iteration"] += 1
                    self.training_status["current_reward"] += 0.1
                    if self.training_status["current_reward"] > self.training_status["best_reward"]:
                        self.training_status["best_reward"] = self.training_status["current_reward"]

                    # Update progress
                    progress = (self.training_status["current_iteration"] / self.training_status["total_iterations"]) * 100
                    self.training_status["progress_percentage"] = min(progress, 100.0)

                    # Update metrics
                    self.training_metrics["iteration"] = self.training_status["current_iteration"]
                    self.training_metrics["mean_reward"] = self.training_status["current_reward"]
                    self.training_metrics["timestamp"] = time.time()

                # Broadcast mock updates
                await self.broadcast({
                    "type": "training_status",
                    "data": self.training_status
                })

                if self.training_status["state"] == "training":
                    await self.broadcast({
                        "type": "training_metrics",
                        "data": self.training_metrics
                    })

            # Always broadcast system status
            await self.broadcast({
                "type": "system_status",
                "data": self.system_status
            })

            await asyncio.sleep(5)  # Update every 5 seconds
    
    async def start(self):
        """Start the Zenoh-WebSocket bridge"""
        self.running = True

        logger.info(f"🚀 Starting Zenoh-WebSocket bridge on ws://{self.host}:{self.port}")

        # Initialize Zenoh connection
        zenoh_success = await self.initialize_zenoh()
        if zenoh_success:
            logger.info("✅ Zenoh integration active - real-time data available")
        else:
            logger.warning("⚠️  Running in mock mode - Zenoh integration failed")

        # Start WebSocket server
        server = await websockets.serve(
            self.handle_client,
            self.host,
            self.port
        )

        # Start update loop
        update_task = asyncio.create_task(self.update_loop())

        logger.info("✅ Zenoh-WebSocket bridge started successfully")
        logger.info(f"   - WebSocket server: ws://{self.host}:{self.port}")
        logger.info(f"   - Zenoh integration: {'Active' if self.zenoh_initialized else 'Mock mode'}")
        logger.info(f"   - Ready for frontend connections")

        # Wait for server to close
        await server.wait_closed()
        update_task.cancel()
    
    async def stop(self):
        """Stop the Zenoh-WebSocket bridge"""
        self.running = False

        # Shutdown Zenoh session
        if self.zenoh_session_manager:
            try:
                await self.zenoh_session_manager.shutdown()
                logger.info("Zenoh session closed")
            except Exception as e:
                logger.error(f"Error closing Zenoh session: {e}")

        logger.info("Zenoh-WebSocket bridge stopped")

def signal_handler(bridge):
    """Handle shutdown signals"""
    def handler(signum, frame):
        logger.info(f"Received signal {signum}, shutting down...")
        asyncio.create_task(bridge.stop())
    return handler

async def main():
    """Main entry point"""
    bridge = ZenohWebSocketBridge()

    # Setup signal handlers
    signal.signal(signal.SIGINT, signal_handler(bridge))
    signal.signal(signal.SIGTERM, signal_handler(bridge))

    try:
        await bridge.start()
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Bridge error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    finally:
        await bridge.stop()

    return 0

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
