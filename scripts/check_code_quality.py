#!/usr/bin/env python3
"""
Code quality checker for EngineAI Legged Gym project.
Runs various code quality tools and reports results.
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path
from typing import List, Tuple, Dict


def run_command(cmd: List[str], description: str) -> Tuple[int, str, str]:
    """Run a command and return exit code, stdout, stderr."""
    print(f"🔍 {description}...")
    try:
        result = subprocess.run(
            cmd, 
            capture_output=True, 
            text=True, 
            cwd=Path(__file__).parent.parent
        )
        return result.returncode, result.stdout, result.stderr
    except FileNotFoundError:
        return 1, "", f"Command not found: {' '.join(cmd)}"


def check_syntax() -> bool:
    """Check Python syntax using py_compile."""
    print("🐍 Checking Python syntax...")
    
    python_files = []
    for root, dirs, files in os.walk("."):
        # Skip certain directories
        dirs[:] = [d for d in dirs if d not in ['.git', '__pycache__', '.venv', 'venv', 'node_modules', 'build', 'dist']]
        
        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))
    
    errors = []
    for file in python_files:
        try:
            with open(file, 'r', encoding='utf-8') as f:
                compile(f.read(), file, 'exec')
        except SyntaxError as e:
            errors.append(f"{file}: {e}")
        except Exception as e:
            errors.append(f"{file}: {e}")
    
    if errors:
        print("❌ Syntax errors found:")
        for error in errors:
            print(f"  {error}")
        return False
    else:
        print("✅ No syntax errors found")
        return True


def check_imports() -> bool:
    """Check for import issues."""
    print("📦 Checking imports...")
    
    # Try to import main modules
    modules_to_check = [
        'legged_gym',
        'zenoh_services.core',
        'rsl_rl',
    ]
    
    errors = []
    for module in modules_to_check:
        try:
            __import__(module)
            print(f"  ✅ {module}")
        except ImportError as e:
            errors.append(f"{module}: {e}")
            print(f"  ❌ {module}: {e}")
        except Exception as e:
            errors.append(f"{module}: {e}")
            print(f"  ⚠️  {module}: {e}")
    
    return len(errors) == 0


def run_black(fix: bool = False) -> bool:
    """Run black code formatter."""
    cmd = ['black', '--line-length=100']
    if not fix:
        cmd.append('--check')
    cmd.extend(['.'])
    
    returncode, stdout, stderr = run_command(cmd, "Running Black formatter")
    
    if returncode == 0:
        print("✅ Black formatting check passed")
        return True
    else:
        print("❌ Black formatting issues found")
        if stdout:
            print(stdout)
        if stderr:
            print(stderr)
        return False


def run_isort(fix: bool = False) -> bool:
    """Run isort import sorter."""
    cmd = ['isort', '--profile=black', '--line-length=100']
    if not fix:
        cmd.append('--check-only')
    cmd.extend(['.'])
    
    returncode, stdout, stderr = run_command(cmd, "Running isort")
    
    if returncode == 0:
        print("✅ Import sorting check passed")
        return True
    else:
        print("❌ Import sorting issues found")
        if stdout:
            print(stdout)
        if stderr:
            print(stderr)
        return False


def run_flake8() -> bool:
    """Run flake8 linter."""
    cmd = ['flake8', '.']
    
    returncode, stdout, stderr = run_command(cmd, "Running flake8 linter")
    
    if returncode == 0:
        print("✅ Flake8 linting passed")
        return True
    else:
        print("❌ Flake8 linting issues found")
        if stdout:
            print(stdout)
        if stderr:
            print(stderr)
        return False


def run_mypy() -> bool:
    """Run mypy type checker."""
    cmd = ['mypy', '--ignore-missing-imports', 'legged_gym', 'zenoh_services']
    
    returncode, stdout, stderr = run_command(cmd, "Running mypy type checker")
    
    if returncode == 0:
        print("✅ MyPy type checking passed")
        return True
    else:
        print("❌ MyPy type checking issues found")
        if stdout:
            print(stdout)
        if stderr:
            print(stderr)
        return False


def generate_report(results: Dict[str, bool]) -> None:
    """Generate a summary report."""
    print("\n" + "="*60)
    print("📊 CODE QUALITY REPORT")
    print("="*60)
    
    total_checks = len(results)
    passed_checks = sum(results.values())
    
    for check, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{check:<30} {status}")
    
    print("-"*60)
    print(f"Total: {passed_checks}/{total_checks} checks passed")
    
    if passed_checks == total_checks:
        print("🎉 All code quality checks passed!")
        return True
    else:
        print("⚠️  Some code quality issues found. Please fix them.")
        return False


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Check code quality")
    parser.add_argument('--fix', action='store_true', help='Fix issues automatically where possible')
    parser.add_argument('--skip-syntax', action='store_true', help='Skip syntax checking')
    parser.add_argument('--skip-imports', action='store_true', help='Skip import checking')
    parser.add_argument('--skip-format', action='store_true', help='Skip formatting checks')
    parser.add_argument('--skip-lint', action='store_true', help='Skip linting')
    parser.add_argument('--skip-types', action='store_true', help='Skip type checking')
    
    args = parser.parse_args()
    
    print("🚀 Starting code quality checks...")
    print(f"Working directory: {os.getcwd()}")
    
    results = {}
    
    # Run checks
    if not args.skip_syntax:
        results['Syntax Check'] = check_syntax()
    
    if not args.skip_imports:
        results['Import Check'] = check_imports()
    
    if not args.skip_format:
        results['Black Formatting'] = run_black(fix=args.fix)
        results['Import Sorting'] = run_isort(fix=args.fix)
    
    if not args.skip_lint:
        results['Flake8 Linting'] = run_flake8()
    
    if not args.skip_types:
        results['MyPy Type Checking'] = run_mypy()
    
    # Generate report
    all_passed = generate_report(results)
    
    # Exit with appropriate code
    sys.exit(0 if all_passed else 1)


if __name__ == "__main__":
    main()
