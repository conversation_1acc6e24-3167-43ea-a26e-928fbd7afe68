#!/usr/bin/env python3
"""
Integration test script for EngineAI Legged Gym.
Tests the integration between different system components.
"""

import os
import sys
import time
import asyncio
import subprocess
import requests
import websockets
import json
from pathlib import Path
from typing import Dict, Any, Optional


class IntegrationTester:
    """Integration test runner."""
    
    def __init__(self):
        self.web_port = 3001  # Use different port to avoid conflicts
        self.websocket_port = 8081
        self.processes = {}
        self.test_results = {}
    
    def start_service(self, name: str, command: list, wait_time: int = 3) -> bool:
        """Start a service for testing."""
        try:
            print(f"🚀 Starting {name}...")
            process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True
            )
            
            self.processes[name] = process
            time.sleep(wait_time)
            
            if process.poll() is None:
                print(f"✅ {name} started (PID: {process.pid})")
                return True
            else:
                print(f"❌ {name} failed to start")
                return False
                
        except Exception as e:
            print(f"❌ Error starting {name}: {e}")
            return False
    
    def stop_service(self, name: str):
        """Stop a service."""
        if name in self.processes:
            process = self.processes[name]
            if process.poll() is None:
                print(f"🛑 Stopping {name}...")
                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
            del self.processes[name]
    
    def stop_all_services(self):
        """Stop all services."""
        for name in list(self.processes.keys()):
            self.stop_service(name)
    
    def test_web_server(self) -> bool:
        """Test if web server is responding."""
        try:
            print("🧪 Testing web server...")
            response = requests.get(f"http://localhost:{self.web_port}", timeout=5)
            
            if response.status_code == 200:
                print("✅ Web server responding")
                return True
            else:
                print(f"❌ Web server returned status {response.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Web server test failed: {e}")
            return False
    
    async def test_websocket_connection(self) -> bool:
        """Test WebSocket connection."""
        try:
            print("🧪 Testing WebSocket connection...")
            
            uri = f"ws://localhost:{self.websocket_port}"
            async with websockets.connect(uri, timeout=5) as websocket:
                # Send a test message
                test_message = {
                    "type": "ping",
                    "data": {"timestamp": time.time()}
                }
                
                await websocket.send(json.dumps(test_message))
                
                # Wait for response
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5)
                    response_data = json.loads(response)
                    
                    print("✅ WebSocket connection successful")
                    return True
                    
                except asyncio.TimeoutError:
                    print("⚠️  WebSocket connected but no response received")
                    return True  # Connection works, just no response
                    
        except Exception as e:
            print(f"❌ WebSocket test failed: {e}")
            return False
    
    def test_zenoh_services(self) -> bool:
        """Test Zenoh services."""
        try:
            print("🧪 Testing Zenoh services...")
            
            # Try to import and create a simple Zenoh session
            import zenoh
            
            config = zenoh.Config()
            session = zenoh.open(config)
            
            # Test basic pub/sub
            test_topic = "test/integration"
            received_data = []
            
            def listener(sample):
                received_data.append(sample.payload.decode())
            
            # Subscribe
            sub = session.declare_subscriber(test_topic, listener)
            time.sleep(0.5)  # Give time for subscription to establish
            
            # Publish
            pub = session.declare_publisher(test_topic)
            test_message = "integration_test_message"
            pub.put(test_message)
            
            time.sleep(1)  # Wait for message
            
            # Cleanup
            sub.undeclare()
            pub.undeclare()
            session.close()
            
            if received_data and received_data[0] == test_message:
                print("✅ Zenoh pub/sub working")
                return True
            else:
                print("❌ Zenoh pub/sub failed")
                return False
                
        except Exception as e:
            print(f"❌ Zenoh test failed: {e}")
            return False
    
    def test_system_dependencies(self) -> bool:
        """Test system dependencies."""
        print("🧪 Testing system dependencies...")
        
        dependencies = {
            'zenoh': 'Zenoh messaging',
            'websockets': 'WebSocket support',
            'torch': 'PyTorch',
            'requests': 'HTTP requests'
        }
        
        all_good = True
        for module, description in dependencies.items():
            try:
                __import__(module)
                print(f"  ✅ {description}")
            except ImportError:
                print(f"  ❌ {description}")
                all_good = False
        
        return all_good
    
    def test_web_build(self) -> bool:
        """Test if web app is properly built."""
        print("🧪 Testing web build...")
        
        dist_path = Path("zenoh_services/web_gui/dist")
        required_files = ["index.html", "assets"]
        
        if not dist_path.exists():
            print("❌ Dist directory not found")
            return False
        
        for file in required_files:
            file_path = dist_path / file
            if not file_path.exists():
                print(f"❌ Required file/directory not found: {file}")
                return False
        
        print("✅ Web build is complete")
        return True
    
    async def run_full_integration_test(self) -> bool:
        """Run complete integration test."""
        print("🚀 Starting Full Integration Test")
        print("="*50)
        
        try:
            # Test 1: Dependencies
            self.test_results['dependencies'] = self.test_system_dependencies()
            
            # Test 2: Web build
            self.test_results['web_build'] = self.test_web_build()
            
            # Test 3: Zenoh services
            self.test_results['zenoh'] = self.test_zenoh_services()
            
            # Test 4: Start web server
            web_started = self.start_service(
                "Web Server",
                [sys.executable, "scripts/serve_web.py", "--port", str(self.web_port), "--no-browser"]
            )
            
            if web_started:
                self.test_results['web_server'] = self.test_web_server()
            else:
                self.test_results['web_server'] = False
            
            # Test 5: Start WebSocket backend
            backend_started = self.start_service(
                "WebSocket Backend",
                [sys.executable, "start_web_backend.py"]
            )
            
            if backend_started:
                self.test_results['websocket'] = await self.test_websocket_connection()
            else:
                self.test_results['websocket'] = False
            
            return self.generate_report()
            
        except Exception as e:
            print(f"❌ Integration test failed: {e}")
            return False
        finally:
            self.stop_all_services()
    
    def generate_report(self) -> bool:
        """Generate test report."""
        print("\n" + "="*60)
        print("📊 INTEGRATION TEST REPORT")
        print("="*60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(self.test_results.values())
        
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{test_name.replace('_', ' ').title():<30} {status}")
        
        print("-"*60)
        print(f"Total: {passed_tests}/{total_tests} tests passed")
        
        if passed_tests == total_tests:
            print("🎉 All integration tests passed!")
            print("✅ System is ready for deployment")
            return True
        else:
            print("⚠️  Some integration tests failed")
            print("❌ System needs attention before deployment")
            return False


async def main():
    """Main entry point."""
    tester = IntegrationTester()
    
    try:
        success = await tester.run_full_integration_test()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🔴 Integration test interrupted")
        tester.stop_all_services()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
