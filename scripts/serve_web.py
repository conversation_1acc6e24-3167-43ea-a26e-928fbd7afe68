#!/usr/bin/env python3
"""
Simple web server to serve the built React application.
"""

import os
import sys
import http.server
import socketserver
import webbrowser
import argparse
from pathlib import Path


class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """Custom handler to serve React app with proper routing."""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=str(Path(__file__).parent.parent / "zenoh_services" / "web_gui" / "dist"), **kwargs)
    
    def end_headers(self):
        """Add CORS headers for development."""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def do_GET(self):
        """Handle GET requests with React Router support."""
        # For React Router, serve index.html for all routes that don't exist as files
        if not os.path.exists(self.translate_path(self.path)):
            # Check if it's an API request or asset
            if not (self.path.startswith('/api/') or 
                   self.path.startswith('/assets/') or 
                   '.' in os.path.basename(self.path)):
                # Serve index.html for React Router
                self.path = '/index.html'
        
        return super().do_GET()


def check_build_exists():
    """Check if the web app has been built."""
    dist_path = Path(__file__).parent.parent / "zenoh_services" / "web_gui" / "dist"
    index_path = dist_path / "index.html"
    
    if not dist_path.exists() or not index_path.exists():
        print("❌ Web app not built. Please run:")
        print("   cd zenoh_services/web_gui && npm run build")
        return False
    
    print(f"✅ Found built web app at: {dist_path}")
    return True


def start_server(port=3000, open_browser=True):
    """Start the web server."""
    if not check_build_exists():
        return False
    
    try:
        with socketserver.TCPServer(("", port), CustomHTTPRequestHandler) as httpd:
            print(f"🚀 Starting web server on port {port}")
            print(f"📱 Web interface: http://localhost:{port}")
            print(f"🔗 WebSocket backend: ws://localhost:8080")
            print("Press Ctrl+C to stop the server")
            
            if open_browser:
                webbrowser.open(f"http://localhost:{port}")
            
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n🛑 Server stopped")
        return True
    except OSError as e:
        if e.errno == 98:  # Address already in use
            print(f"❌ Port {port} is already in use. Try a different port with --port")
        else:
            print(f"❌ Error starting server: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Serve the EngineAI Web Interface")
    parser.add_argument('--port', type=int, default=3000, help='Port to serve on (default: 3000)')
    parser.add_argument('--no-browser', action='store_true', help='Don\'t open browser automatically')
    parser.add_argument('--check-only', action='store_true', help='Only check if build exists')
    
    args = parser.parse_args()
    
    if args.check_only:
        if check_build_exists():
            print("✅ Web app is ready to serve")
            sys.exit(0)
        else:
            print("❌ Web app needs to be built")
            sys.exit(1)
    
    success = start_server(port=args.port, open_browser=not args.no_browser)
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
