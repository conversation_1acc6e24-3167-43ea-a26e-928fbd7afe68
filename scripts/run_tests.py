#!/usr/bin/env python3
"""
Test runner for EngineAI Legged Gym project.
Runs different categories of tests with proper reporting.
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path
from typing import List, Dict, Tuple


def run_command(cmd: List[str], description: str) -> Tuple[int, str, str]:
    """Run a command and return exit code, stdout, stderr."""
    print(f"🧪 {description}...")
    try:
        result = subprocess.run(
            cmd, 
            capture_output=True, 
            text=True, 
            cwd=Path(__file__).parent.parent
        )
        return result.returncode, result.stdout, result.stderr
    except FileNotFoundError:
        return 1, "", f"Command not found: {' '.join(cmd)}"


def run_unit_tests() -> bool:
    """Run unit tests."""
    cmd = [
        'python', '-m', 'pytest', 
        'zenoh_services/tests/test_data_models.py',
        'zenoh_services/tests/test_message_format.py',
        'zenoh_services/tests/test_serialization.py',
        'zenoh_services/tests/test_topics.py',
        '-v', '--tb=short'
    ]
    
    returncode, stdout, stderr = run_command(cmd, "Running unit tests")
    
    if returncode == 0:
        print("✅ Unit tests passed")
        return True
    else:
        print("❌ Unit tests failed")
        if stdout:
            print(stdout)
        if stderr:
            print(stderr)
        return False


def run_integration_tests() -> bool:
    """Run integration tests."""
    cmd = [
        'python', '-m', 'pytest', 
        'zenoh_services/tests/test_simple_integration.py',
        'zenoh_services/tests/test_service_integration.py',
        '-v', '--tb=short'
    ]
    
    returncode, stdout, stderr = run_command(cmd, "Running integration tests")
    
    if returncode == 0:
        print("✅ Integration tests passed")
        return True
    else:
        print("❌ Integration tests failed")
        if stdout:
            print(stdout)
        if stderr:
            print(stderr)
        return False


def run_service_tests() -> bool:
    """Run service-specific tests."""
    cmd = [
        'python', '-m', 'pytest', 
        'zenoh_services/tests/test_config_service.py',
        'zenoh_services/tests/test_session_manager.py',
        '-v', '--tb=short'
    ]
    
    returncode, stdout, stderr = run_command(cmd, "Running service tests")
    
    if returncode == 0:
        print("✅ Service tests passed")
        return True
    else:
        print("❌ Service tests failed")
        if stdout:
            print(stdout)
        if stderr:
            print(stderr)
        return False


def run_legged_gym_tests() -> bool:
    """Run legged gym environment tests."""
    # Check if Isaac Gym is available
    try:
        import isaacgym
        isaac_available = True
    except ImportError:
        isaac_available = False
        print("⚠️  Isaac Gym not available, skipping environment tests")
        return True
    
    if isaac_available:
        cmd = [
            'python', '-m', 'pytest', 
            'legged_gym/tests/',
            '-v', '--tb=short'
        ]
        
        returncode, stdout, stderr = run_command(cmd, "Running legged gym tests")
        
        if returncode == 0:
            print("✅ Legged gym tests passed")
            return True
        else:
            print("❌ Legged gym tests failed")
            if stdout:
                print(stdout)
            if stderr:
                print(stderr)
            return False
    
    return True


def run_performance_tests() -> bool:
    """Run performance tests."""
    cmd = [
        'python', '-m', 'pytest', 
        'zenoh_services/tests/test_performance_monitoring.py',
        '-v', '--tb=short', '-m', 'not slow'
    ]
    
    returncode, stdout, stderr = run_command(cmd, "Running performance tests")
    
    if returncode == 0:
        print("✅ Performance tests passed")
        return True
    else:
        print("❌ Performance tests failed")
        if stdout:
            print(stdout)
        if stderr:
            print(stderr)
        return False


def run_all_tests() -> bool:
    """Run all available tests."""
    cmd = [
        'python', '-m', 'pytest', 
        'zenoh_services/tests/',
        'legged_gym/tests/',
        '-v', '--tb=short',
        '--maxfail=5',  # Stop after 5 failures
        '-x'  # Stop on first failure
    ]
    
    returncode, stdout, stderr = run_command(cmd, "Running all tests")
    
    if returncode == 0:
        print("✅ All tests passed")
        return True
    else:
        print("❌ Some tests failed")
        if stdout:
            print(stdout)
        if stderr:
            print(stderr)
        return False


def run_coverage_tests() -> bool:
    """Run tests with coverage reporting."""
    cmd = [
        'python', '-m', 'pytest', 
        'zenoh_services/tests/',
        '--cov=zenoh_services',
        '--cov=legged_gym',
        '--cov-report=term-missing',
        '--cov-report=html:htmlcov',
        '-v'
    ]
    
    returncode, stdout, stderr = run_command(cmd, "Running tests with coverage")
    
    if returncode == 0:
        print("✅ Coverage tests completed")
        print("📊 Coverage report generated in htmlcov/")
        return True
    else:
        print("❌ Coverage tests failed")
        if stdout:
            print(stdout)
        if stderr:
            print(stderr)
        return False


def generate_report(results: Dict[str, bool]) -> bool:
    """Generate a summary report."""
    print("\n" + "="*60)
    print("📊 TEST RESULTS SUMMARY")
    print("="*60)
    
    total_suites = len(results)
    passed_suites = sum(results.values())
    
    for suite, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{suite:<30} {status}")
    
    print("-"*60)
    print(f"Total: {passed_suites}/{total_suites} test suites passed")
    
    if passed_suites == total_suites:
        print("🎉 All test suites passed!")
        return True
    else:
        print("⚠️  Some test suites failed. Please check the output above.")
        return False


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Run tests for EngineAI Legged Gym")
    parser.add_argument('--unit', action='store_true', help='Run unit tests only')
    parser.add_argument('--integration', action='store_true', help='Run integration tests only')
    parser.add_argument('--service', action='store_true', help='Run service tests only')
    parser.add_argument('--legged-gym', action='store_true', help='Run legged gym tests only')
    parser.add_argument('--performance', action='store_true', help='Run performance tests only')
    parser.add_argument('--coverage', action='store_true', help='Run tests with coverage')
    parser.add_argument('--all', action='store_true', help='Run all tests')
    parser.add_argument('--fast', action='store_true', help='Run fast tests only (skip slow ones)')
    
    args = parser.parse_args()
    
    print("🚀 Starting test execution...")
    print(f"Working directory: {os.getcwd()}")
    
    results = {}
    
    # Determine which tests to run
    if args.unit:
        results['Unit Tests'] = run_unit_tests()
    elif args.integration:
        results['Integration Tests'] = run_integration_tests()
    elif args.service:
        results['Service Tests'] = run_service_tests()
    elif args.legged_gym:
        results['Legged Gym Tests'] = run_legged_gym_tests()
    elif args.performance:
        results['Performance Tests'] = run_performance_tests()
    elif args.coverage:
        results['Coverage Tests'] = run_coverage_tests()
    elif args.all:
        results['All Tests'] = run_all_tests()
    else:
        # Default: run core tests
        results['Unit Tests'] = run_unit_tests()
        results['Integration Tests'] = run_integration_tests()
        results['Service Tests'] = run_service_tests()
        if not args.fast:
            results['Performance Tests'] = run_performance_tests()
    
    # Generate report
    all_passed = generate_report(results)
    
    # Exit with appropriate code
    sys.exit(0 if all_passed else 1)


if __name__ == "__main__":
    main()
