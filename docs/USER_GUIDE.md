# EngineAI 足式机器人训练平台 - 专业使用指南

## 📋 目录

1. [项目概述](#项目概述)
2. [系统要求](#系统要求)
3. [环境配置](#环境配置)
4. [安装指南](#安装指南)
5. [快速开始](#快速开始)
6. [核心功能详解](#核心功能详解)
7. [Web界面使用](#web界面使用)
8. [高级配置](#高级配置)
9. [故障排除](#故障排除)
10. [性能优化](#性能优化)
11. [开发指南](#开发指南)
12. [API参考](#api参考)

---

## 项目概述

EngineAI 足式机器人训练平台是一个基于强化学习的四足机器人训练系统，集成了Isaac Gym物理仿真、Zenoh通信中间件和现代化Web管理界面。该平台支持多种机器人模型（Anymal-C、ZQSA01等）在复杂地形环境中的训练和部署。

### 🎯 核心特性

- **高性能仿真**：基于Isaac Gym的GPU加速物理仿真
- **分布式架构**：Zenoh中间件支持的微服务架构
- **现代化界面**：React + TypeScript构建的Web管理平台
- **多机器人支持**：支持Anymal-C、ZQSA01等多种四足机器人
- **复杂环境训练**：崎岖地形、楼梯、斜坡等多样化训练场景
- **实时监控**：完整的训练过程监控和错误处理系统

---

## 系统要求

### 🖥️ 硬件要求

| 组件 | 最低要求 | 推荐配置 |
|------|----------|----------|
| **CPU** | Intel i5-8400 / AMD Ryzen 5 2600 | Intel i7-10700K / AMD Ryzen 7 3700X |
| **GPU** | NVIDIA GTX 1060 6GB | NVIDIA RTX 3080 / RTX 4080 |
| **内存** | 16GB DDR4 | 32GB DDR4 |
| **存储** | 50GB 可用空间 | 100GB SSD |
| **网络** | 千兆以太网 | 千兆以太网 |

### 🐧 软件要求

- **操作系统**：Ubuntu 20.04 LTS / Ubuntu 22.04 LTS
- **Python**：3.8 - 3.10
- **CUDA**：11.8 或更高版本
- **Docker**：20.10 或更高版本（可选）
- **Node.js**：18.x 或更高版本

---

## 环境配置

### 1. Conda环境设置

```bash
# 激活您的conda环境
conda activate eng

# 验证Python版本
python --version  # 应显示 Python 3.8-3.10

# 验证CUDA可用性
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}')"
```

### 2. 系统依赖安装

```bash
# 更新系统包
sudo apt update && sudo apt upgrade -y

# 安装系统依赖
sudo apt install -y \
    build-essential \
    cmake \
    git \
    curl \
    wget \
    unzip \
    libgl1-mesa-dev \
    libglu1-mesa-dev \
    libxrandr2 \
    libxinerama1 \
    libxcursor1 \
    libxi6 \
    libasound2-dev \
    libpulse-dev

# 安装Node.js (如果未安装)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

---

## 安装指南

### 1. 克隆项目

```bash
# 克隆项目到本地
git clone <repository-url> engineai_legged_gym
cd engineai_legged_gym

# 检查项目结构
ls -la
```

### 2. Python依赖安装

```bash
# 确保在eng环境中
conda activate eng

# 安装核心依赖
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# 安装Isaac Gym
cd thirdparty/isaacgym/python
pip install -e .
cd ../../../

# 安装项目依赖
pip install -e .

# 安装额外依赖
pip install -r requirements.txt
```

### 3. Zenoh服务安装

```bash
# 安装Zenoh Python绑定
pip install eclipse-zenoh

# 安装消息序列化依赖
pip install msgpack

# 验证Zenoh安装
python -c "import zenoh; print('Zenoh installed successfully')"
python -c "import msgpack; print('MessagePack installed successfully')"
```

### 4. Web界面依赖安装

```bash
# 进入Web GUI目录
cd zenoh_services/web_gui

# 安装Node.js依赖
npm install

# 构建项目
npm run build

# 返回项目根目录
cd ../../
```

---

## 快速开始

### 1. 环境验证

```bash
# 激活环境
conda activate eng

# 验证Isaac Gym安装
python -c "from isaacgym import gymapi; print('Isaac Gym ready')"

# 验证GPU可用性
python -c "import torch; print(f'GPU Count: {torch.cuda.device_count()}')"
```

### 2. 启动Zenoh路由器

```bash
# 在新终端中启动Zenoh路由器
conda activate eng
python zenoh_services/zenoh_router.py
```

### 3. 启动训练服务

```bash
# 在新终端中启动训练服务
conda activate eng
cd zenoh_services
python -m services training_service
```

### 4. 启动Web界面

```bash
# 在新终端中启动Web服务
conda activate eng
cd zenoh_services/web_gui
npm run dev
```

### 5. 访问Web界面

打开浏览器访问：`http://localhost:5173`

---

## 🚀 一键启动（推荐）

如果您想要快速启动所有服务，可以使用我们提供的服务管理脚本：

```bash
# 激活环境
conda activate eng

# 验证安装（可选）
python demo_services.py --verify-only

# 启动核心服务
python demo_services.py --start-all
```

这将自动启动：
- ✅ Zenoh路由器 (端口7447)
- ✅ 训练服务 (Zenoh客户端)
- ✅ Web后端 (WebSocket端口8080)

**启动Web界面**：
```bash
# 在新终端中启动前端
cd zenoh_services/web_gui
npm run dev
```

**访问系统**：
- 🌐 Web界面：http://localhost:5173
- 🔌 WebSocket API：ws://localhost:8080

**停止所有服务**：
```bash
# 在另一个终端中
python demo_services.py --stop-all

# 或者直接按 Ctrl+C 停止
```

**查看服务状态**：
```bash
python demo_services.py --status
```

---

## 核心功能详解

### 🤖 训练管理

#### 支持的机器人模型

| 模型 | 描述 | 适用场景 |
|------|------|----------|
| **Anymal-C** | 高精度四足机器人 | 复杂地形导航、精确控制 |
| **ZQSA01** | 轻量级四足机器人 | 快速移动、敏捷操作 |

#### 训练环境配置

```python
# 基础训练配置示例
training_config = {
    "task_name": "anymal_c_rough_terrain",
    "num_envs": 4096,
    "max_iterations": 1500,
    "learning_rate": 0.0003,
    "batch_size": 32,
    "experiment_name": "terrain_navigation_v1",
    "run_name": f"run_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
}
```

#### 启动训练

```bash
# 命令行启动训练
conda activate eng
python scripts/train.py \
    --task=anymal_c_rough_terrain \
    --num_envs=4096 \
    --max_iterations=1500 \
    --headless
```

### 🌍 仿真环境

#### 地形类型

- **平地 (Flat Ground)**：基础移动训练
- **崎岖地形 (Rough Terrain)**：复杂地形适应
- **楼梯 (Stairs)**：垂直移动能力
- **斜坡 (Slope)**：倾斜面行走
- **随机障碍物 (Random Obstacles)**：动态避障

#### 物理参数配置

```python
# 仿真物理参数
physics_config = {
    "gravity": -9.81,
    "time_step": 0.005,
    "substeps": 2,
    "friction": 1.0,
    "restitution": 0.0,
    "wind_speed": 0.0
}
```

### � 监控系统

#### 系统指标监控

- **CPU使用率**：实时CPU负载监控
- **GPU使用率**：GPU计算资源监控
- **内存使用**：系统内存占用情况
- **网络流量**：数据传输监控

#### 训练指标追踪

- **总奖励 (Total Reward)**：累积奖励值
- **回合长度 (Episode Length)**：单次训练回合时长
- **成功率 (Success Rate)**：任务完成率
- **平均奖励 (Average Reward)**：平均奖励值

---

## Web界面使用

### 🎛️ 仪表盘

访问 `http://localhost:5173` 后，您将看到主仪表盘，包含：

- **系统概览**：整体系统状态
- **训练会话**：当前活跃的训练任务
- **已部署模型**：可用的训练模型
- **活跃仿真**：正在运行的仿真实例

### 🏋️ 训练管理

#### 创建新训练任务

1. 点击左侧导航栏的"训练"
2. 在训练配置面板中设置参数：
   - **任务名称**：选择机器人和环境组合
   - **环境数量**：并行仿真环境数（推荐4096）
   - **最大迭代次数**：训练轮次（推荐1500）
   - **学习率**：优化器学习率（推荐0.0003）

3. 高级设置（可选）：
   - **实验名称**：便于管理的实验标识
   - **运行名称**：具体运行实例名称
   - **恢复训练**：从检查点继续训练
   - **检查点**：指定检查点编号

4. 点击"开始训练"启动任务

#### 训练过程监控

- **实时进度**：查看训练进度百分比
- **当前迭代**：显示当前训练轮次
- **最佳奖励**：追踪最高奖励值
- **状态控制**：暂停、恢复、停止训练

### 🎮 仿真控制

#### 环境配置

1. 切换到"仿真"标签页
2. 在环境配置面板中设置：
   - **地形类型**：选择训练地形
   - **地形尺寸**：设置长度和宽度
   - **物理参数**：重力、时间步长等

#### 机器人控制

- **紧急停止**：立即停止所有机器人运动
- **控制模式**：
  - 速度控制：通过速度命令控制
  - 位置控制：通过位置目标控制
  - 扭矩控制：通过扭矩命令控制

### 🚀 模型部署

#### 模型导出

1. 进入"部署"页面
2. 选择训练完成的模型
3. 配置导出参数：
   - **导出格式**：ONNX、TorchScript等
   - **目标平台**：CPU、GPU、边缘设备
   - **优化级别**：无、基础、激进

4. 点击"开始导出"

#### 性能测试

- **推理性能测试**：测试模型推理速度
- **精度验证**：验证模型准确性
- **模型对比**：比较不同模型性能

### 📈 系统监控

#### 错误监控

- **实时错误追踪**：监控系统错误
- **服务健康状态**：各服务运行状态
- **自动恢复**：错误自动处理机制

#### 日志管理

- **系统日志**：查看详细系统日志
- **训练日志**：追踪训练过程日志
- **错误日志**：错误信息详细记录

---

## 高级配置

### 🔧 训练参数优化

#### 环境数量调优

```python
# 根据GPU内存调整环境数量
gpu_memory_gb = torch.cuda.get_device_properties(0).total_memory / 1e9
recommended_envs = min(4096, int(gpu_memory_gb * 400))
```

#### 学习率调度

```python
# 自适应学习率配置
lr_schedule = {
    "initial_lr": 0.0003,
    "decay_factor": 0.95,
    "decay_steps": 100,
    "min_lr": 1e-6
}
```

### 🌐 分布式训练

#### 多GPU配置

```bash
# 多GPU训练启动
conda activate eng
python scripts/train.py \
    --task=anymal_c_rough_terrain \
    --num_envs=8192 \
    --multi_gpu \
    --gpu_ids=0,1,2,3
```

### 📝 自定义任务

#### 创建新任务

```python
# custom_task.py
from legged_gym.envs.base.legged_robot import LeggedRobot
from legged_gym.envs.base.legged_robot_config import LeggedRobotCfg

class CustomRobotCfg(LeggedRobotCfg):
    class env:
        num_envs = 4096
        num_observations = 235
        num_privileged_obs = None
        num_actions = 12
        env_spacing = 3.0
        send_timeouts = True
        episode_length_s = 20

    class terrain:
        mesh_type = 'trimesh'
        horizontal_scale = 0.1
        vertical_scale = 0.005
        border_size = 25
        curriculum = True
        static_friction = 1.0
        dynamic_friction = 1.0
        restitution = 0.0

class CustomRobot(LeggedRobot):
    cfg: CustomRobotCfg

    def __init__(self, cfg, sim_params, physics_engine, sim_device, headless):
        super().__init__(cfg, sim_params, physics_engine, sim_device, headless)

    def _reward_tracking_lin_vel(self):
        # 自定义奖励函数
        lin_vel_error = torch.sum(torch.square(
            self.commands[:, :2] - self.base_lin_vel[:, :2]), dim=1)
        return torch.exp(-lin_vel_error/self.cfg.rewards.tracking_sigma)
```

---

## 故障排除

### 🚨 常见问题

#### 1. Isaac Gym安装问题

**问题**：`ImportError: No module named 'isaacgym'`

**解决方案**：
```bash
conda activate eng
cd thirdparty/isaacgym/python
pip install -e .
```

#### 2. CUDA内存不足

**问题**：`RuntimeError: CUDA out of memory`

**解决方案**：
```bash
# 减少环境数量
python scripts/train.py --num_envs=2048  # 从4096减少到2048

# 或者清理GPU缓存
python -c "import torch; torch.cuda.empty_cache()"
```

#### 3. Zenoh连接失败

**问题**：服务无法连接到Zenoh路由器

**解决方案**：
```bash
# 检查Zenoh路由器状态
ps aux | grep zenoh

# 重启Zenoh路由器
python zenoh_services/zenoh_router.py
```

#### 4. Web界面无法访问

**问题**：浏览器无法打开 `http://localhost:5173`

**解决方案**：
```bash
# 检查端口占用
netstat -tulpn | grep 5173

# 重启Web服务
cd zenoh_services/web_gui
npm run dev
```

### 🔍 日志分析

#### 系统日志位置

```bash
# 训练日志
tail -f logs/training_service.log

# Zenoh路由器日志
tail -f logs/zenoh_router.log

# Web服务日志
tail -f logs/web_gui.log
```

#### 调试模式启动

```bash
# 启用详细日志
export RUST_LOG=debug
export PYTHONPATH=$PYTHONPATH:$(pwd)

# 调试模式启动训练
python scripts/train.py --task=anymal_c_flat --debug
```

---

## 性能优化

### ⚡ 训练性能优化

#### GPU优化

```python
# 优化GPU设置
import torch
torch.backends.cudnn.benchmark = True
torch.backends.cuda.matmul.allow_tf32 = True
torch.backends.cudnn.allow_tf32 = True
```

#### 内存优化

```python
# 梯度累积减少内存使用
training_config = {
    "gradient_accumulation_steps": 4,
    "max_grad_norm": 1.0,
    "mixed_precision": True
}
```

### 🚀 系统性能监控

#### 性能指标收集

```bash
# 安装性能监控工具
pip install nvidia-ml-py3 psutil

# 启动性能监控
python scripts/monitor_performance.py
```

#### 资源使用优化

```bash
# CPU亲和性设置
taskset -c 0-7 python scripts/train.py --task=anymal_c_rough_terrain

# 内存预分配
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
```

---

## 开发指南

### 🛠️ 开发环境设置

```bash
# 安装开发依赖
conda activate eng
pip install -r requirements-dev.txt

# 安装pre-commit钩子
pre-commit install

# 运行代码格式化
black .
isort .
flake8 .
```

### 🧪 测试

```bash
# 运行单元测试
python -m pytest tests/

# 运行集成测试
python -m pytest tests/integration/

# 生成测试覆盖率报告
python -m pytest --cov=legged_gym tests/
```

---

## API参考

### 🔌 Zenoh API

#### 训练服务API

```python
# 启动训练
zenoh_client.publish("training/start", {
    "task_name": "anymal_c_rough_terrain",
    "config": training_config
})

# 查询训练状态
status = zenoh_client.get("training/status")

# 停止训练
zenoh_client.publish("training/stop", {"job_id": "train_001"})
```

#### 仿真控制API

```python
# 启动仿真
zenoh_client.publish("simulation/start", {
    "environment": "rough_terrain",
    "robot_model": "anymal_c"
})

# 机器人控制
zenoh_client.publish("robot/control", {
    "mode": "velocity",
    "commands": [0.5, 0.0, 0.0]  # [forward, lateral, angular]
})
```

### 🌐 REST API

#### 训练管理

```bash
# 获取训练任务列表
curl -X GET http://localhost:8080/api/training/jobs

# 创建新训练任务
curl -X POST http://localhost:8080/api/training/jobs \
  -H "Content-Type: application/json" \
  -d '{"task_name": "anymal_c_rough_terrain", "num_envs": 4096}'

# 获取训练状态
curl -X GET http://localhost:8080/api/training/jobs/{job_id}/status
```

#### 模型管理

```bash
# 获取可用模型
curl -X GET http://localhost:8080/api/models

# 导出模型
curl -X POST http://localhost:8080/api/models/{model_id}/export \
  -H "Content-Type: application/json" \
  -d '{"format": "onnx", "platform": "gpu"}'
```

---

## 📞 支持与社区

### 🆘 获取帮助

- **文档**：查看在线文档获取详细信息
- **Issues**：在GitHub上提交问题和建议
- **讨论**：参与社区讨论和经验分享

### 🤝 贡献指南

1. Fork项目仓库
2. 创建功能分支：`git checkout -b feature/new-feature`
3. 提交更改：`git commit -am 'Add new feature'`
4. 推送分支：`git push origin feature/new-feature`
5. 创建Pull Request

### 📄 许可证

本项目采用MIT许可证，详见LICENSE文件。

---

## 📚 附录

### 🔗 有用链接

- [Isaac Gym文档](https://developer.nvidia.com/isaac-gym)
- [Zenoh官方文档](https://zenoh.io/docs/)
- [PyTorch文档](https://pytorch.org/docs/)
- [React文档](https://reactjs.org/docs/)

### 📋 检查清单

#### 安装验证清单

- [ ] Conda环境 `eng` 已激活
- [ ] Python 3.8-3.10 已安装
- [ ] CUDA 11.8+ 可用
- [ ] Isaac Gym 正常导入
- [ ] Zenoh 服务正常启动
- [ ] Web界面可访问
- [ ] 训练任务可正常启动

#### 性能优化清单

- [ ] GPU内存使用率 < 90%
- [ ] CPU使用率 < 80%
- [ ] 训练FPS > 1000
- [ ] 网络延迟 < 10ms
- [ ] 磁盘I/O正常

---

**🎉 恭喜！您已完成EngineAI足式机器人训练平台的配置。开始您的机器人训练之旅吧！**
