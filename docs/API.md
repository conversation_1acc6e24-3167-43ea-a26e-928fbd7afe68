# EngineAI Legged Gym - API文档

## 📋 目录

- [API概述](#api概述)
- [核心API](#核心api)
- [服务API](#服务api)
- [Web API](#web-api)
- [Python SDK](#python-sdk)
- [REST API](#rest-api)
- [WebSocket API](#websocket-api)

## 🔌 API概述

EngineAI Legged Gym 提供多层次的API接口：

### API架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Frontend  │    │   REST API      │    │   Python SDK    │
│   (React/TS)    │◄──►│   (FastAPI)     │◄──►│   (Core API)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Zenoh Message Bus                            │
├─────────────────────────────────────────────────────────────────┤
│  Training Service │ Simulation Service │ Deployment Service     │
└─────────────────────────────────────────────────────────────────┘
```

### API特性

- **异步支持**: 所有API都支持异步操作
- **实时通信**: 基于Zenoh的低延迟消息传递  
- **类型安全**: TypeScript和Python类型提示
- **错误处理**: 统一的错误处理和重试机制
- **版本控制**: API版本管理和向后兼容

## 🧠 核心API

### 环境API

#### 创建环境

```python
from legged_gym.envs.zqsa01.zqsa01 import ZQSA01
from legged_gym.envs.zqsa01.zqsa01_config import ZQSA01Config

# 创建配置
config = ZQSA01Config()
config.env.num_envs = 4096
config.sim.dt = 0.0083

# 创建环境
env = ZQSA01(cfg=config, render=True, eval_cfg=None)

# 环境信息
print(f"观测维度: {env.num_obs}")
print(f"动作维度: {env.num_actions}")
print(f"环境数量: {env.num_envs}")
```

#### 环境交互

```python
# 重置环境
observations = env.reset()
# 返回: torch.Tensor, shape=[num_envs, num_obs]

# 执行动作
actions = torch.randn(env.num_envs, env.num_actions)
observations, rewards, dones, infos = env.step(actions)

# 返回值:
# - observations: torch.Tensor, shape=[num_envs, num_obs]
# - rewards: torch.Tensor, shape=[num_envs]
# - dones: torch.Tensor, shape=[num_envs] (bool)
# - infos: dict, 包含额外信息
```

#### 环境配置

```python
# 设置目标速度
env.set_target_velocity(
    vx=1.0,    # 前进速度 (m/s)
    vy=0.0,    # 侧向速度 (m/s)  
    vyaw=0.0   # 转向速度 (rad/s)
)

# 获取机器人状态
robot_state = env.get_robot_state()
print(f"基座位置: {robot_state['base_pos']}")
print(f"基座姿态: {robot_state['base_quat']}")
print(f"关节位置: {robot_state['dof_pos']}")
print(f"关节速度: {robot_state['dof_vel']}")

# 应用外力
env.apply_external_force(
    force=[100, 0, 0],      # 力向量 (N)
    position=[0, 0, 1],     # 作用点
    env_ids=[0, 1, 2]       # 环境ID列表
)
```

### 训练API

#### 训练器

```python
from rsl_rl.runners.on_policy_runner import OnPolicyRunner
from rsl_rl.algorithms.ppo import PPO

# 创建训练器
train_cfg = config.runner
runner = OnPolicyRunner(env, train_cfg, log_dir=log_root, device=device)

# 开始训练
runner.learn(
    num_learning_iterations=1000,
    init_at_random_ep_len=True,
    eval_env=None
)

# 保存模型
runner.save(f"{log_root}/model_{iteration}.pt")

# 加载模型  
runner.load(f"{log_root}/model_1000.pt")
```

#### 自定义训练循环

```python
import torch
from collections import deque

class CustomTrainer:
    def __init__(self, env, algorithm, device):
        self.env = env
        self.alg = algorithm
        self.device = device
        self.episode_rewards = deque(maxlen=100)
        
    def train_step(self):
        """单步训练"""
        # 收集经验
        with torch.no_grad():
            obs = self.env.get_observations()
            actions = self.alg.act(obs)
            next_obs, rewards, dones, infos = self.env.step(actions)
            
        # 存储经验
        self.alg.storage.add_transitions(
            observations=obs,
            actions=actions,
            rewards=rewards,
            dones=dones,
            values=self.alg.storage.values,
            actions_log_prob=self.alg.storage.actions_log_prob,
            mu=self.alg.storage.mu,
            sigma=self.alg.storage.sigma
        )
        
        # 更新策略
        if self.alg.storage.step >= self.alg.storage.num_transitions_per_env:
            loss = self.alg.update()
            self.alg.storage.clear()
            return loss
            
        return None
        
    def evaluate(self, num_episodes=10):
        """评估模型"""
        episode_rewards = []
        
        for _ in range(num_episodes):
            obs = self.env.reset()
            episode_reward = 0
            done = False
            
            while not done:
                with torch.no_grad():
                    actions = self.alg.act_inference(obs)
                obs, rewards, dones, _ = self.env.step(actions)
                episode_reward += rewards.mean().item()
                done = dones.all()
                
            episode_rewards.append(episode_reward)
            
        return {
            'mean_reward': np.mean(episode_rewards),
            'std_reward': np.std(episode_rewards),
            'max_reward': np.max(episode_rewards),
            'min_reward': np.min(episode_rewards)
        }
```

### 算法API

#### PPO算法

```python
from rsl_rl.algorithms.ppo import PPO, PPOConfig

# PPO配置
ppo_config = PPOConfig()
ppo_config.algorithm.learning_rate = 0.0026
ppo_config.algorithm.num_learning_epochs = 5
ppo_config.algorithm.mini_batch_size = 4096
ppo_config.algorithm.gamma = 0.99
ppo_config.algorithm.lam = 0.95
ppo_config.algorithm.entropy_coef = 0.01

# 创建PPO实例
ppo = PPO(
    actor_critic=policy,
    num_transitions_per_env=24,
    num_learning_epochs=ppo_config.algorithm.num_learning_epochs,
    num_mini_batches=ppo_config.algorithm.num_mini_batches,
    clip_param=ppo_config.algorithm.clip_param,
    gamma=ppo_config.algorithm.gamma,
    lam=ppo_config.algorithm.lam,
    value_loss_coef=ppo_config.algorithm.value_loss_coef,
    entropy_coef=ppo_config.algorithm.entropy_coef,
    learning_rate=ppo_config.algorithm.learning_rate,
    max_grad_norm=ppo_config.algorithm.max_grad_norm,
    use_clipped_value_loss=ppo_config.algorithm.use_clipped_value_loss,
    schedule=ppo_config.algorithm.schedule,
    desired_kl=ppo_config.algorithm.desired_kl,
    device=device
)

# 执行动作
actions = ppo.act(observations, deterministic=False)

# 推理模式
actions = ppo.act_inference(observations)

# 更新策略
mean_value_loss, mean_surrogate_loss = ppo.update()
```

## 🔧 服务API

### 训练服务API

```python
from zenoh_services.services.training_service import TrainingService

class TrainingServiceAPI:
    def __init__(self):
        self.service = TrainingService()
        
    async def start_training(self, config: dict) -> str:
        """开始训练"""
        request = {
            'command': 'start_training',
            'config': config,
            'timestamp': time.time()
        }
        
        response = await self.service.handle_training_request(request)
        return response['training_id']
        
    async def stop_training(self, training_id: str) -> bool:
        """停止训练"""
        request = {
            'command': 'stop_training',
            'training_id': training_id,
            'timestamp': time.time()
        }
        
        response = await self.service.handle_training_request(request)
        return response['success']
        
    async def get_training_status(self, training_id: str) -> dict:
        """获取训练状态"""
        request = {
            'command': 'get_status',
            'training_id': training_id,
            'timestamp': time.time()
        }
        
        response = await self.service.handle_training_request(request)
        return {
            'status': response['status'],
            'iteration': response['iteration'],
            'reward': response['current_reward'],
            'loss': response['current_loss'],
            'progress': response['progress']
        }
        
    async def save_model(self, training_id: str, checkpoint_name: str) -> str:
        """保存模型"""
        request = {
            'command': 'save_model',
            'training_id': training_id,
            'checkpoint_name': checkpoint_name,
            'timestamp': time.time()
        }
        
        response = await self.service.handle_training_request(request)
        return response['model_path']
        
    async def load_model(self, model_path: str) -> bool:
        """加载模型"""
        request = {
            'command': 'load_model',
            'model_path': model_path,
            'timestamp': time.time()
        }
        
        response = await self.service.handle_training_request(request)
        return response['success']
```

### 仿真服务API

```python
from zenoh_services.services.simulation_service import SimulationService

class SimulationServiceAPI:
    def __init__(self):
        self.service = SimulationService()
        
    async def create_simulation(self, config: dict) -> str:
        """创建仿真环境"""
        request = {
            'command': 'create_simulation',
            'config': config,
            'timestamp': time.time()
        }
        
        response = await self.service.handle_simulation_request(request)
        return response['simulation_id']
        
    async def step_simulation(self, simulation_id: str, actions: list) -> dict:
        """执行仿真步骤"""
        request = {
            'command': 'step',
            'simulation_id': simulation_id,
            'actions': actions,
            'timestamp': time.time()
        }
        
        response = await self.service.handle_simulation_request(request)
        return {
            'observations': response['observations'],
            'rewards': response['rewards'],
            'dones': response['dones'],
            'info': response['info']
        }
        
    async def reset_simulation(self, simulation_id: str) -> dict:
        """重置仿真环境"""
        request = {
            'command': 'reset',
            'simulation_id': simulation_id,
            'timestamp': time.time()
        }
        
        response = await self.service.handle_simulation_request(request)
        return {
            'observations': response['observations'],
            'simulation_id': response['simulation_id']
        }
        
    async def get_robot_state(self, simulation_id: str) -> dict:
        """获取机器人状态"""
        request = {
            'command': 'get_robot_state',
            'simulation_id': simulation_id,
            'timestamp': time.time()
        }
        
        response = await self.service.handle_simulation_request(request)
        return response['robot_state']
        
    async def set_robot_pose(self, simulation_id: str, pose: dict) -> bool:
        """设置机器人姿态"""
        request = {
            'command': 'set_robot_pose',
            'simulation_id': simulation_id,
            'pose': pose,
            'timestamp': time.time()
        }
        
        response = await self.service.handle_simulation_request(request)
        return response['success']
```

### 部署服务API

```python
from zenoh_services.services.deployment_service import DeploymentService

class DeploymentServiceAPI:
    def __init__(self):
        self.service = DeploymentService()
        
    async def export_model(self, model_path: str, export_config: dict) -> str:
        """导出模型"""
        request = {
            'command': 'export_model',
            'model_path': model_path,
            'export_format': export_config.get('format', 'onnx'),
            'optimization_level': export_config.get('optimization', 'standard'),
            'target_platform': export_config.get('platform', 'generic'),
            'timestamp': time.time()
        }
        
        response = await self.service.handle_deployment_request(request)
        return response['exported_model_path']
        
    async def validate_model(self, model_path: str, validation_config: dict) -> dict:
        """验证模型"""
        request = {
            'command': 'validate_model',
            'model_path': model_path,
            'test_cases': validation_config.get('test_cases', []),
            'tolerance': validation_config.get('tolerance', 1e-3),
            'timestamp': time.time()
        }
        
        response = await self.service.handle_deployment_request(request)
        return {
            'validation_passed': response['passed'],
            'accuracy': response['accuracy'],
            'latency_ms': response['latency'],
            'memory_mb': response['memory_usage'],
            'test_results': response['test_results']
        }
        
    async def deploy_to_device(self, model_path: str, device_config: dict) -> str:
        """部署到设备"""
        request = {
            'command': 'deploy_to_device',
            'model_path': model_path,
            'device_type': device_config['type'],
            'device_address': device_config['address'],
            'credentials': device_config.get('credentials', {}),
            'timestamp': time.time()
        }
        
        response = await self.service.handle_deployment_request(request)
        return response['deployment_id']
```

## 🌐 Web API

### REST API 接口

#### 训练接口

```http
POST /api/v1/training/start
Content-Type: application/json

{
    "task": "zqsa01",
    "config": {
        "num_envs": 4096,
        "max_iterations": 1000,
        "learning_rate": 0.0026
    }
}

Response:
{
    "training_id": "train_20240115_143022",
    "status": "started",
    "estimated_duration": 7200
}
```

```http
GET /api/v1/training/{training_id}/status

Response:
{
    "training_id": "train_20240115_143022",
    "status": "running",
    "iteration": 450,
    "max_iterations": 1000,
    "current_reward": 125.8,
    "best_reward": 134.2,
    "loss": {
        "policy": 0.012,
        "value": 0.045
    },
    "progress": 0.45,
    "eta_seconds": 3960
}
```

```http
POST /api/v1/training/{training_id}/stop

Response:
{
    "success": true,
    "final_model_path": "logs/model_450.pt",
    "training_summary": {
        "total_iterations": 450,
        "final_reward": 125.8,
        "training_time": 3240
    }
}
```

#### 仿真接口

```http
POST /api/v1/simulation/create
Content-Type: application/json

{
    "task": "zqsa01",
    "render": true,
    "num_envs": 1,
    "config": {
        "dt": 0.0083,
        "physics_engine": "physx"
    }
}

Response:
{
    "simulation_id": "sim_20240115_143055",
    "status": "created",
    "num_observations": 235,
    "num_actions": 12
}
```

```http
POST /api/v1/simulation/{simulation_id}/step
Content-Type: application/json

{
    "actions": [0.1, 0.0, -0.1, 0.2, 0.0, -0.2, 0.1, 0.0, -0.1, 0.2, 0.0, -0.2]
}

Response:
{
    "observations": [...],
    "rewards": [1.25],
    "dones": [false],
    "info": {
        "episode_length": 120,
        "base_height": 1.02
    }
}
```

#### 部署接口

```http
POST /api/v1/deployment/export
Content-Type: application/json

{
    "model_path": "logs/model_1000.pt",
    "export_format": "onnx",
    "target_platform": "jetson",
    "optimization": "tensorrt"
}

Response:
{
    "export_id": "export_20240115_143120",
    "exported_model_path": "exports/zqsa01_model.onnx",
    "model_info": {
        "input_shape": [1, 235],
        "output_shape": [1, 12],
        "model_size_mb": 4.8,
        "parameters": 1204567
    }
}
```

#### 监控接口

```http
GET /api/v1/monitoring/system

Response:
{
    "timestamp": "2024-01-15T14:30:45Z",
    "cpu": {
        "usage_percent": 78.5,
        "temperature": 65.2
    },
    "gpu": {
        "usage_percent": 92.1,
        "memory_used_mb": 7890,
        "memory_total_mb": 8192,
        "temperature": 78.0
    },
    "memory": {
        "usage_percent": 64.3,
        "available_mb": 11520
    },
    "services": {
        "training_service": "running",
        "simulation_service": "running",
        "deployment_service": "running",
        "monitoring_service": "running"
    }
}
```

### WebSocket API

#### 实时训练监控

```javascript
const ws = new WebSocket('ws://localhost:8000/ws/training/train_20240115_143022');

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    
    switch(data.type) {
        case 'iteration_update':
            console.log(`Iteration ${data.iteration}: Reward = ${data.reward}`);
            break;
            
        case 'loss_update':
            console.log(`Loss: Policy = ${data.policy_loss}, Value = ${data.value_loss}`);
            break;
            
        case 'training_complete':
            console.log('Training completed!');
            console.log(`Final model: ${data.model_path}`);
            break;
            
        case 'error':
            console.error(`Training error: ${data.message}`);
            break;
    }
};

// 发送控制命令
ws.send(JSON.stringify({
    command: 'pause_training'
}));
```

#### 实时仿真数据

```javascript
const ws = new WebSocket('ws://localhost:8000/ws/simulation/sim_20240115_143055');

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    
    switch(data.type) {
        case 'robot_state':
            updateRobotVisualization(data.state);
            break;
            
        case 'sensor_data':
            updateSensorReadings(data.sensors);
            break;
            
        case 'collision':
            console.warn('Collision detected!');
            break;
    }
};

// 发送控制命令
ws.send(JSON.stringify({
    command: 'set_target_velocity',
    velocity: {vx: 1.0, vy: 0.0, vyaw: 0.0}
}));
```

## 📚 Python SDK

### 高级API封装

```python
from engineai_legged_gym import EngineAI

# 创建SDK实例
ai = EngineAI()

# 快速训练
training_session = ai.train(
    task='zqsa01',
    max_iterations=1000,
    config={
        'learning_rate': 0.003,
        'num_envs': 2048
    }
)

# 监控训练进度
for update in training_session.progress():
    print(f"Iteration {update.iteration}: Reward = {update.reward:.2f}")
    
    if update.reward > 150:
        print("Target reward reached!")
        training_session.stop()
        break

# 保存最佳模型
model_path = training_session.save_best_model()
print(f"Model saved to: {model_path}")
```

### 批量实验

```python
from engineai_legged_gym.experiments import ExperimentManager

# 创建实验管理器
manager = ExperimentManager()

# 定义参数网格
param_grid = {
    'learning_rate': [0.001, 0.003, 0.01],
    'entropy_coef': [0.01, 0.05, 0.1],
    'num_envs': [2048, 4096]
}

# 运行批量实验
results = manager.run_grid_search(
    task='zqsa01',
    param_grid=param_grid,
    max_iterations=500,
    num_seeds=3
)

# 分析结果
best_config = results.get_best_config()
print(f"Best configuration: {best_config}")
print(f"Best reward: {results.get_best_reward():.2f}")

# 生成报告
results.generate_report('experiment_results.html')
```

### 模型评估

```python
from engineai_legged_gym.evaluation import ModelEvaluator

# 创建评估器
evaluator = ModelEvaluator()

# 加载模型
model = evaluator.load_model('logs/model_1000.pt')

# 评估性能
results = evaluator.evaluate(
    model=model,
    task='zqsa01',
    num_episodes=100,
    render=True
)

print(f"Average reward: {results.mean_reward:.2f}")
print(f"Success rate: {results.success_rate:.1%}")
print(f"Average episode length: {results.mean_episode_length:.1f}")

# 生成评估报告
evaluator.generate_report(results, 'evaluation_report.pdf')
```

## 🔌 集成示例

### 自定义训练回调

```python
from engineai_legged_gym.callbacks import TrainingCallback

class CustomCallback(TrainingCallback):
    def __init__(self):
        self.best_reward = -float('inf')
        
    def on_iteration_end(self, iteration, metrics):
        """每次迭代结束时调用"""
        current_reward = metrics['mean_reward']
        
        if current_reward > self.best_reward:
            self.best_reward = current_reward
            print(f"New best reward: {current_reward:.2f}")
            
        # 自动保存检查点
        if iteration % 100 == 0:
            self.trainer.save_checkpoint(f'checkpoint_{iteration}.pt')
            
    def on_training_end(self, final_metrics):
        """训练结束时调用"""
        print(f"Training completed. Best reward: {self.best_reward:.2f}")
        
        # 自动导出ONNX模型
        self.trainer.export_onnx('final_model.onnx')

# 使用自定义回调
callback = CustomCallback()
ai.train('zqsa01', callbacks=[callback])
```

### 分布式训练

```python
from engineai_legged_gym.distributed import DistributedTraining

# 初始化分布式训练
dist_trainer = DistributedTraining(
    num_workers=4,
    backend='nccl'  # 或 'gloo' for CPU
)

# 配置分布式参数
config = {
    'task': 'zqsa01',
    'total_envs': 16384,  # 将被分散到4个workers
    'max_iterations': 1000,
    'sync_frequency': 10  # 每10次迭代同步一次
}

# 启动分布式训练
results = dist_trainer.run(config)
print(f"Distributed training completed: {results}")
```

### 云端训练

```python
from engineai_legged_gym.cloud import CloudTraining

# 配置云端训练
cloud = CloudTraining(provider='aws')  # 支持 aws, gcp, azure

# 提交训练任务
job = cloud.submit_training(
    task='zqsa01',
    instance_type='p3.2xlarge',
    max_duration_hours=24,
    config={
        'max_iterations': 2000,
        'num_envs': 8192
    }
)

# 监控任务状态
for status in job.monitor():
    print(f"Status: {status.state}, Progress: {status.progress:.1%}")
    
    if status.state == 'completed':
        # 下载训练结果
        job.download_results('./results/')
        break
```

## ❗ 错误处理

### 异常类型

```python
from engineai_legged_gym.exceptions import (
    EngineAIException,
    TrainingException,
    SimulationException,
    DeploymentException,
    ConfigurationException
)

try:
    ai.train('invalid_task')
except TrainingException as e:
    print(f"Training error: {e}")
    print(f"Error code: {e.error_code}")
    print(f"Suggestions: {e.suggestions}")
except ConfigurationException as e:
    print(f"Configuration error: {e}")
    print(f"Invalid parameters: {e.invalid_params}")
```

### 重试机制

```python
from engineai_legged_gym.utils import retry_on_failure

@retry_on_failure(max_attempts=3, delay=5.0)
async def robust_training():
    try:
        return await ai.train_async('zqsa01')
    except Exception as e:
        print(f"Training attempt failed: {e}")
        raise

# 使用重试机制
result = await robust_training()
```

## 📊 性能优化

### 批量操作

```python
# 批量创建环境
envs = ai.create_environments_batch(
    tasks=['zqsa01'] * 4,
    num_envs_per_task=1024
)

# 批量执行动作
actions_batch = [
    torch.randn(1024, 12) for _ in range(4)
]
results = ai.step_environments_batch(envs, actions_batch)
```

### 内存优化

```python
# 启用梯度检查点
config = {
    'gradient_checkpointing': True,
    'mixed_precision': True,
    'memory_efficient_attention': True
}

ai.train('zqsa01', config=config)
```

### 性能监控

```python
from engineai_legged_gym.profiling import PerformanceProfiler

with PerformanceProfiler() as profiler:
    # 运行训练代码
    ai.train('zqsa01', max_iterations=100)

# 生成性能报告
profiler.generate_report('performance_report.html')
```

---

**更多API详细信息请参考源码文档和类型提示。**