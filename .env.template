# EngineAI Legged Gym Environment Variables Template
# Copy this file to .env and modify the values as needed

# Basic Paths
ENGINEAI_ROOT=/path/to/engineai-legged-gym
ISAACGYM_ROOT=/path/to/isaacgym
LEGGED_GYM_ROOT_DIR=/path/to/engineai-legged-gym

# CUDA Configuration
CUDA_VISIBLE_DEVICES=0
CUDA_LAUNCH_BLOCKING=0
PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512

# Zenoh Configuration
ZENOH_CONFIG_PATH=./zenoh_services/config/
ZENOH_LOG_LEVEL=info
ZENOH_ROUTER_ENDPOINT=tcp/127.0.0.1:7447

# Training Configuration
ISAAC_GYM_ROOT_PATH=${ISAACGYM_ROOT}
WANDB_API_KEY=your_wandb_key_here  # Optional, for experiment tracking
TENSORBOARD_LOG_DIR=./logs/tensorboard

# Web Services Configuration
REACT_APP_API_BASE_URL=http://localhost:8000
NODE_ENV=production
WEB_GUI_PORT=3000
WEBSOCKET_PORT=8765

# Logging Configuration
LOG_LEVEL=INFO
LOG_DIR=./logs
ENABLE_FILE_LOGGING=true

# Performance Configuration
OMP_NUM_THREADS=8
MKL_NUM_THREADS=8
NUMBA_NUM_THREADS=8

# Development Configuration (optional)
PYTHONPATH=${ENGINEAI_ROOT}:${ISAACGYM_ROOT}/python
PYTHONDONTWRITEBYTECODE=1
PYTHONUNBUFFERED=1

# Security (if needed)
API_SECRET_KEY=your_secret_key_here
ENABLE_AUTHENTICATION=false

# Database (if using persistent storage)
DATABASE_URL=sqlite:///./data/engineai.db

# Monitoring (optional)
PROMETHEUS_PORT=9090
GRAFANA_PORT=3001
ENABLE_METRICS_EXPORT=false
