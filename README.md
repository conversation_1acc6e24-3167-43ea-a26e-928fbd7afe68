# EngineAI Legged Gym

[![License](https://img.shields.io/badge/License-BSD%203--Clause-blue.svg)](LICENSE)
[![Python](https://img.shields.io/badge/Python-3.8%2B-blue.svg)](https://www.python.org/)
[![<PERSON> Gym](https://img.shields.io/badge/Isaac%20Gym-Preview%204-green.svg)](https://developer.nvidia.com/isaac-gym)
[![Zenoh](https://img.shields.io/badge/Zenoh-0.10%2B-orange.svg)](https://zenoh.io/)

**A comprehensive reinforcement learning training system for legged robots with distributed architecture and web-based monitoring.**

This repository provides a complete solution for training legged robots using NVIDIA's Isaac Gym, featuring:
- 🤖 **Advanced RL Training**: PPO-based training with sim-to-real transfer capabilities
- 🌐 **Distributed Architecture**: Zenoh-based microservices for scalable deployment
- 📊 **Web Interface**: Modern React-based GUI for training monitoring and control
- 🔄 **Complete Pipeline**: From simulation training to real robot deployment
- 📈 **Performance Monitoring**: Real-time metrics and system health tracking

## 🎯 Key Features

- **Reinforcement Learning**: State-of-the-art PPO implementation with RSL-RL
- **Physics Simulation**: High-performance Isaac Gym integration
- **Distributed Services**: Zenoh-powered microservice architecture
- **Web Dashboard**: Real-time training visualization and control
- **Sim-to-Real**: Complete pipeline from simulation to robot deployment
- **Model Export**: Support for PyTorch (.pt) and ONNX (.onnx) formats
- **Terrain Generation**: Advanced terrain curriculum learning
- **Domain Randomization**: Comprehensive randomization for robust policies

---

## 📋 Table of Contents

- [Installation](#installation)
- [Quick Start](#quick-start)
- [System Architecture](#system-architecture)
- [Usage](#usage)
- [Configuration](#configuration)
- [API Reference](#api-reference)
- [Contributing](#contributing)
- [License](#license)

---

**Maintainer**: EngineAI Team
**Organization**: EngineAI Robot, China
**Website**: https://www.engineai.com.cn/
**Contact**: <EMAIL>

---

## 🚀 Quick Start

### Prerequisites

- **OS**: Ubuntu 18.04/20.04/22.04 LTS (recommended)
- **Python**: 3.8-3.10
- **CUDA**: 11.8+ (for GPU training)
- **GPU**: NVIDIA RTX 3070+ (recommended)
- **RAM**: 16GB+ (32GB recommended)

### Installation

#### 1. Clone Repository
```bash
git clone https://github.com/engineai-robotics/engineai_legged_gym.git
cd engineai_legged_gym
```

#### 2. Create Virtual Environment
```bash
# Using conda (recommended)
conda create -n engineai python=3.9
conda activate engineai

# Or using venv
python3.9 -m venv venv
source venv/bin/activate
```

#### 3. Install Dependencies
```bash
# Install PyTorch with CUDA support
pip install torch==1.13.1+cu117 torchvision==0.14.1+cu117 torchaudio==0.13.1 --extra-index-url https://download.pytorch.org/whl/cu117

# Install project dependencies
pip install -r requirements.txt

# Install in development mode
pip install -e .
```

#### 4. Install Isaac Gym
```bash
# Download Isaac Gym Preview 4 from NVIDIA
# https://developer.nvidia.com/isaac-gym
tar -xf IsaacGym_Preview_4_Package.tar.gz
cd isaacgym/python && pip install -e .

# Test installation
cd examples && python 1080_balls_of_solitude.py
```

#### 5. Install RSL-RL
```bash
cd rsl_rl && pip install -e .
```

#### 6. Setup Web Interface
```bash
cd zenoh_services/web_gui
npm install
npm run build
```

### Verification
```bash
# Run installation verification
python demo_services.py --verify-only

# Test training environment
python legged_gym/tests/test_env.py --task=zqsa01
```

---

## 🏗️ System Architecture

The system follows a distributed microservice architecture powered by Zenoh:

```
┌─────────────────────────────────────────────────────────────────┐
│                        Web Interface                            │
│                    (React + TypeScript)                        │
└─────────────────────┬───────────────────────────────────────────┘
                      │ WebSocket
┌─────────────────────▼───────────────────────────────────────────┐
│                   Web Backend                                   │
│              (Python + WebSockets)                             │
└─────────────────────┬───────────────────────────────────────────┘
                      │ Zenoh Topics
┌─────────────────────▼───────────────────────────────────────────┐
│                  Zenoh Router                                   │
│              (Message Distribution)                            │
└─┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────────┘
  │         │         │         │         │         │
  ▼         ▼         ▼         ▼         ▼         ▼
┌───────┐ ┌───────┐ ┌───────┐ ┌───────┐ ┌───────┐ ┌───────────┐
│Training│ │Simula-│ │Deploy-│ │Config │ │Monitor│ │   Play    │
│Service │ │tion   │ │ment   │ │Service│ │Service│ │ Service   │
│       │ │Service│ │Service│ │       │ │       │ │           │
└───┬───┘ └───┬───┘ └───┬───┘ └───────┘ └───────┘ └───────────┘
    │         │         │
    ▼         ▼         ▼
┌───────┐ ┌───────┐ ┌───────┐
│Isaac  │ │Model  │ │ONNX   │
│Gym    │ │Export │ │Export │
│       │ │       │ │       │
└───────┘ └───────┘ └───────┘
```

### Core Components

1. **Training Service**: Manages RL training with Isaac Gym
2. **Simulation Service**: Handles environment simulation and rendering
3. **Deployment Service**: Manages model export and deployment
4. **Configuration Service**: Centralized configuration management
5. **Monitoring Service**: System health and performance tracking
6. **Web Backend**: WebSocket bridge for frontend communication
7. **Web Interface**: React-based dashboard for system control

### Communication Flow

- **Zenoh Topics**: Distributed pub/sub messaging
- **WebSocket**: Real-time web interface updates
- **REST API**: Configuration and control endpoints
- **File System**: Model and log storage

---

## 📖 Usage

### 🎮 Quick Start Commands

#### Start All Services
```bash
# Start the complete system
python demo_services.py

# Or start individual services
python start_training_service.py
python start_web_backend.py
```

#### Web Interface
```bash
# Access the web dashboard
open http://localhost:3000
```

### 🏋️ Training

#### Basic Training
```bash
# Train ZQSA01 robot
python legged_gym/scripts/train.py --task=zqsa01

# Train with custom parameters
python legged_gym/scripts/train.py --task=zqsa01 \
    --num_envs=4096 \
    --max_iterations=1500 \
    --headless
```

#### Advanced Training Options
```bash
# Resume training from checkpoint
python legged_gym/scripts/train.py --task=zqsa01 --resume

# Custom experiment configuration
python legged_gym/scripts/train.py --task=zqsa01 \
    --experiment_name=my_experiment \
    --run_name=test_run \
    --seed=42
```

#### Training Parameters
- `--task`: Environment task name (e.g., zqsa01, anymal_c_flat)
- `--num_envs`: Number of parallel environments (default: 4096)
- `--max_iterations`: Maximum training iterations (default: 1500)
- `--headless`: Run without rendering for better performance
- `--sim_device`: Simulation device (cuda/cpu)
- `--rl_device`: RL computation device (cuda/cpu)
- `--resume`: Resume from last checkpoint
- `--seed`: Random seed for reproducibility

### 🎯 Policy Evaluation

#### Play Trained Policy
```bash
# Play the latest trained policy
python legged_gym/scripts/play.py --task=zqsa01

# Play specific checkpoint
python legged_gym/scripts/play.py --task=zqsa01 \
    --load_run=0 \
    --checkpoint=1000
```

### 🔄 Model Export and Deployment

#### Export to ONNX (Sim2Real)
```bash
# Export policy to ONNX format
python sim2real_deploy/export_onnx_policy.py

# Generated file: sim2real_deploy/zqsa01_policy.onnx
```

#### Sim2Sim Testing
```bash
# Test policy in simulation
python legged_gym/scripts/sim2sim_zqsa01.py \
    --load_model logs/zqsa01_ppo/0_exported/policies/policy_1.pt
```

### 📊 Monitoring and Visualization

#### TensorBoard
```bash
# View training metrics
tensorboard --logdir=logs/

# Access at http://localhost:6006
```

#### System Monitoring
```bash
# Monitor system performance
python scripts/monitor_performance.py
```

---

## ⚙️ Configuration

### Environment Variables
Copy `.env.template` to `.env` and configure:
```bash
cp .env.template .env
# Edit .env with your specific paths and settings
```

### System Configuration
Main configuration file: `config/system_config.yaml`

```yaml
# Training settings
training:
  algorithm: "ppo"
  device: "cuda"
  num_envs: 4096
  max_iterations: 1500

# Simulation settings
simulation:
  headless: false
  physics_engine: "physx"
  dt: 0.0083  # 120 Hz

# Web interface
web_gui:
  host: "0.0.0.0"
  port: 3000
```

### Robot Configuration
Robot-specific configs in `legged_gym/envs/{robot_name}/{robot_name}_config.py`:

```python
class ZQSA01Config(LeggedRobotConfig):
    class env:
        num_envs = 4096
        episode_length_s = 20

    class terrain:
        mesh_type = 'trimesh'
        curriculum = True

    class rewards:
        tracking_sigma = 0.25
        base_height_target = 1.0
```

---

## 🔌 API Reference

### Zenoh Topics

#### Training Service
- `/engineai/training/start` - Start training
- `/engineai/training/stop` - Stop training
- `/engineai/training/status` - Training status
- `/engineai/training/metrics` - Training metrics

#### Simulation Service
- `/engineai/simulation/reset` - Reset simulation
- `/engineai/simulation/step` - Simulation step
- `/engineai/simulation/render` - Rendering control

#### Deployment Service
- `/engineai/deployment/export` - Export model
- `/engineai/deployment/models` - Available models
- `/engineai/deployment/deploy` - Deploy to robot

### WebSocket API

#### Connection
```javascript
const ws = new WebSocket('ws://localhost:8765');
```

#### Message Format
```json
{
  "type": "training_status",
  "data": {
    "iteration": 1000,
    "reward": 15.2,
    "status": "running"
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### REST API Endpoints

#### Training Control
- `POST /api/training/start` - Start training
- `POST /api/training/stop` - Stop training
- `GET /api/training/status` - Get status
- `GET /api/training/logs` - Get logs

#### Model Management
- `GET /api/models` - List models
- `POST /api/models/export` - Export model
- `DELETE /api/models/{id}` - Delete model

---

## 🛠️ Development

### Adding New Environments

1. **Create Environment Structure**
   ```bash
   mkdir legged_gym/envs/my_robot
   touch legged_gym/envs/my_robot/__init__.py
   touch legged_gym/envs/my_robot/my_robot.py
   touch legged_gym/envs/my_robot/my_robot_config.py
   ```

2. **Implement Configuration**
   ```python
   # my_robot_config.py
   from legged_gym.envs.base.legged_robot_config import LeggedRobotCfg, LeggedRobotCfgPPO

   class MyRobotCfg(LeggedRobotCfg):
       class env:
           num_envs = 4096
           episode_length_s = 20

       class asset:
           file = '{LEGGED_GYM_ROOT_DIR}/resources/robots/my_robot/urdf/my_robot.urdf'
           name = "my_robot"
   ```

3. **Register Environment**
   ```python
   # legged_gym/envs/__init__.py
   from .my_robot.my_robot import MyRobot
   from .my_robot.my_robot_config import MyRobotCfg, MyRobotCfgPPO

   task_registry.register("my_robot", MyRobot, MyRobotCfg, MyRobotCfgPPO)
   ```

### Code Quality

```bash
# Format code
black .
isort .

# Lint code
flake8 .
mypy .

# Run tests
pytest tests/
```

### Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

---

## 🐛 Troubleshooting

### Common Issues

#### Isaac Gym Installation
```bash
# Missing libpython
sudo apt install python3.9-dev

# Library path issues
export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/path/to/conda/envs/engineai/lib
```

#### CUDA Issues
```bash
# CUDA out of memory
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:256

# Check CUDA availability
python -c "import torch; print(torch.cuda.is_available())"
```

#### Zenoh Connection Issues
```bash
# Check port availability
netstat -tulpn | grep 7447

# Kill conflicting processes
sudo fuser -k 7447/tcp
```

### Known Limitations

1. **Contact Forces**: GPU triangle mesh terrain contact forces may be unreliable
2. **Memory Usage**: Large environments require significant GPU memory
3. **Platform Support**: Full functionality requires Linux with NVIDIA GPU

### Getting Help

- 📖 **Documentation**: Check [docs/](docs/) directory
- 🐛 **Issues**: Report bugs on [GitHub Issues](https://github.com/engineai-robotics/engineai_legged_gym/issues)
- 💬 **Discussions**: Join [GitHub Discussions](https://github.com/engineai-robotics/engineai_legged_gym/discussions)
- 📧 **Contact**: <EMAIL>

---

## 📄 License

This project is licensed under the BSD 3-Clause License - see the [LICENSE](LICENSE) file for details.

---

## 🙏 Acknowledgments

- [NVIDIA Isaac Gym](https://developer.nvidia.com/isaac-gym) for the physics simulation
- [RSL-RL](https://github.com/leggedrobotics/rsl_rl) for the RL algorithms
- [Eclipse Zenoh](https://zenoh.io/) for distributed messaging
- [Legged Gym](https://github.com/leggedrobotics/legged_gym) for the original framework

---

## 📊 Project Status

- ✅ **Core Training**: Fully functional
- ✅ **Web Interface**: Complete with monitoring
- ✅ **Distributed Services**: Zenoh integration working
- ✅ **Model Export**: ONNX and PyTorch support
- 🔄 **Documentation**: Continuously improving
- 🔄 **Testing**: Expanding test coverage

**Last Updated**: 2024-07-24
